# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [1.11.76](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.11.76) (2025-08-15)


### Bug Fixes

* api base修改 ([56c6510](https://code.devops.xiaohongshu.com/fe/promotion/commits/56c65106b09f24422d1f43eb000d4b145c76f518))





## [1.11.73](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.11.73) (2025-08-11)


### Features

* 策略可视化 ([55da2e8](https://code.devops.xiaohongshu.com/fe/promotion/commits/55da2e8d1e103158676029b16145ea822cf0d27a))





## [1.11.70](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.11.70) (2025-08-05)


### Features

* 白名单管理页面 ([9751eb8](https://code.devops.xiaohongshu.com/fe/promotion/commits/9751eb835ffba85e918c73519590def485bf7c2b))





## [1.11.67](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.11.67) (2025-07-31)


### Features

* 阶段库存配置项优化 ([191472d](https://code.devops.xiaohongshu.com/fe/promotion/commits/191472d15f7669f5fdcee370540124970088d1d9))





## [1.11.65](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.11.65) (2025-07-29)


### Features

* 增加玩法组件列表 ([4ab381c](https://code.devops.xiaohongshu.com/fe/promotion/commits/4ab381c173d16c8b85edcfcf15e9c3074547df84))





## [1.11.64](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.11.64) (2025-07-25)


### Bug Fixes

* params ([86b1ddd](https://code.devops.xiaohongshu.com/fe/promotion/commits/86b1ddd9c4c6e306d44da588e15361ea250c48dc))





## [1.11.63](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.11.63) (2025-07-25)


### Bug Fixes

* zhongcao task ([505bb87](https://code.devops.xiaohongshu.com/fe/promotion/commits/505bb879dc94e095894305b8f20353703dc4fc4a))





## [1.11.62](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.11.62) (2025-07-23)


### Bug Fixes

* ui ([1a93646](https://code.devops.xiaohongshu.com/fe/promotion/commits/1a93646641d6acc589dcc18dab4592a5be2eda21))





## [1.11.61](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.11.61) (2025-07-15)


### Features

* 统一投放平台 ([e6e9459](https://code.devops.xiaohongshu.com/fe/promotion/commits/e6e9459e46bd664e2bfb35905782780cdbe2c30b))





## [1.11.60](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.11.60) (2025-07-15)


### Features

* 阶段库存 ([0102ee4](https://code.devops.xiaohongshu.com/fe/promotion/commits/0102ee4e5e06edc3d4226a4111e2e52cca231188))





## [1.11.41](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.11.41) (2025-04-25)


### Bug Fixes

* bugfix ([4a76852](https://code.devops.xiaohongshu.com/fe/promotion/commits/4a768521c00efe8d66bb0950c9bc7dce0507df36))





## [1.11.39](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.11.39) (2025-04-23)


### Features

* 增加境外配置 ([362f940](https://code.devops.xiaohongshu.com/fe/promotion/commits/362f94010b927b3c2a63a6879ae58724791a6ba5))





## [1.11.36](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.11.36) (2025-04-16)


### Features

* 任务筛选 ([e482263](https://code.devops.xiaohongshu.com/fe/promotion/commits/e482263317aa162a87aea32286801bc41d3c6a71))





## [1.11.28](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.11.28) (2025-03-14)


### Bug Fixes

* 秋上新券 ([9a0c4af](https://code.devops.xiaohongshu.com/fe/promotion/commits/9a0c4af14d83e22ff3514f2373c7f502c300d517))


### Features

* patch ([e0989de](https://code.devops.xiaohongshu.com/fe/promotion/commits/e0989ded2b14e49f62b02a4b99e6aa5e8b07124f))





## [1.11.26](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.11.26) (2025-03-12)


### Features

* 任务平台 ([ec394a7](https://code.devops.xiaohongshu.com/fe/promotion/commits/ec394a7c507d223e8fd56ad91a91a45fec0e1d8f))





## [1.11.24](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.11.24) (2025-03-10)


### Features

* 增加笔记来源项 ([d453291](https://code.devops.xiaohongshu.com/fe/promotion/commits/d45329147536c59385c005291d80378bbeafe7a8))





## [1.11.21](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.11.21) (2025-03-06)


### Features

* RedShop多语言氛围配置 ([cea0135](https://code.devops.xiaohongshu.com/fe/promotion/commits/cea0135297b5a7b0aad18280b9cc7271d191be4e))





## [1.11.17](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.11.17) (2025-02-25)


### Features

* 策略总数 ([029e6e4](https://code.devops.xiaohongshu.com/fe/promotion/commits/029e6e41ae3d54cac1d511f5b6511132064c7312))





## [1.11.15](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.11.15) (2025-02-24)


### Features

* 策略平台白名单 ([b88dfa4](https://code.devops.xiaohongshu.com/fe/promotion/commits/b88dfa4dcc8201e08102d45e364c25d5a885922d))





## [1.11.12](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.11.12) (2025-02-12)


### Features

* 增加搜索功能 ([6e7da21](https://code.devops.xiaohongshu.com/fe/promotion/commits/6e7da21b7d776df1380092514f7afbfdf2813c6f))





## [1.11.10](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.11.10) (2025-02-10)


### Bug Fixes

* onchange ([42ad888](https://code.devops.xiaohongshu.com/fe/promotion/commits/42ad8889a55b1078e367dd882849a7a044c60632))


### Features

* 复制标题优化 ([881890a](https://code.devops.xiaohongshu.com/fe/promotion/commits/881890a79ae30641da9d1d8e7825650d93f17f41))
* 校验实验组 ([b269e21](https://code.devops.xiaohongshu.com/fe/promotion/commits/b269e21f390ba68cadf1048317e2c6fbf05d5b50))





## [1.11.5](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.11.5) (2024-12-10)


### Bug Fixes

* 换肤开关 ([452ae8e](https://code.devops.xiaohongshu.com/fe/promotion/commits/452ae8e735e1a24af851e6a4c2c755f34675b2d4))





## [1.11.2](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.11.2) (2024-11-29)


### Bug Fixes

* 修复规则复制问题 ([cb0bc4f](https://code.devops.xiaohongshu.com/fe/promotion/commits/cb0bc4fa4430c32892356ce5879b554985de59f4))





## [1.11.1](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.11.1) (2024-11-27)


### Bug Fixes

* 条件规则修复 ([734fe25](https://code.devops.xiaohongshu.com/fe/promotion/commits/734fe25f927d946b27c2614a0f58a5bb519dae0e))





# [1.11.0](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.11.0) (2024-11-27)


### Features

* 条件详情加入limitTimes ([9a6681f](https://code.devops.xiaohongshu.com/fe/promotion/commits/9a6681fc27484fc58e02009b25c2ef7b6a4ac957))





## [1.10.2](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.10.2) (2024-11-25)


### Features

* support new experiment ([8b93e53](https://code.devops.xiaohongshu.com/fe/promotion/commits/8b93e53aaf3eb7e9650a9a092c13c34b3e6f3044))





## [1.10.1](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.10.1) (2024-11-21)


### Bug Fixes

* excel ([4a80f7a](https://code.devops.xiaohongshu.com/fe/promotion/commits/4a80f7af14155ed613443d5f63aa98fbbd4fd310))





# [1.10.0](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.10.0) (2024-11-19)


### Features

* 修改权益池模版链接 ([e35e067](https://code.devops.xiaohongshu.com/fe/promotion/commits/e35e0674c652d58b8029e35eab51f777d12771a5))





## [1.9.68](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.68) (2024-11-14)


### Features

* active menu test ([b5e0077](https://code.devops.xiaohongshu.com/fe/promotion/commits/b5e00772e125dd5c4fd3a47e99980e00876b60ab))





## [1.9.67](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.67) (2024-11-14)


### Features

* support crowd ([7a8f1fb](https://code.devops.xiaohongshu.com/fe/promotion/commits/7a8f1fb316cc99249db37eaea8af94da0b0e4ecd))





## [1.9.66](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.66) (2024-11-08)

**Note:** Version bump only for package loki-promotion





## [1.9.64](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.64) (2024-11-04)


### Features

* support coupon status judge ([bbbe4c4](https://code.devops.xiaohongshu.com/fe/promotion/commits/bbbe4c47b434cfab8a2738e370723860d44f88a6))





## [1.9.60](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.60) (2024-10-30)


### Features

* 预览表单优化 ([ae4e235](https://code.devops.xiaohongshu.com/fe/promotion/commits/ae4e235e6b35fa9e1e850c94dac7e4ea6d2ff10f))





## [1.9.59](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.59) (2024-10-25)


### Bug Fixes

* add priority diff ([cb1a4cd](https://code.devops.xiaohongshu.com/fe/promotion/commits/cb1a4cd435596ce068bd1c54ab26be47ae7ffd00))





## [1.9.58](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.58) (2024-10-18)


### Features

* 混资券 ([b21395b](https://code.devops.xiaohongshu.com/fe/promotion/commits/b21395b677dbc0e3f425ed3525c7af10b1211678))





## [1.9.57](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.57) (2024-10-11)


### Bug Fixes

* 新增item优先级计算逻辑 ([f207427](https://code.devops.xiaohongshu.com/fe/promotion/commits/f207427829288ab632e0940c5bdbbf0a7a62b54a))





## [1.9.56](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.56) (2024-10-11)


### Features

* 频控组负反馈 ([0658fa6](https://code.devops.xiaohongshu.com/fe/promotion/commits/0658fa6641eed069ac6c30c71e6e64b92c11ae8f))





## [1.9.55](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.55) (2024-10-10)


### Features

* 添加时间范围组件 ([26d0f7d](https://code.devops.xiaohongshu.com/fe/promotion/commits/26d0f7d09646dcc710bdd2e9393c7811d81bc312))





## [1.9.54](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.54) (2024-09-26)


### Bug Fixes

* extra节点 ([87b81ca](https://code.devops.xiaohongshu.com/fe/promotion/commits/87b81ca0a6af729ee39744968ce6bb96008dab68))





## [1.9.53](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.53) (2024-09-26)


### Bug Fixes

* 编辑调整 ([eb88e37](https://code.devops.xiaohongshu.com/fe/promotion/commits/eb88e37db78916d006c4b5277aa948a0c952db6e))





## [1.9.52](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.52) (2024-09-26)


### Bug Fixes

* 编辑调整 ([9c14385](https://code.devops.xiaohongshu.com/fe/promotion/commits/9c14385e808cdfdef90b3e8577b8a391dc19863e))





## [1.9.51](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.51) (2024-09-26)


### Features

* dynamic config ([fea5a85](https://code.devops.xiaohongshu.com/fe/promotion/commits/fea5a8567c4cc94d92f378225d280ab0834ab93d))





## [1.9.50](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.50) (2024-09-26)

**Note:** Version bump only for package loki-promotion





## [1.9.49](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.49) (2024-09-25)


### Features

* excel权益池 ([493cb9c](https://code.devops.xiaohongshu.com/fe/promotion/commits/493cb9c0063eb92f37a0870cf05c0aec51213922))





## [1.9.48](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.48) (2024-09-04)


### Features

* 个人页走http ([0e3f4b5](https://code.devops.xiaohongshu.com/fe/promotion/commits/0e3f4b582ec9bf7a9b0388ffa03b8d12394ac6a5))
* 新增券和if组件 ([27389e0](https://code.devops.xiaohongshu.com/fe/promotion/commits/27389e0cb7f96e53756dcfa2fd8da93de6e0a6d4))





## [1.9.47](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.47) (2024-09-03)


### Features

* 新增标签枚举 ([7f0a0aa](https://code.devops.xiaohongshu.com/fe/promotion/commits/7f0a0aa5c9cd4a8e838b62fa75cde7710be1d942))
* force build ([c14ef12](https://code.devops.xiaohongshu.com/fe/promotion/commits/c14ef124afb46ab90e2cea50f75b37b29fa33a0c))





## [1.9.46](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.46) (2024-08-30)


### Features

* 文案模版 ([954c314](https://code.devops.xiaohongshu.com/fe/promotion/commits/954c3145c7ac3ac39c3c6e20f3bd46586367f922))





## [1.9.45](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.45) (2024-08-28)


### Bug Fixes

* 超多张券权益浮层的展示 ([d361a85](https://code.devops.xiaohongshu.com/fe/promotion/commits/d361a8557845eeea1cc6d05be616dbad41ce9e67))





## [1.9.44](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.44) (2024-08-24)


### Features

* 新增策略标签 ([16cddc4](https://code.devops.xiaohongshu.com/fe/promotion/commits/16cddc45e40ac2875db83babf4d8bd4526b969d5))





## [1.9.43](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.43) (2024-08-16)


### Bug Fixes

* 修改频控获取策略列表接口 ([e4ee3a4](https://code.devops.xiaohongshu.com/fe/promotion/commits/e4ee3a46c2372de8a08a91eb74cffd5d1d166ddc))





## [1.9.42](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.42) (2024-08-15)


### Features

* 创建策略组加实验 ([e8a6756](https://code.devops.xiaohongshu.com/fe/promotion/commits/e8a6756362dfcf27a8795cf8c821d9488591209c))





## [1.9.41](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.41) (2024-08-13)


### Bug Fixes

* 容器规则编辑卡顿 ([7ba3ff2](https://code.devops.xiaohongshu.com/fe/promotion/commits/7ba3ff261e6d49f7382d54b15a6d4b1a30147a87))





## [1.9.39](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.39) (2024-07-31)


### Features

* 修改数据来源 ([dab4ebb](https://code.devops.xiaohongshu.com/fe/promotion/commits/dab4ebb8607961475735757f4cf59418614d459c))





## [1.9.38](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.38) (2024-07-30)


### Features

* 修复氛围中心路径名称重复问题 ([5119957](https://code.devops.xiaohongshu.com/fe/promotion/commits/511995728b52cfeb7891383986e81317f3415ee0))





## [1.9.34](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.34) (2024-07-23)


### Bug Fixes

* new tag ([65b0b3e](https://code.devops.xiaohongshu.com/fe/promotion/commits/65b0b3e670694640ff0b60f1bbcf9c7f08c34c3c))





## [1.9.33](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.33) (2024-07-23)


### Bug Fixes

* 增加电商直播间选项，修改枚举值 ([8178715](https://code.devops.xiaohongshu.com/fe/promotion/commits/817871530e2c48fa7d01b7fbba8f1a9d8e1e615e))





## [1.9.32](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.32) (2024-07-22)


### Features

* 新增 compass ([df00a0e](https://code.devops.xiaohongshu.com/fe/promotion/commits/df00a0ee47ec58e3a360d89700717f4cc368f1c9))





## [1.9.28](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.28) (2024-07-18)


### Bug Fixes

* 直播人群增加枚举 ([5956518](https://code.devops.xiaohongshu.com/fe/promotion/commits/595651847b03ad8deacc14cf3b44731401d61b27))





## [1.9.27](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.27) (2024-07-18)


### Features

* add tile env ([0f7bb0b](https://code.devops.xiaohongshu.com/fe/promotion/commits/0f7bb0bc692cd703550c6dca9038d5bec60de51c))





## [1.9.26](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.26) (2024-07-16)


### Bug Fixes

* 实验参数子实验判断 ([408f616](https://code.devops.xiaohongshu.com/fe/promotion/commits/408f616707a50f4799200c1658c6e5be13ecdf81))


### Features

* 老策略组支持删除，频控节点符合规则可编辑 ([0300e1a](https://code.devops.xiaohongshu.com/fe/promotion/commits/0300e1a3b59732980f2e71f6547f8bdfbf2404bb))





## [1.9.25](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.25) (2024-07-12)


### Bug Fixes

* 暂时隐藏 tile ([7e5e390](https://code.devops.xiaohongshu.com/fe/promotion/commits/7e5e390c94edd9155a6d785b6970c676cbdea82f))





## [1.9.24](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.24) (2024-07-11)


### Bug Fixes

* fix http config ([45c47d8](https://code.devops.xiaohongshu.com/fe/promotion/commits/45c47d817b02c81d55538ad46241c4b1fa441447))





## [1.9.23](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.23) (2024-07-11)


### Features

* 强行打包 ([1d81076](https://code.devops.xiaohongshu.com/fe/promotion/commits/1d81076ffa457a876436e886853e1dc04a704e61))
* update tile selector ([3e1af45](https://code.devops.xiaohongshu.com/fe/promotion/commits/3e1af45e826053a864f316b8c3c4999a2590b469))





## [1.9.22](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.22) (2024-07-11)


### Features

* 新链路支持更多权益类型 ([d41b48e](https://code.devops.xiaohongshu.com/fe/promotion/commits/d41b48e7693615cf228b5a70a7c782f0cbd03bc6))





## [1.9.20](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.20) (2024-07-09)


### Bug Fixes

* 修复 bizconfig ([6708af1](https://code.devops.xiaohongshu.com/fe/promotion/commits/6708af1a4ba14fe4a2e85980040f5b552b8276d8))





## [1.9.19](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.19) (2024-07-08)


### Features

* 增加暂停中状态 ([fcd36d8](https://code.devops.xiaohongshu.com/fe/promotion/commits/fcd36d822fa6b1ce1e5b26d02cf90e38da7f39d5))





## [1.9.18](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.18) (2024-07-08)


### Bug Fixes

* new tag ([cb8c632](https://code.devops.xiaohongshu.com/fe/promotion/commits/cb8c6321b65e1fc6e088f9f191b95c0a5b718207))
* showTypeV1 ([304108c](https://code.devops.xiaohongshu.com/fe/promotion/commits/304108c7d6d6cd8a8d2818bf9db7c1c0a9580993))





## [1.9.16](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.16) (2024-07-04)


### Features

* 投放计划 活动中心试验参数 ([ff8abab](https://code.devops.xiaohongshu.com/fe/promotion/commits/ff8abab8808b9046ca831a9ae252c6e733e8e358))





## [1.9.15](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.15) (2024-07-03)


### Features

* 添加错误校验 ([95eb0a5](https://code.devops.xiaohongshu.com/fe/promotion/commits/95eb0a5e6b0a5579a58f162b59f7e5339858f00a))





## [1.9.14](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.14) (2024-07-03)


### Bug Fixes

* 默认文案fix ([3a6a9c4](https://code.devops.xiaohongshu.com/fe/promotion/commits/3a6a9c4048a6c68f4fb415071e485b590a200c9c))





## [1.9.13](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.13) (2024-06-28)


### Features

* 盒子直播间文案配置化 ([72b9750](https://code.devops.xiaohongshu.com/fe/promotion/commits/72b97505eadbf16639f96f13e7425eb205fff069))





## [1.9.11](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.11) (2024-06-27)


### Features

* haspriority ([cdeb002](https://code.devops.xiaohongshu.com/fe/promotion/commits/cdeb0028f5ee240c68d650a7ee0615443c09a88a))





## [1.9.10](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.10) (2024-06-27)


### Bug Fixes

* 优先级管理详情修复 ([221127d](https://code.devops.xiaohongshu.com/fe/promotion/commits/221127d54d1a7631cc948c6443ac78d7fe47e4ab))





## [1.9.8](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.8) (2024-06-26)


### Bug Fixes

* 已结束策略删除 ([1c8718d](https://code.devops.xiaohongshu.com/fe/promotion/commits/1c8718da317de7e9d7142ea9de2ef1d009d5ca51))





## [1.9.7](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.7) (2024-06-15)


### Features

* 新投放计划+策略配置升级 ([6cc1d6d](https://code.devops.xiaohongshu.com/fe/promotion/commits/6cc1d6df07730e5803b3d78296af1b32a70818d7))





## [1.9.6](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.6) (2024-06-14)


### Features

* 替换上传模板 ([e4bd98c](https://code.devops.xiaohongshu.com/fe/promotion/commits/e4bd98cdf55d6c19593f6d48bba4f865ad774a1a))
* 修改删除按钮位置 ([091d258](https://code.devops.xiaohongshu.com/fe/promotion/commits/091d258ff4d86ef99f1312326921f647ef50e7cc))





## [1.9.5](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.5) (2024-06-06)

**Note:** Version bump only for package loki-promotion





## [1.9.4](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.4) (2024-06-06)


### Features

* 活动列表重定向 ([119d3c6](https://code.devops.xiaohongshu.com/fe/promotion/commits/119d3c6c0cf620e467223c2b024902aaefd36f7a))





## [1.9.3](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.3) (2024-06-06)


### Features

* 修改数量; ([93c9006](https://code.devops.xiaohongshu.com/fe/promotion/commits/93c90066b5269e7c03035b59b4563f7e1771d1a9))





# [1.9.0](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.9.0) (2024-06-05)


### Features

* **dd:** 图片接入播控 ([939d060](https://code.devops.xiaohongshu.com/fe/promotion/commits/939d0608c4441480b660a8fe6b43b3141e2e231b))
* **dd:** 图片接入播控 ([ee9cd09](https://code.devops.xiaohongshu.com/fe/promotion/commits/ee9cd09acbc2e18a05a0119529e49064575e1a14))





## [1.8.175](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.175) (2024-06-04)


### Features

* redlabel审核重定向 ([144e93f](https://code.devops.xiaohongshu.com/fe/promotion/commits/144e93fc6c227f8bd7daa32c071d662cd924ddff))





## [1.8.173](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.173) (2024-05-15)


### Features

* 618膨胀换肤 ([e84bf84](https://code.devops.xiaohongshu.com/fe/promotion/commits/e84bf846cf4d59d68525976a8c3af3f47866208e))





## [1.8.171](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.171) (2024-05-11)


### Bug Fixes

* 去除实验必填校验 ([3c2cade](https://code.devops.xiaohongshu.com/fe/promotion/commits/3c2cadeac8f3d3738bd4b17bf4f47244252d407c))
* new tag ([c306337](https://code.devops.xiaohongshu.com/fe/promotion/commits/c30633765ff1331e0a47adb5b5a021d0938d997b))





## [1.8.168](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.168) (2024-04-28)

**Note:** Version bump only for package loki-promotion





## [1.8.166](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.166) (2024-04-23)


### Features

* 复制投放计划 ([a96fb8d](https://code.devops.xiaohongshu.com/fe/promotion/commits/a96fb8d83fef65f1a4abfb5a691cea2cd8b59222))





## [1.8.164](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.164) (2024-04-03)


### Features

* 策略营销投放计划 ([43e44b1](https://code.devops.xiaohongshu.com/fe/promotion/commits/43e44b1e4782655142515838b5dd44d26b8f623e))





## [1.8.159](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.159) (2024-03-21)


### Features

* 支持查看买手名单 ([3b35606](https://code.devops.xiaohongshu.com/fe/promotion/commits/3b35606a1d39d0761db26b9ef83b4638a4e7a4c7))





## [1.8.158](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.158) (2024-03-18)


### Features

* 新增活动配置 ([eec9e73](https://code.devops.xiaohongshu.com/fe/promotion/commits/eec9e73dfd4f4ce5f21916923bee266c70732d79))





## [1.8.157](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.157) (2024-03-18)


### Bug Fixes

* 修复活动中心配置不回填 ([87b9089](https://code.devops.xiaohongshu.com/fe/promotion/commits/87b9089cf8c426b725b959d0b7fc651a2bb0c194))





## [1.8.156](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.156) (2024-03-15)


### Features

* 增加跳转凑单页配置 ([daf5c27](https://code.devops.xiaohongshu.com/fe/promotion/commits/daf5c27d935d0974b16ab9a46966b7c4967d6108))





## [1.8.155](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.155) (2024-03-15)


### Features

* 新增行业拉新策略标签 ([dffebd2](https://code.devops.xiaohongshu.com/fe/promotion/commits/dffebd24a1e12f430717a69390afa878e6d760d7))





## [1.8.154](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.154) (2024-03-12)


### Features

* downline ecrm ([d7bcb10](https://code.devops.xiaohongshu.com/fe/promotion/commits/d7bcb105bddc8a8f5d3fbbc52a6f739d37601e2c))





## [1.8.153](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.153) (2024-03-11)


### Bug Fixes

* change to camel ([71342d1](https://code.devops.xiaohongshu.com/fe/promotion/commits/71342d13ff71f994572c9416507634e83e39434a))





## [1.8.152](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.152) (2024-03-11)


### Features

* change crowd api ([a1054b5](https://code.devops.xiaohongshu.com/fe/promotion/commits/a1054b5927d13f6d9393c597d7b16693876b052e))





## [1.8.145](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.145) (2024-02-22)


### Features

* **style:** lint ([f8ec7d1](https://code.devops.xiaohongshu.com/fe/promotion/commits/f8ec7d1bff342e448fbe6e6a7ee61da12e08e1b3))





## [1.8.142](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.142) (2024-01-16)


### Features

* 限制最大折扣金额 ([dc5d396](https://code.devops.xiaohongshu.com/fe/promotion/commits/dc5d3966537f4aef95a34f722d072b4edfc0dad5))





## [1.8.141](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.141) (2024-01-15)


### Bug Fixes

* 修复筛选分页问题 ([367ba95](https://code.devops.xiaohongshu.com/fe/promotion/commits/367ba9535abf35535359f7d4c6165f47a7926ae2))


### Features

* 投放系统支持分销计划商品搜索组件 ([8f5c010](https://code.devops.xiaohongshu.com/fe/promotion/commits/8f5c010e151a0c803d87c597236398ad9a647239))





## [1.8.138](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.138) (2024-01-10)


### Features

* add label ([616e2b1](https://code.devops.xiaohongshu.com/fe/promotion/commits/616e2b1526dac26211d889d6bd91f42c85508599))





## [1.8.137](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.137) (2024-01-08)


### Bug Fixes

* redlabel审核 ([3c6afbd](https://code.devops.xiaohongshu.com/fe/promotion/commits/3c6afbdd54631a30e5669dfb15475baef92fe787))





## [1.8.135](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.135) (2024-01-06)


### Bug Fixes

* word error ([3e4b4c5](https://code.devops.xiaohongshu.com/fe/promotion/commits/3e4b4c532c943e73b5454066bbc96ed1ab352359))





## [1.8.134](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.134) (2024-01-03)

**Note:** Version bump only for package loki-promotion





## [1.8.133](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.133) (2023-12-27)


### Features

* tag; ([c1560c5](https://code.devops.xiaohongshu.com/fe/promotion/commits/c1560c5b9f10da5b652d5235df60178950d1f6db))





## [1.8.132](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.132) (2023-12-27)


### Bug Fixes

* add 0 value ([c6af007](https://code.devops.xiaohongshu.com/fe/promotion/commits/c6af007dc140b77d38c65da722b4b14c13a2d7f0))





## [1.8.131](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.131) (2023-12-22)


### Features

* 氛围中心路由初始化 ([497cd41](https://code.devops.xiaohongshu.com/fe/promotion/commits/497cd414cf0df6d0b4dc9168ef076dbf8bc5dd55))





## [1.8.130](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.130) (2023-12-19)


### Bug Fixes

* add option in basic info ([3b1f2de](https://code.devops.xiaohongshu.com/fe/promotion/commits/3b1f2def35df7e6e80c222e35a81f63ceba9f8ce))





## [1.8.129](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.129) (2023-12-18)


### Bug Fixes

* 再改qa文案 ([36ddaf6](https://code.devops.xiaohongshu.com/fe/promotion/commits/36ddaf6f2aa91027e8e46a3dec5f50615af61259))





## [1.8.128](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.128) (2023-12-15)


### Features

* JsonTree的结构和结构列表字段支持按需展示配 ([dbe14b7](https://code.devops.xiaohongshu.com/fe/promotion/commits/dbe14b7a08da9edf1c3005a1fad57f36fb6d1b04))





## [1.8.127](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.127) (2023-12-14)


### Features

* condition rule list page ([74ace27](https://code.devops.xiaohongshu.com/fe/promotion/commits/74ace277c8a386937ad62047eb967aed67f7bd5c))





## [1.8.126](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.126) (2023-12-11)


### Bug Fixes

* enlarge pageSize ([af57675](https://code.devops.xiaohongshu.com/fe/promotion/commits/af576755cb60e2a2299042744551275d80b969f7))





## [1.8.125](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.125) (2023-12-08)


### Bug Fixes

* 逻辑冲突 ([78368ce](https://code.devops.xiaohongshu.com/fe/promotion/commits/78368ced38a53ef5166b860db577a8c260d8dcaf))





## [1.8.124](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.124) (2023-12-08)


### Bug Fixes

* 逻辑冲突 ([4494a76](https://code.devops.xiaohongshu.com/fe/promotion/commits/4494a76a412a3eac8675084ab6355f4742f137bf))





## [1.8.123](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.123) (2023-12-07)


### Bug Fixes

* add filter in frequency control ([7615e26](https://code.devops.xiaohongshu.com/fe/promotion/commits/7615e26c706d61e9cc58b1af5b28623a223418c9))





## [1.8.122](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.122) (2023-12-07)


### Bug Fixes

* 体验优化 ([e88d453](https://code.devops.xiaohongshu.com/fe/promotion/commits/e88d453d3c52a75d12d0100750595ccffb707416))





## [1.8.121](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.121) (2023-12-07)


### Bug Fixes

* 增加item搜索 ([cbe1701](https://code.devops.xiaohongshu.com/fe/promotion/commits/cbe1701204921aeecc3a90a9ae24ea46a7ad5a13))





## [1.8.120](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.120) (2023-12-07)


### Features

* 误入老活动审核页面给重定向至新页面 ([ee9f32a](https://code.devops.xiaohongshu.com/fe/promotion/commits/ee9f32ada7c164a937656e1c9b4b01c88aee10cc))
* add buyer upload ([45d2fea](https://code.devops.xiaohongshu.com/fe/promotion/commits/45d2fea8bdf6c1f0578e16c0a1e8b702e9b4f664))





## [1.8.119](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.119) (2023-12-02)


### Bug Fixes

* 节点配置 ([ee55729](https://code.devops.xiaohongshu.com/fe/promotion/commits/ee55729cb5c21d7560f30c9bbe561003d544b3fc))





## [1.8.118](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.118) (2023-11-29)


### Features

* **sort:** 增加枚举 ([47a8eb0](https://code.devops.xiaohongshu.com/fe/promotion/commits/47a8eb036358737852163f6bdf5e0a57631617f6))





## [1.8.117](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.117) (2023-11-29)


### Features

* Strength Condition Config ([0622fcc](https://code.devops.xiaohongshu.com/fe/promotion/commits/0622fcc4a2420dc1f40e13c0dc16bc42da40cc33))





## [1.8.116](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.116) (2023-11-23)


### Bug Fixes

* 活动详情优化 ([31f6a93](https://code.devops.xiaohongshu.com/fe/promotion/commits/31f6a939440d114bca6b8975f8145f8b273e4606))





## [1.8.115](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.115) (2023-11-23)


### Bug Fixes

* 膨胀前权益 ([9033e69](https://code.devops.xiaohongshu.com/fe/promotion/commits/9033e69078b3fc13bea6b91f27e01b7583ce71a3))





## [1.8.114](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.114) (2023-11-21)


### Features

* 增加活动配置信息 ([95a831d](https://code.devops.xiaohongshu.com/fe/promotion/commits/95a831d67224141782e3f66125db5345d2e6ff8b))





## [1.8.113](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.113) (2023-11-20)


### Bug Fixes

* 补全sit活动模版 ([476f72e](https://code.devops.xiaohongshu.com/fe/promotion/commits/476f72ee0230874c9e6309c6e9abe9ffb59c0726))





## [1.8.112](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.112) (2023-11-16)

**Note:** Version bump only for package loki-promotion





## [1.8.111](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.111) (2023-11-09)


### Features

* 新增ArkNps配置平台 ([e62508f](https://code.devops.xiaohongshu.com/fe/promotion/commits/e62508f78569cd6727a097d5713de620d2578ee5))





## [1.8.110](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.110) (2023-11-08)


### Bug Fixes

* lint ([6d3f84f](https://code.devops.xiaohongshu.com/fe/promotion/commits/6d3f84f4fdb4e04cbddab2644e12ba3675b456c4))





## [1.8.109](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.109) (2023-11-07)


### Bug Fixes

* 修复最大值最小值问题 & 字符串表达式执行问题 ([d969628](https://code.devops.xiaohongshu.com/fe/promotion/commits/d96962838200d84e211e622f6f52bd6d90bcead6))





## [1.8.108](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.108) (2023-11-07)


### Features

* 接push ([7410b74](https://code.devops.xiaohongshu.com/fe/promotion/commits/7410b74ac081a2b052582d2cca5bf232fcc13bb9))





## [1.8.107](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.107) (2023-11-06)


### Features

* 新增协作人 ([3d24285](https://code.devops.xiaohongshu.com/fe/promotion/commits/3d24285feceb9310c18582e7413b99f672822178))





## [1.8.106](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.106) (2023-11-02)


### Bug Fixes

* 删除了设置-大促四个页面title ([7b61b9d](https://code.devops.xiaohongshu.com/fe/promotion/commits/7b61b9dd525743cab3de570ef2d675dc46d4da21))


### Features

* **robot:** 更换域名 ([bb1ed2d](https://code.devops.xiaohongshu.com/fe/promotion/commits/bb1ed2d800f7b58742f428526e6b28f75a224611))





## [1.8.105](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.105) (2023-11-02)


### Features

* 调整黑名单路由配置 ([b4fe294](https://code.devops.xiaohongshu.com/fe/promotion/commits/b4fe2947eb458f7c3c4617a8e05149bfbd92909a))





## [1.8.104](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.104) (2023-10-27)


### Features

* 调整文案和模板文件 ([ced886c](https://code.devops.xiaohongshu.com/fe/promotion/commits/ced886c4ad2f2cbe567744c3a4d4b24361c35b3f))





## [1.8.103](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.103) (2023-10-27)


### Reverts

* Revert "feat: 新增协作人" ([80032fb](https://code.devops.xiaohongshu.com/fe/promotion/commits/80032fb1c5691127901e8a69295abb5a079a87c6))





## [1.8.102](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.102) (2023-10-26)


### Features

* 黑名单查询 ([ccce260](https://code.devops.xiaohongshu.com/fe/promotion/commits/ccce26039a09f603229b3d06de65b6fb9b4d4794))





## [1.8.101](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.101) (2023-10-26)


### Features

* 新增协作人 ([6853225](https://code.devops.xiaohongshu.com/fe/promotion/commits/685322551e6c4253166df57da906ac28aab4c0e3))





## [1.8.100](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.100) (2023-10-26)


### Features

* **ssg:** lint ([8f61fec](https://code.devops.xiaohongshu.com/fe/promotion/commits/8f61fec3e9fdaaccf82b3aa150f37790f1b4245c))





## [1.8.99](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.99) (2023-10-26)


### Features

* **ssg:** 增加机器人通知 ([931cfa8](https://code.devops.xiaohongshu.com/fe/promotion/commits/931cfa840dd9622d19e10920cc70dfd6f9f4fa35))





## [1.8.98](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.98) (2023-10-25)


### Features

* 频控逻辑修改 ([7baae0c](https://code.devops.xiaohongshu.com/fe/promotion/commits/7baae0c285810111186054ecfebf9db7848cf559))





## [1.8.97](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.97) (2023-10-24)


### Features

* 大促预演系统 错误message透传 ([fe05db8](https://code.devops.xiaohongshu.com/fe/promotion/commits/fe05db8a57abbca449d0f93d24219b50ea8ed4cd))
* 增加枚举 ([0d7d8ae](https://code.devops.xiaohongshu.com/fe/promotion/commits/0d7d8ae3a3efcdf71d08f986ac67c1d930018783))





## [1.8.96](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.96) (2023-10-23)


### Bug Fixes

* **apollo:** 修复阿波罗权限未打通问题 ([a5a6742](https://code.devops.xiaohongshu.com/fe/promotion/commits/a5a6742583b096a27ae4b6d8ecff76e5b2bf5dac))





## [1.8.95](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.95) (2023-10-18)


### Bug Fixes

* cors ([bfe41e9](https://code.devops.xiaohongshu.com/fe/promotion/commits/bfe41e94555b04e46e61e19ee1d1be2d3b998425))





## [1.8.94](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.94) (2023-10-18)


### Features

* 调整策略平台表单校验逻辑 ([601be41](https://code.devops.xiaohongshu.com/fe/promotion/commits/601be4192e45f511ca1187bc65f445ece1a1a2ef))
* 调整策略平台管控策略 ([f1c2b6d](https://code.devops.xiaohongshu.com/fe/promotion/commits/f1c2b6d3180c8a196cd015e995ccb74bb593f5a5))
* patch version ([02a02c3](https://code.devops.xiaohongshu.com/fe/promotion/commits/02a02c3b959ac8ecc09fe91afa79012feea00a21))





## [1.8.93](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.93) (2023-10-17)


### Features

* 新增标签 ([cdf99c3](https://code.devops.xiaohongshu.com/fe/promotion/commits/cdf99c39131e9fdb69bbf3bf7d17b045c7795d34))





## [1.8.92](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.92) (2023-10-17)


### Features

* 增加枚举 ([d282c0d](https://code.devops.xiaohongshu.com/fe/promotion/commits/d282c0dfdf91708c1c46799b44424eb42832fdd0))





## [1.8.91](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.91) (2023-10-17)


### Features

* 增加变更管控 ([2a34bfb](https://code.devops.xiaohongshu.com/fe/promotion/commits/2a34bfb3d13637b65139e6a506b164f3deabdedc))





## [1.8.90](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.90) (2023-10-16)


### Features

* 增加变更管控 ([d7906f0](https://code.devops.xiaohongshu.com/fe/promotion/commits/d7906f06f95bc74ee3ee1b2a64720d969febacee))





## [1.8.89](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.89) (2023-10-12)


### Bug Fixes

* 投放-解决文本列表字段在初始化时赋值数组的问题 ([94df601](https://code.devops.xiaohongshu.com/fe/promotion/commits/94df601187756609c3cb991403b18f5704d53000))





## [1.8.88](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.88) (2023-10-11)


### Bug Fixes

* 修复复制活动审核类型错误问题 ([3ef8491](https://code.devops.xiaohongshu.com/fe/promotion/commits/3ef8491eeb48a02ab4a18a781640e99e2e266247))





## [1.8.87](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.87) (2023-10-11)


### Bug Fixes

* 购物车利益点咨询 ([fec693a](https://code.devops.xiaohongshu.com/fe/promotion/commits/fec693a98b929cfcfa92d38c78e8556c67cbe0ee))





## [1.8.86](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.86) (2023-10-10)


### Features

* **ssg:** 统一使用一个namespace ([5b1ce1c](https://code.devops.xiaohongshu.com/fe/promotion/commits/5b1ce1c47f03a629e782e34371e7ccd327eb01d1))





## [1.8.85](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.85) (2023-10-09)


### Bug Fixes

* 修复选品池接口 401 ([51f9e5a](https://code.devops.xiaohongshu.com/fe/promotion/commits/51f9e5ad1a0c820cf6ec111afb7c5b75e790088c))





## [1.8.84](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.84) (2023-10-07)


### Features

* **ssg:** 接入ssg缓存更新逻辑 ([81c1caa](https://code.devops.xiaohongshu.com/fe/promotion/commits/81c1caa0cedc34264f15082a838c83311a8466b0))





## [1.8.83](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.83) (2023-09-25)


### Bug Fixes

* 禁选电商直播间 ([d9584b1](https://code.devops.xiaohongshu.com/fe/promotion/commits/d9584b113c570acc2f91a3596422eaf03fb832aa))





## [1.8.82](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.82) (2023-09-22)


### Features

* 调整圈选文案 ([878b4c3](https://code.devops.xiaohongshu.com/fe/promotion/commits/878b4c39c90befe86b9ef4a33e2c3a5fba9b320f))





## [1.8.81](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.81) (2023-09-22)


### Bug Fixes

* 滚动优化 ([e99cb3e](https://code.devops.xiaohongshu.com/fe/promotion/commits/e99cb3e631fb748803530a2cc0e26539698abb38))





## [1.8.80](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.80) (2023-09-21)

**Note:** Version bump only for package loki-promotion





## [1.8.79](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.79) (2023-09-20)


### Features

* 策略组 ([a3abc33](https://code.devops.xiaohongshu.com/fe/promotion/commits/a3abc33ba4a5a53b861927e8bb870bd1b3b35c03))





## [1.8.78](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.78) (2023-09-20)


### Bug Fixes

* 去除履约优化 ([60f556b](https://code.devops.xiaohongshu.com/fe/promotion/commits/60f556b64cffb6aa773cd441b832045687b4205d))





## [1.8.77](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.77) (2023-09-20)


### Features

* 活动创建修改 ([9f7bceb](https://code.devops.xiaohongshu.com/fe/promotion/commits/9f7bceba6fa324d8f007be23cf0484fe72b444a6))





## [1.8.76](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.76) (2023-09-20)


### Features

* 投放优化SKU投放策略 ([38f9ebb](https://code.devops.xiaohongshu.com/fe/promotion/commits/38f9ebb864cc92bd99643af413cb4a19027fd2e0))





## [1.8.75](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.75) (2023-09-19)


### Bug Fixes

* update dep ([e76f7d2](https://code.devops.xiaohongshu.com/fe/promotion/commits/e76f7d23e2fd35749f8d24fe310a94ee46c15938))





## [1.8.74](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.74) (2023-09-18)


### Features

* 圈选入口 ([7c0e653](https://code.devops.xiaohongshu.com/fe/promotion/commits/7c0e6537d3de89c34353516b2f7116f83d2cb8f5))





## [1.8.73](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.73) (2023-09-15)


### Bug Fixes

* bug ([bea14b0](https://code.devops.xiaohongshu.com/fe/promotion/commits/bea14b0d1a5e50f2c2f2e06bb47daf4062f89363))





## [1.8.72](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.72) (2023-09-11)


### Bug Fixes

* 全部审核优化 ([7229a27](https://code.devops.xiaohongshu.com/fe/promotion/commits/7229a2782d611c079cc3a3761380768a7da44b77))





## [1.8.71](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.71) (2023-09-11)


### Features

* 1 ([85b6010](https://code.devops.xiaohongshu.com/fe/promotion/commits/85b60105bc559b5ea00735fce9885d2f59aef0fe))
* 1 ([ec4d576](https://code.devops.xiaohongshu.com/fe/promotion/commits/ec4d576b72d0e352eefb3b2832e17bd9cd815055))





## [1.8.70](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.70) (2023-09-01)


### Bug Fixes

* empty状态优化 ([78684a9](https://code.devops.xiaohongshu.com/fe/promotion/commits/78684a9b09572e81eda4f3e5a1fbd889c94d4036))





## [1.8.69](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.69) (2023-09-01)


### Bug Fixes

* **dataset:** 修复html格式问题 ([f5dcbde](https://code.devops.xiaohongshu.com/fe/promotion/commits/f5dcbdef39e2eedd00beb5c779a24324a3ab7fbf))





## [1.8.68](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.68) (2023-08-31)


### Features

* **data-set:** 数据集管理埋点 ([3968a43](https://code.devops.xiaohongshu.com/fe/promotion/commits/3968a439267376b2ea26366104be5374dec07eb2))





## [1.8.67](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.67) (2023-08-31)


### Bug Fixes

* frequency ([2c8aaae](https://code.devops.xiaohongshu.com/fe/promotion/commits/2c8aaaecec4c7c8db045f7a2fe1d89d125328968))





## [1.8.66](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.66) (2023-08-28)


### Bug Fixes

* lint ([d38bdbc](https://code.devops.xiaohongshu.com/fe/promotion/commits/d38bdbcfc96231708879f0623b566dad7dc642af))





## [1.8.65](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.65) (2023-08-25)


### Bug Fixes

* 链接下线 ([690f237](https://code.devops.xiaohongshu.com/fe/promotion/commits/690f2370f42845c96ec4737a5a284b8ebeb5d8fa))





## [1.8.64](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.64) (2023-08-25)


### Bug Fixes

* bugfix ([28d7cef](https://code.devops.xiaohongshu.com/fe/promotion/commits/28d7cefc0b728aac9254069254c78f4f816c6e71))





## [1.8.63](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.63) (2023-08-24)


### Bug Fixes

* 测试; ([e0db2b2](https://code.devops.xiaohongshu.com/fe/promotion/commits/e0db2b2fb6d3efa7491b065fdf35c5b87999d197))
* 发放模型 ([8eb70f1](https://code.devops.xiaohongshu.com/fe/promotion/commits/8eb70f1e3e7f48ffbf6e1108a1a873a88e57f33e))





## [1.8.62](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.62) (2023-08-23)


### Bug Fixes

* 前置利益点咨询 ([d46649a](https://code.devops.xiaohongshu.com/fe/promotion/commits/d46649a73001112975b6d2cb72a8e98df67d8ba9))





## [1.8.61](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.61) (2023-08-23)


### Features

* 测试查看; ([fc04b6c](https://code.devops.xiaohongshu.com/fe/promotion/commits/fc04b6c63a9e6f5ac33bf92c88ad7c5ee7e27b14))





## [1.8.60](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.60) (2023-08-23)

**Note:** Version bump only for package loki-promotion





## [1.8.59](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.59) (2023-08-23)


### Bug Fixes

* fixbugbug ([97a08e9](https://code.devops.xiaohongshu.com/fe/promotion/commits/97a08e9258bbef42844b1822eccbe896d4bd3458))





## [1.8.58](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.58) (2023-08-23)


### Bug Fixes

* fixbugbug ([aa992a9](https://code.devops.xiaohongshu.com/fe/promotion/commits/aa992a9679071f8bc6f25da58b2d71e45734b4b6))





## [1.8.57](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.57) (2023-08-22)


### Bug Fixes

* 复制策略展示问题fix ([de892ba](https://code.devops.xiaohongshu.com/fe/promotion/commits/de892ba8a1e0b6dbb1d798e31e6776e794152b38))





## [1.8.56](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.56) (2023-08-22)


### Reverts

* Revert "feat: 不展示问题;" ([83b9e27](https://code.devops.xiaohongshu.com/fe/promotion/commits/83b9e27012b8ed009224b1842ac485ebf4e864cb))





## [1.8.55](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.55) (2023-08-22)


### Features

* 投放支持ITEM ([d6a5ea2](https://code.devops.xiaohongshu.com/fe/promotion/commits/d6a5ea26920533b59f42a16056222016566b2a52))





## [1.8.54](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.54) (2023-08-22)


### Features

* appICon始终展示 ([55d2b37](https://code.devops.xiaohongshu.com/fe/promotion/commits/55d2b37a627f7853042a8b3af058e31db3e86e6a))





## [1.8.53](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.53) (2023-08-22)


### Features

* 算法规则增加算法模型选择 ([4851f32](https://code.devops.xiaohongshu.com/fe/promotion/commits/4851f32010e4ebb7e6af6e358adc325791d718f5))





## [1.8.52](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.52) (2023-08-22)


### Features

* 不展示问题; ([f39f8a1](https://code.devops.xiaohongshu.com/fe/promotion/commits/f39f8a1079242a70dab0b527eaafe681db753c7e))





## [1.8.51](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.51) (2023-08-22)


### Features

* 测试; ([4ec1efe](https://code.devops.xiaohongshu.com/fe/promotion/commits/4ec1efe3c83bfdc68cefe3cea48a9edb4ae8fd77))





## [1.8.50](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.50) (2023-08-22)


### Bug Fixes

* 给服务端 formList 新增 options 字段, 暂时禁用多选配置 ([eeb5c91](https://code.devops.xiaohongshu.com/fe/promotion/commits/eeb5c91648ee6f095b66ff6ae5b7975458aa096d))





## [1.8.49](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.49) (2023-08-21)


### Bug Fixes

* fixbugbug ([bb80781](https://code.devops.xiaohongshu.com/fe/promotion/commits/bb807812bbabdb22896ac25bd4b69c49ed72bf70))





## [1.8.48](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.48) (2023-08-21)


### Features

* 审核中状态优化 ([1480aad](https://code.devops.xiaohongshu.com/fe/promotion/commits/1480aada83aeb34575ae676a9b48435d5573714b))





## [1.8.47](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.47) (2023-08-21)


### Reverts

* Revert "Revert "feat: 策略后台支持批量复制"" ([9533d0d](https://code.devops.xiaohongshu.com/fe/promotion/commits/9533d0d53c95a0aef2e5edcc5abfdda9c4ab3b18))





## [1.8.46](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.46) (2023-08-19)


### Reverts

* Revert "feat: 策略后台支持批量复制" ([53be83e](https://code.devops.xiaohongshu.com/fe/promotion/commits/53be83ec19e8f1dc1163bf02fa5a81116bc18bc6))





## [1.8.45](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.45) (2023-08-19)


### Bug Fixes

* 活动审核 ([0aabb8b](https://code.devops.xiaohongshu.com/fe/promotion/commits/0aabb8bbdf9948b4d7a11766d3a487d193a86128))





## [1.8.44](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.44) (2023-08-19)


### Features

* 策略后台支持批量复制 ([5223933](https://code.devops.xiaohongshu.com/fe/promotion/commits/5223933f7d1b4a6af00383de5db252410b7f543f))





## [1.8.43](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.43) (2023-08-18)


### Bug Fixes

* 活动日期取值字段更改 ([55af64a](https://code.devops.xiaohongshu.com/fe/promotion/commits/55af64afff05e9a1b7327c38839a47f4469224d5))


### Features

* 交互优化 ([6509ff6](https://code.devops.xiaohongshu.com/fe/promotion/commits/6509ff6d24499d284950197cba40a5efc6395b41))





## [1.8.42](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.42) (2023-08-18)


### Features

* 膨胀玩法 ([f693f7d](https://code.devops.xiaohongshu.com/fe/promotion/commits/f693f7d87f03a800386cf41465f40994de22e30b))





## [1.8.41](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.41) (2023-08-18)


### Bug Fixes

* resotre weekdaysPrice ([9f8a693](https://code.devops.xiaohongshu.com/fe/promotion/commits/9f8a6930f3f2dfa7cb73d840c245e4cf43c9560c))





## [1.8.40](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.40) (2023-08-17)


### Bug Fixes

* 活动分享链接更改 ([df9eef1](https://code.devops.xiaohongshu.com/fe/promotion/commits/df9eef1743b41ab8f25e8e33de481512ec59c4b2))





## [1.8.39](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.39) (2023-08-15)


### Features

* 加群文案 ([4d96e8d](https://code.devops.xiaohongshu.com/fe/promotion/commits/4d96e8d0817c701db2528f5cf67ed53d621f2b92))





## [1.8.38](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.38) (2023-08-14)


### Features

* 优化直播预告组件的报错文案 ([f271ffe](https://code.devops.xiaohongshu.com/fe/promotion/commits/f271ffe69535a05ef097c509aaf1ff2d40f92947))





## [1.8.37](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.37) (2023-08-11)

**Note:** Version bump only for package loki-promotion





## [1.8.36](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.36) (2023-08-11)


### Features

* 频控组触发频控非必填 ([30f1606](https://code.devops.xiaohongshu.com/fe/promotion/commits/30f1606d6f5ae131a7473304957e50ada90776d2))





## [1.8.35](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.35) (2023-08-09)


### Features

* 修复JsonTree字段联动依赖项存在多个同类型字段时不能正确提供下拉框选项值的问题 ([e4f7804](https://code.devops.xiaohongshu.com/fe/promotion/commits/e4f7804396bb79d5548de57ad9272bd6f717af96))





## [1.8.34](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.34) (2023-08-09)


### Features

* 七夕投放,新增笔记搜索组件/直播预告组件/资源位ID,减少字段联动config大小,日期时间组件支持配置值为时间戳,解决struct/list<struct>下数字型组件非必填时保存后字段列消失的问题…… ([5e20769](https://code.devops.xiaohongshu.com/fe/promotion/commits/5e207693760b0d508229adaa2b8700f3fc73c709))





## [1.8.33](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.33) (2023-08-08)

**Note:** Version bump only for package loki-promotion





## [1.8.32](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.32) (2023-08-08)


### Features

* 调整枚举 ([45c569d](https://code.devops.xiaohongshu.com/fe/promotion/commits/45c569df50c24262614ef21b09e7e2067d180cfe))





## [1.8.31](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.31) (2023-08-08)


### Features

* 策略后台支持七夕活动 ([9b8118b](https://code.devops.xiaohongshu.com/fe/promotion/commits/9b8118b5ec6fa38ae452aae70536d5cd5a510829))





## [1.8.30](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.30) (2023-08-05)


### Features

* 去除总搜ac的图片宽度限制 ([bc9d2cc](https://code.devops.xiaohongshu.com/fe/promotion/commits/bc9d2ccfdec251b8b3302f2cd2e77a36f0317d0b))





## [1.8.29](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.29) (2023-08-04)


### Bug Fixes

* 盘货优化 ([6168afc](https://code.devops.xiaohongshu.com/fe/promotion/commits/6168afc2e1898cfe5664fdb0771de4630a7202cd))





## [1.8.28](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.28) (2023-08-04)


### Bug Fixes

* lint ([22ee4b6](https://code.devops.xiaohongshu.com/fe/promotion/commits/22ee4b6774f4cc81343dc2905f5ced83697c59fd))





## [1.8.27](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.27) (2023-08-03)


### Bug Fixes

* 素材字段默认露出 ([6f99c6d](https://code.devops.xiaohongshu.com/fe/promotion/commits/6f99c6df3d2935242b3c029ddaf69aa03761ed86))





## [1.8.26](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.26) (2023-08-02)


### Bug Fixes

* 素材字段默认露出 ([f0f767b](https://code.devops.xiaohongshu.com/fe/promotion/commits/f0f767bbb0110e5529b1839489890d4095544eba))





## [1.8.25](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.25) (2023-08-01)


### Bug Fixes

* 邀约入口 ([9d112f7](https://code.devops.xiaohongshu.com/fe/promotion/commits/9d112f7bc2c9f3056d0803f8ea717bcc2d9de402))





## [1.8.24](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.24) (2023-08-01)


### Bug Fixes

* bugfix number required validate ([9fb871f](https://code.devops.xiaohongshu.com/fe/promotion/commits/9fb871f4991f1a704cbb39d9357b27b4a6e184f7))





## [1.8.23](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.23) (2023-08-01)


### Bug Fixes

* auth ([728bda5](https://code.devops.xiaohongshu.com/fe/promotion/commits/728bda5680ce720189118e21499d98f32678df96))


### Features

* 创建子活动 ([df40de8](https://code.devops.xiaohongshu.com/fe/promotion/commits/df40de8b847c86e8ed91da6a209dc6e2c798e615))





## [1.8.22](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.22) (2023-08-01)


### Bug Fixes

* 恢复投放之前被误操作的部分 ([5454a9d](https://code.devops.xiaohongshu.com/fe/promotion/commits/5454a9d05755633cf9b8081d143c88c4cfbd8875))





## [1.8.21](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.21) (2023-07-31)


### Bug Fixes

* 入口卡控 ([a2df568](https://code.devops.xiaohongshu.com/fe/promotion/commits/a2df568aa66af4ef31cd695366280786550776b7))





## [1.8.20](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.20) (2023-07-31)


### Features

* 老入口 ([0320d20](https://code.devops.xiaohongshu.com/fe/promotion/commits/0320d2087d4450788b10487a03bd2649474560a5))





## [1.8.19](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.19) (2023-07-31)


### Bug Fixes

* formId 为0兜底 ([0fc8519](https://code.devops.xiaohongshu.com/fe/promotion/commits/0fc8519d3fd70bd4cf63acb93c6d66f950407baf))





## [1.8.18](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.18) (2023-07-31)


### Bug Fixes

* 入口控制 ([320acdb](https://code.devops.xiaohongshu.com/fe/promotion/commits/320acdb2da2aa3a2f4d77dbb1dd3639023a5db36))


### Features

* 策略接算法 ([f96db78](https://code.devops.xiaohongshu.com/fe/promotion/commits/f96db78e529a8c3a09b6c05cf96807742fae98fe))





## [1.8.17](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.17) (2023-07-31)


### Bug Fixes

* 入口控制 ([68b723e](https://code.devops.xiaohongshu.com/fe/promotion/commits/68b723edbfce37f7ee93880b046b16c25fbe5e78))





## [1.8.16](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.16) (2023-07-31)


### Features

* 单单返 ([66c396c](https://code.devops.xiaohongshu.com/fe/promotion/commits/66c396cf0a797343daf354b9d05599c4a5d65b96))





## [1.8.15](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.15) (2023-07-28)


### Bug Fixes

* introduction ([e7913e8](https://code.devops.xiaohongshu.com/fe/promotion/commits/e7913e8c3f518d0ea19dea4f8cb03238a8c9ac17))





## [1.8.14](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.14) (2023-07-28)


### Features

* 盘货 ([1d19fd1](https://code.devops.xiaohongshu.com/fe/promotion/commits/1d19fd192391e5c22f7eea6918d4381922f584c9))





## [1.8.13](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.13) (2023-07-22)


### Features

* 投放之用户搜索组件字段修改和商品搜索组件样式优化 ([e880ada](https://code.devops.xiaohongshu.com/fe/promotion/commits/e880adacacac494ce52e30e2e67282d38b7ce400))





## [1.8.12](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.12) (2023-07-19)


### Bug Fixes

* 修正展示兜底数据逻辑 ([8bfd248](https://code.devops.xiaohongshu.com/fe/promotion/commits/8bfd248ea466730bdb61c7c26aa38669c9228d5e))





## [1.8.11](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.11) (2023-07-18)


### Features

* JsonTree所有字段都支持编辑字段配置,支持forbidCustom/defaultPrimaryKey/defaultRequired,调整字段标识命名逻辑,选择为空与否时隐藏第三个控件,删除小数位数限制 ([12dd97e](https://code.devops.xiaohongshu.com/fe/promotion/commits/12dd97e6b7d956c37a0904beb0603f7f5dcd8f47))





## [1.8.10](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.10) (2023-07-18)


### Features

* 条件项修改为动态下发 ([67aa06e](https://code.devops.xiaohongshu.com/fe/promotion/commits/67aa06ed880f15d76c4ee36e424b0b373efaccdb))





## [1.8.9](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.9) (2023-07-17)


### Features

* 氛围确认创建人可操作 ([728e5da](https://code.devops.xiaohongshu.com/fe/promotion/commits/728e5dad8ed1b50282c6fd29978e804afb14db62))





## [1.8.8](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.8) (2023-07-13)


### Features

* 综搜ac引导条放开宽度限制 ([e917be2](https://code.devops.xiaohongshu.com/fe/promotion/commits/e917be2bde542da24f602ef56d56d0320d63d076))





## [1.8.7](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.7) (2023-07-05)


### Bug Fixes

* 修复创建时场景无数据问题 ([7385215](https://code.devops.xiaohongshu.com/fe/promotion/commits/73852155e5c6a2ba2b6e53075a7137d425a65006))





## [1.8.6](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.6) (2023-07-05)


### Features

* 列表细节优化 ([4d50adf](https://code.devops.xiaohongshu.com/fe/promotion/commits/4d50adffabdd5c1fdbc8f123774514351d6e50f0))





## [1.8.5](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.5) (2023-07-04)


### Features

* 调整字段和增加预览入口 ([fabaa42](https://code.devops.xiaohongshu.com/fe/promotion/commits/fabaa422201d36e76127fe5ab75a81cc6e9fdd49))





## [1.8.4](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.4) (2023-06-30)


### Features

* 1 ([6d15d8a](https://code.devops.xiaohongshu.com/fe/promotion/commits/6d15d8af1c6e2d1377028f82a3194899be4151ad))
* 增加下单数量condition ([c35eb05](https://code.devops.xiaohongshu.com/fe/promotion/commits/c35eb05f8b990e3f13a42306bac9d37b91414561))





## [1.8.3](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.3) (2023-06-28)


### Features

* del ci yml ([b7fb52b](https://code.devops.xiaohongshu.com/fe/promotion/commits/b7fb52b0938080b65f04d629433183219a69daf9))





## [1.8.2](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.2) (2023-06-28)


### Bug Fixes

* 修复运算符问题 ([401deaa](https://code.devops.xiaohongshu.com/fe/promotion/commits/401deaa3b70b9e1ed9aeb0e6ec4994321b5882c5))





## [1.8.1](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.1) (2023-06-27)

**Note:** Version bump only for package loki-promotion





# [1.8.0](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.8.0) (2023-06-27)

**Note:** Version bump only for package loki-promotion





## [1.7.29](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.29) (2023-06-26)


### Bug Fixes

* 解决冲突导致时间校验代码缺失 ([88078b0](https://code.devops.xiaohongshu.com/fe/promotion/commits/88078b084b6fc48afef8d282d40762206b13f428))





## [1.7.28](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.28) (2023-06-26)


### Features

* 增加商搜 ([b41ce43](https://code.devops.xiaohongshu.com/fe/promotion/commits/b41ce4318d6dd0aa57a6a936e0c325ead8594987))





## [1.7.27](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.27) (2023-06-20)


### Features

* 投放与选品池打通 ([c17ca24](https://code.devops.xiaohongshu.com/fe/promotion/commits/c17ca243162b0e548c56b5e232bd913bb836b1b2))





## [1.7.26](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.26) (2023-06-20)


### Features

* 策略列表增加分页参数 ([f490bfb](https://code.devops.xiaohongshu.com/fe/promotion/commits/f490bfb5ed1ac45fb423b7cd529c8e5c014825dd))





## [1.7.25](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.25) (2023-06-16)


### Bug Fixes

* remove promotion ([96e429f](https://code.devops.xiaohongshu.com/fe/promotion/commits/96e429f453f3992ac422d196a9893e129a061627))
* update container name ([0a5b64a](https://code.devops.xiaohongshu.com/fe/promotion/commits/0a5b64a0b5860ff6c9c8686d1f4f5446f7f9238e))





## [1.7.24](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.24) (2023-06-12)


### Bug Fixes

* 活动简介限制1000 ([d6df80b](https://code.devops.xiaohongshu.com/fe/promotion/commits/d6df80b3e0261bac83afffb4bbe590b0174f92e0))





## [1.7.23](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.23) (2023-06-08)


### Bug Fixes

* 修复全局频控组管理页面分页问题 ([a058ea6](https://code.devops.xiaohongshu.com/fe/promotion/commits/a058ea63c454363e93167871eedd0ffd75fdb7b0))





## [1.7.22](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.22) (2023-06-07)


### Bug Fixes

* 活动简介优化 ([e0ad6b0](https://code.devops.xiaohongshu.com/fe/promotion/commits/e0ad6b05b3ad6525d0ca82bada00221343af9c2d))





## [1.7.21](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.21) (2023-06-07)

**Note:** Version bump only for package loki-promotion





## [1.7.20](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.20) (2023-06-07)


### Features

* 投放系统接入人群包 & SelectWithSearch封装及全局替换 ([ebb54ad](https://code.devops.xiaohongshu.com/fe/promotion/commits/ebb54ad9f00cac31aae613ce93bbaab7ec5b91ab))





## [1.7.19](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.19) (2023-06-06)


### Bug Fixes

* 修改 ([9a23d6f](https://code.devops.xiaohongshu.com/fe/promotion/commits/9a23d6f6cf45513275d56e644d6f596af04d4997))





## [1.7.18](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.18) (2023-06-06)


### Features

* 策略营销接入算法判断发券 ([0510242](https://code.devops.xiaohongshu.com/fe/promotion/commits/0510242d56b1d33b19411ad4654cabc13da1dfc0))





## [1.7.17](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.17) (2023-06-05)


### Bug Fixes

* 修复列表搜索传参 ([de3f37d](https://code.devops.xiaohongshu.com/fe/promotion/commits/de3f37dd96b207a3ac51dffa95ed05565a86c401))





## [1.7.16](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.16) (2023-06-02)


### Features

* 增加新容器 ([c7214e9](https://code.devops.xiaohongshu.com/fe/promotion/commits/c7214e9c59e0b5683670d9d19dc05a6055fab5e8))
* 活动氛围置顶 ([7a75502](https://code.devops.xiaohongshu.com/fe/promotion/commits/7a75502a48ece70a6d5e2df6b7ea076a0d0a1d45))





## [1.7.15](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.15) (2023-06-01)


### Features

* 投放系统接入实验平台&优化多处体验 ([3c6064b](https://code.devops.xiaohongshu.com/fe/promotion/commits/3c6064b2a6bbc1b14128dca730f6b4be5d5f54b2))





## [1.7.14](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.14) (2023-05-29)


### Bug Fixes

* 策略平台接入商搜 ([0a4c33a](https://code.devops.xiaohongshu.com/fe/promotion/commits/0a4c33a5c4ff2f39e93d7c452210aa53e48dab41))





## [1.7.13](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.13) (2023-05-25)


### Features

* 黑名单列表新增创建时间 ([2bfb904](https://code.devops.xiaohongshu.com/fe/promotion/commits/2bfb904349b5b44b6916fed4140abd075777e0fb))





## [1.7.12](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.12) (2023-05-24)


### Bug Fixes

* 修复导出信息 ([6b19e2b](https://code.devops.xiaohongshu.com/fe/promotion/commits/6b19e2bd7e7912d6e24c8e52a4e6ebbc0634ba48))
* 修复导出状态问题 ([d96a1b5](https://code.devops.xiaohongshu.com/fe/promotion/commits/d96a1b57a0e25aba6bf670d18770c3c75bdb47ec))


### Features

* 修改父活动编辑权限逻辑 ([cf0c909](https://code.devops.xiaohongshu.com/fe/promotion/commits/cf0c90946b556a292f63fe25a3019d4344c4173d))





## [1.7.11](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.11) (2023-05-23)


### Features

* Ditto增加协作人 ([ec73834](https://code.devops.xiaohongshu.com/fe/promotion/commits/ec73834b5997bfc1c88256c15e1732f64c3a01ac))





## [1.7.10](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.10) (2023-05-22)


### Features

* 调整招选搭投链接 ([91ad1cc](https://code.devops.xiaohongshu.com/fe/promotion/commits/91ad1ccfd55316184f66598039778059fafed0cd))





## [1.7.9](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.9) (2023-05-20)


### Features

* 投放系统临时救火618图片上传问题 ([4520565](https://code.devops.xiaohongshu.com/fe/promotion/commits/45205656858911738af2307f24ec91543bacd56d))





## [1.7.8](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.8) (2023-05-19)


### Bug Fixes

* 文案 ([defdcbf](https://code.devops.xiaohongshu.com/fe/promotion/commits/defdcbfbc0cecae2497ee480e8210898af25ca16))
* lint ([aa8978c](https://code.devops.xiaohongshu.com/fe/promotion/commits/aa8978c152b664f58b8c2fcd0407765c89bf823f))





## [1.7.7](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.7) (2023-05-18)


### Features

* 创建人群支持david人群包 ([0e334fb](https://code.devops.xiaohongshu.com/fe/promotion/commits/0e334fba1cb2326255790708fcc437a95bf02d21))





## [1.7.6](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.6) (2023-05-18)


### Bug Fixes

* 精度 ([0676fae](https://code.devops.xiaohongshu.com/fe/promotion/commits/0676faea756df7b641916d5fc700d832cb3ad078))





## [1.7.5](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.5) (2023-05-17)


### Features

* 拉用户入群 ([722d9f2](https://code.devops.xiaohongshu.com/fe/promotion/commits/722d9f249832f238d8f388d5d536367f12e24632))





## [1.7.4](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.4) (2023-05-16)


### Features

* fix bug ([81f58e3](https://code.devops.xiaohongshu.com/fe/promotion/commits/81f58e3a01d7fce8866485e2c6c723d84980290d))





## [1.7.3](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.3) (2023-05-16)


### Features

* add promotion ([ea75e1f](https://code.devops.xiaohongshu.com/fe/promotion/commits/ea75e1f24ec4fc92c8737fe212e1f2c2be3ecd9b))





## [1.7.2](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.2) (2023-05-16)


### Features

* 添加商品权限逻辑 ([4b9e308](https://code.devops.xiaohongshu.com/fe/promotion/commits/4b9e308c7f6779f5b161036ba8649ab60c849c3d))





## [1.7.1](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.1) (2023-05-15)


### Features

* 调整活动报名描述 ([eb409cc](https://code.devops.xiaohongshu.com/fe/promotion/commits/eb409cc7ed41e5a1fa1d44acf71df03d44588a05))





# [1.7.0](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.7.0) (2023-05-12)


### Features

* 活动氛围 ([62e4c64](https://code.devops.xiaohongshu.com/fe/promotion/commits/62e4c644872e7b36a693c819e6133d90c5cd96ba))





## [1.6.17](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.6.17) (2023-05-12)


### Bug Fixes

* 履约描述一直可编辑 ([3206f5a](https://code.devops.xiaohongshu.com/fe/promotion/commits/3206f5a49d1306e1b5c778fb46b89c2b18ead946))





## [1.6.16](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.6.16) (2023-05-12)


### Features

* 增加<用户是否是券敏感用户>条件项 ([8d1726e](https://code.devops.xiaohongshu.com/fe/promotion/commits/8d1726e5a0814fac1f82e00ddd1d5a04a1f3bc17))





## [1.6.15](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.6.15) (2023-05-12)


### Features

* 618 活动控价 ([a3b75fa](https://code.devops.xiaohongshu.com/fe/promotion/commits/a3b75fad2cc353525025dde58bb3d5e9d7801a5f))





## [1.6.14](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.6.14) (2023-05-11)


### Bug Fixes

* 膨胀系数兼容小数 ([694d11e](https://code.devops.xiaohongshu.com/fe/promotion/commits/694d11e48eac5adff26abcce3940b073c3f11eb3))





## [1.6.13](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.6.13) (2023-05-11)


### Features

* 履约 ([0f59690](https://code.devops.xiaohongshu.com/fe/promotion/commits/0f5969047a9520f3acc74c5ca10502868ea9272d))





## [1.6.12](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.6.12) (2023-05-11)


### Bug Fixes

* 修复复制活动问题 ([3b920a9](https://code.devops.xiaohongshu.com/fe/promotion/commits/3b920a96f21423de947a285a4f9b556f7648e79e))





## [1.6.11](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.6.11) (2023-05-11)


### Bug Fixes

* 修复人群包创建bug ([4db91c4](https://code.devops.xiaohongshu.com/fe/promotion/commits/4db91c4bff19dfbc941dfb170d10f71237553d25))





## [1.6.10](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.6.10) (2023-05-10)

**Note:** Version bump only for package loki-promotion





## [1.6.9](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.6.9) (2023-05-10)


### Bug Fixes

* 修改枚举项 ([7d33847](https://code.devops.xiaohongshu.com/fe/promotion/commits/7d3384778f6cee0ca891bb4ec5b991542c87aa5c))


### Features

* 修改枚举项 ([99fb303](https://code.devops.xiaohongshu.com/fe/promotion/commits/99fb3034ae034806b19b86ae54b6dbc2e3a6077e))





## [1.6.8](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.6.8) (2023-05-09)


### Bug Fixes

* edit https ([32b1bca](https://code.devops.xiaohongshu.com/fe/promotion/commits/32b1bca7423a0af81beee2b95e9aed223db50c28))





## [1.6.7](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.6.7) (2023-05-08)


### Bug Fixes

* activity type ([a989853](https://code.devops.xiaohongshu.com/fe/promotion/commits/a9898534941ae70857cb6df155418f1280712a6a))





## [1.6.6](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.6.6) (2023-05-08)


### Bug Fixes

* A+级别活动选择商品时默认勾选发送笔记 ([d5d173b](https://code.devops.xiaohongshu.com/fe/promotion/commits/d5d173ba2b085bc3e0ec4012e58ed5567e10680e))





## [1.6.5](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.6.5) (2023-05-08)


### Bug Fixes

* 预售活动选择优化 ([c0ab9d0](https://code.devops.xiaohongshu.com/fe/promotion/commits/c0ab9d015bd63e54e35dad9e7010c37bd3cb7ebe))





## [1.6.4](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.6.4) (2023-05-08)


### Bug Fixes

* 接入盘招选反馈平台 ([43adb45](https://code.devops.xiaohongshu.com/fe/promotion/commits/43adb45caa7148cbf23c62e3fc78fadd65e9b121))





## [1.6.3](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.6.3) (2023-05-06)


### Bug Fixes

* ditto history ([11d1449](https://code.devops.xiaohongshu.com/fe/promotion/commits/11d144985474c20c2bc4dcef2340061b695c48ac))





## [1.6.2](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.6.2) (2023-05-06)


### Bug Fixes

* 修复子活动话题 ([1bbf979](https://code.devops.xiaohongshu.com/fe/promotion/commits/1bbf979ac39630ac3460bf7a69f9ebf3be527051))





## [1.6.1](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.6.1) (2023-05-06)

**Note:** Version bump only for package loki-promotion





# [1.6.0](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.6.0) (2023-05-06)


### Features

* 营销黑名单 ([27e2c02](https://code.devops.xiaohongshu.com/fe/promotion/commits/27e2c02a41f9716fe17ee8a6011dbb7104359bcb))





## [1.5.24](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.5.24) (2023-05-06)


### Bug Fixes

* baseUrl ([d25362a](https://code.devops.xiaohongshu.com/fe/promotion/commits/d25362a113c664baecb05fca8d07c9f355ef06a7))





## [1.5.23](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.5.23) (2023-05-05)


### Bug Fixes

* 样式走查 ([5b7c174](https://code.devops.xiaohongshu.com/fe/promotion/commits/5b7c174ca51276ec6b1c3053ba40da7088dfbbfc))
* 注释 ([515ab76](https://code.devops.xiaohongshu.com/fe/promotion/commits/515ab765d1f385acb3c5a8eac40a554cf8d025b7))





## [1.5.22](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.5.22) (2023-05-05)


### Bug Fixes

* 定金预售-招商 ([0a9347e](https://code.devops.xiaohongshu.com/fe/promotion/commits/0a9347e8b7009f5afb2335b63baba04863bcc353))





## [1.5.21](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.5.21) (2023-05-05)


### Bug Fixes

* bug ([947fec5](https://code.devops.xiaohongshu.com/fe/promotion/commits/947fec5440e10a69daf29cc01626dd842ba425c3))


### Features

* remove log ([5624121](https://code.devops.xiaohongshu.com/fe/promotion/commits/5624121f4374a24459ce2cd288fe38b9c661d85c))
* remove log ([b0335a1](https://code.devops.xiaohongshu.com/fe/promotion/commits/b0335a1a0e7de3cac559508d0a13b376f27506e4))





## [1.5.20](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.5.20) (2023-05-04)


### Features

* 修改条件项映射 ([adaeabe](https://code.devops.xiaohongshu.com/fe/promotion/commits/adaeabe0385755acb13b51c23543a43196efb7be))





## [1.5.19](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.5.19) (2023-05-04)


### Features

* 关联促销组和活动等级做关联 ([f21478f](https://code.devops.xiaohongshu.com/fe/promotion/commits/f21478f2811c43f33a8f798c022f480d2d3a3f27))





## [1.5.18](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.5.18) (2023-04-28)


### Bug Fixes

* 修复子活动图片上传 ([10b13b6](https://code.devops.xiaohongshu.com/fe/promotion/commits/10b13b63ebc586165cdd64e261bb2e6fa829ef72))
* 修改获取ecrm人群包的接口 ([4af30e4](https://code.devops.xiaohongshu.com/fe/promotion/commits/4af30e482d3cbbceb704e5f042b440962313ee15))





## [1.5.17](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.5.17) (2023-04-27)


### Features

* 优化细节 ([2db4f96](https://code.devops.xiaohongshu.com/fe/promotion/commits/2db4f96a8a5681433fbfdf941d05ef2315eebb82))





## [1.5.16](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.5.16) (2023-04-26)


### Features

* 投放系统支持商家点查,表单新增温馨提示,优化JsonTable必填项校验功能,图片链接支持预览,完善节点上下游交互 ([a4bf915](https://code.devops.xiaohongshu.com/fe/promotion/commits/a4bf915230a589f3b798dcd5f2ca4aae31a41b79))





## [1.5.15](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.5.15) (2023-04-26)


### Features

* 子活动详情新增图片 ([86cd17b](https://code.devops.xiaohongshu.com/fe/promotion/commits/86cd17b6878de6e9dab02fb91cd979e31cb50871))





## [1.5.14](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.5.14) (2023-04-21)


### Bug Fixes

* dacu user add ([464a129](https://code.devops.xiaohongshu.com/fe/promotion/commits/464a1291baecd06aba4a10bd183680e8048bb8c6))





## [1.5.13](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.5.13) (2023-04-18)


### Features

* 投放系统重构2.0 ([24af63f](https://code.devops.xiaohongshu.com/fe/promotion/commits/24af63f41f19adfc7a03139971c9d8d3adea2b7b))





## [1.5.12](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.5.12) (2023-04-17)


### Features

* 升级组件 ([5de8fed](https://code.devops.xiaohongshu.com/fe/promotion/commits/5de8feda7792c762a3d8bf14f7ce955360bf94cb))





## [1.5.11](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.5.11) (2023-04-12)


### Bug Fixes

* **launchPlan:** 默认值去除 ([ac3c5cd](https://code.devops.xiaohongshu.com/fe/promotion/commits/ac3c5cd945533500755d7d77e4b93dfb28d7f826))





## [1.5.10](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.5.10) (2023-04-06)


### Features

* 资质审核价格取消会员价 ([9c505a7](https://code.devops.xiaohongshu.com/fe/promotion/commits/9c505a7c433f1529d5038f78e6ceacec136e6938))





## [1.5.9](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.5.9) (2023-03-29)


### Features

* 优化资源位编辑逻辑 ([dc7cf23](https://code.devops.xiaohongshu.com/fe/promotion/commits/dc7cf23860a432e04c0c6e74a95fda3bf6e47887))





## [1.5.8](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.5.8) (2023-03-27)


### Bug Fixes

* 修复模版下载问题 ([c35244b](https://code.devops.xiaohongshu.com/fe/promotion/commits/c35244b76635163121da2c924fc47ea2846a1482))





## [1.5.7](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.5.7) (2023-03-24)


### Features

* 规范路由文件 ([4ea4982](https://code.devops.xiaohongshu.com/fe/promotion/commits/4ea4982691bfdd52d54ee177adcca59da08d0a6f))
* 投放系统重构1.0 ([929d975](https://code.devops.xiaohongshu.com/fe/promotion/commits/929d9751f912ff7e652af6f74b909b12ee2433a5))





## [1.5.6](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.5.6) (2023-03-22)


### Bug Fixes

* 修复商详跳转 ([7bcadff](https://code.devops.xiaohongshu.com/fe/promotion/commits/7bcadff855af25abe7c7326037b5e9d12fefa665))





## [1.5.5](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.5.5) (2023-03-20)

**Note:** Version bump only for package loki-promotion





## [1.5.4](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.5.4) (2023-03-17)

**Note:** Version bump only for package loki-promotion





## [1.5.3](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.5.3) (2023-03-16)


### Bug Fixes

* 修复活动报名资质校验枚举 ([6ac1149](https://code.devops.xiaohongshu.com/fe/promotion/commits/6ac11499b7ad9dd0b9e59b434c7423d6db7745b5))





## [1.5.2](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.5.2) (2023-03-14)

**Note:** Version bump only for package loki-promotion





## [1.5.1](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.5.1) (2023-03-10)


### Bug Fixes

* 修复类目查询问题 ([f1ff1b0](https://code.devops.xiaohongshu.com/fe/promotion/commits/f1ff1b078fd6fe482e4baa3d53e59788c664daf4))





# [1.5.0](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.5.0) (2023-03-03)


### Features

* 商家审核后台新增导出功能 ([821f415](https://code.devops.xiaohongshu.com/fe/promotion/commits/821f415795e37c743a8aac544a0356ee8d8089d8))





## [1.4.19](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.4.19) (2023-03-02)


### Bug Fixes

* 修复图片展示问题 ([8b054c6](https://code.devops.xiaohongshu.com/fe/promotion/commits/8b054c62a68a5f698a5ae91f5d6f7fcdbbe4822f))





## [1.4.18](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.4.18) (2023-02-27)


### Bug Fixes

* 优化交互 ([438618c](https://code.devops.xiaohongshu.com/fe/promotion/commits/438618cb1f72714af621ed374fbae789d75e245c))


### Features

* 撤销商品审核 ([a6b3513](https://code.devops.xiaohongshu.com/fe/promotion/commits/a6b3513a208c891bacfeef1673a30502606103b7))





## [1.4.17](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.4.17) (2023-02-23)


### Bug Fixes

* tab change ([c72ab5e](https://code.devops.xiaohongshu.com/fe/promotion/commits/c72ab5e8e6d73454ff35e89a326bf513fb618a73))





## [1.4.16](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.4.16) (2023-02-22)


### Bug Fixes

* lint ([070dfe8](https://code.devops.xiaohongshu.com/fe/promotion/commits/070dfe837eecf957171588bb79e0f8cb31f228dd))





## [1.4.15](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.4.15) (2023-02-21)


### Features

* 审核不通过 -> 驳回 ([7e5d995](https://code.devops.xiaohongshu.com/fe/promotion/commits/7e5d995bc80fc005fcb6ed9af3c76cc81e603a9e))





## [1.4.14](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.4.14) (2023-02-13)


### Features

* 活动报名-修改父活动表格操作列更多的展示逻辑 ([8fcbad2](https://code.devops.xiaohongshu.com/fe/promotion/commits/8fcbad2a8c3a7bb33ad9814d6030af12af7b3968))





## [1.4.13](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.4.13) (2023-02-08)


### Features

* 隐藏父活动导出 ([c5b8fb5](https://code.devops.xiaohongshu.com/fe/promotion/commits/c5b8fb50a79348dbf6a7f119819d582b48359f51))





## [1.4.12](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.4.12) (2023-02-06)


### Bug Fixes

* 尝试修复创建子活动路由跳转问题 ([7e5e274](https://code.devops.xiaohongshu.com/fe/promotion/commits/7e5e274192524ae0c800f62ae3c10cd1e56cd167))





## [1.4.11](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.4.11) (2023-01-17)


### Bug Fixes

* 修复子活动编辑类目不回显的问题 ([85c4140](https://code.devops.xiaohongshu.com/fe/promotion/commits/85c41401c7dc5cc99e41f6eca6d11fb4f745d4a9))





## [1.4.10](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.4.10) (2023-01-16)


### Features

* 父活动支持生成报名链接 ([4b2213d](https://code.devops.xiaohongshu.com/fe/promotion/commits/4b2213d29c9b28e4c4a830967b3fcb3dd69227c9))





## [1.4.9](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.4.9) (2023-01-14)

**Note:** Version bump only for package loki-promotion





## [1.4.8](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.4.8) (2023-01-09)


### Features

* 新增权限校验 ([c0f25f5](https://code.devops.xiaohongshu.com/fe/promotion/commits/c0f25f533aaf0622bb75788e8e520eb00025fdb4))
* remove nouse deps ([88cf279](https://code.devops.xiaohongshu.com/fe/promotion/commits/88cf279c0b0c46328dbc43b4d62ad5e5dad14c72))





## [1.4.7](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.4.7) (2022-12-20)


### Features

* update ([abe1992](https://code.devops.xiaohongshu.com/fe/promotion/commits/abe19927b03b11ec8d31fe6fc86ab077251abdee))





## [1.4.6](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.4.6) (2022-12-20)


### Bug Fixes

* 修复minStockCheck为undefined时的问题 ([26186c3](https://code.devops.xiaohongshu.com/fe/promotion/commits/26186c331a4b7976f1fdd8d66ef54634aede09ad))


### Features

* update ([4b0d7c1](https://code.devops.xiaohongshu.com/fe/promotion/commits/4b0d7c10eb9bca1315ebaf23f8ed0e962c9b6364))





## [1.4.5](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.4.5) (2022-12-14)

**Note:** Version bump only for package loki-promotion





## [1.4.4](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.4.4) (2022-12-14)


### Features

* 优化活动报名体验 ([f4ab3e8](https://code.devops.xiaohongshu.com/fe/promotion/commits/f4ab3e8f8562867048c0c0c31c1d7e4116175782))





## [1.4.3](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.4.3) (2022-11-30)


### Features

* optional for dnot need review audit ([382742a](https://code.devops.xiaohongshu.com/fe/promotion/commits/382742aec471f2db148f6e898952f3dd8a7f9d46))
* update appoint inbiter limit to 30 ([1c21728](https://code.devops.xiaohongshu.com/fe/promotion/commits/1c21728ef943f9f0f8dc2710c87f555d71376359))





## [1.4.2](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.4.2) (2022-11-29)


### Bug Fixes

* fixed time extends ([2fdaf79](https://code.devops.xiaohongshu.com/fe/promotion/commits/2fdaf79461597f47dfc5a43ad5dd64dad3cd0dd0))





## [1.4.1](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.4.1) (2022-11-28)


### Bug Fixes

* fixed sellerLimit label Text ([ebeb674](https://code.devops.xiaohongshu.com/fe/promotion/commits/ebeb6744b1f95e7751a36ec24be8cb20f9b9f01c))





# [1.4.0](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.4.0) (2022-11-28)

**Note:** Version bump only for package loki-promotion





## [1.3.4](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.3.4) (2022-11-21)


### Bug Fixes

* fixed qiankun props access after mount ([7329675](https://code.devops.xiaohongshu.com/fe/promotion/commits/7329675827cc367d3c8840d67ebee777658fcafd))


### Features

* 新增封面人群包 ID ([a810f7f](https://code.devops.xiaohongshu.com/fe/promotion/commits/a810f7f4443037fbef8acfe1b0965422f9b9100b))





## [1.3.3](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.3.3) (2022-11-15)


### Features

* add activeMenu ([b4d33e5](https://code.devops.xiaohongshu.com/fe/promotion/commits/b4d33e5a484b8c5ae5046b7e6a376cd9ed5c5377))





## [1.3.2](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.3.2) (2022-11-14)


### Bug Fixes

* fixed submit when image uploading ([7e12eff](https://code.devops.xiaohongshu.com/fe/promotion/commits/7e12eff63dd022e7da3ff50dae3018aa070ac890))
* fixed time required validate ([1427eba](https://code.devops.xiaohongshu.com/fe/promotion/commits/1427eba0107a9d9ccaff3254221e3b7259e3680e))
* fixed useRoute outside vue component ([8e82def](https://code.devops.xiaohongshu.com/fe/promotion/commits/8e82defc7ff2ea3a9df9d42b50543c7661b7cac1))





## [1.3.1](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.3.1) (2022-11-02)


### Features

* 子活动报名时间不限制当天前不可选 ([d114127](https://code.devops.xiaohongshu.com/fe/promotion/commits/d1141270434c6e1de859b215c37cb2b5368af488))





# [1.3.0](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.3.0) (2022-10-28)


### Bug Fixes

* 登录后跳转地址 & 文案修改 & 给loading加finally & UI ([f944864](https://code.devops.xiaohongshu.com/fe/promotion/commits/f944864089602f347bfb8f23d5fbba577c3645b7))
* 会场签到和任务会场的时间戳用路由传 ([315990f](https://code.devops.xiaohongshu.com/fe/promotion/commits/315990f49cc739a29ab49e28cca6a2c6d949684a))
* 会场任务，会场签到daterange ([75c8255](https://code.devops.xiaohongshu.com/fe/promotion/commits/75c825541a92b2e4726ad2e6a15294483588b8ae))
* 解决按钮闪烁问题 ([2574eeb](https://code.devops.xiaohongshu.com/fe/promotion/commits/2574eeb1d1e74d8ada9746d1fa3a3f7a1feea071))
* 权益池编辑，新建后请求数据 ([d0fffd5](https://code.devops.xiaohongshu.com/fe/promotion/commits/d0fffd5b7130acf47a10f5c37f474d7560a97d6a))
* 权益池页daterange ([2944058](https://code.devops.xiaohongshu.com/fe/promotion/commits/294405827ce22524eed81b7d639cbcdab07373b7))
* 任务会场unix时间戳 ([59382af](https://code.devops.xiaohongshu.com/fe/promotion/commits/59382af7ab7102c95eb821014c1c01bc2f298686))
* 优化 ([06dc8c0](https://code.devops.xiaohongshu.com/fe/promotion/commits/06dc8c065009e5fa0b59f0dfa4ab356fc085bad4))
* 优化 ([3e4a8c0](https://code.devops.xiaohongshu.com/fe/promotion/commits/3e4a8c02fb62da3972cc80a99cdbe1dc8f992177))
* button ([97811b4](https://code.devops.xiaohongshu.com/fe/promotion/commits/97811b48cbe9188c50c23ad2e13b1cf9ac0190fa))
* eslint ([c2efa26](https://code.devops.xiaohongshu.com/fe/promotion/commits/c2efa26d675d820af9c67fd52c99e9a0a3ba5508))
* isComplete逻辑和step修改 ([79affd4](https://code.devops.xiaohongshu.com/fe/promotion/commits/79affd4158b13981316c76641a1885efbb034422))
* toast disappear ([f9ad17f](https://code.devops.xiaohongshu.com/fe/promotion/commits/f9ad17f4fa34f3c84ddfaae3ba942e808c2b3c16))
* type ([5f26668](https://code.devops.xiaohongshu.com/fe/promotion/commits/5f26668dfc2cfddece7e96e705488457d65972f5))
* type ([26095a2](https://code.devops.xiaohongshu.com/fe/promotion/commits/26095a2ec301fff1aa6f0a50debdf84e97c4838e))


### Features

* 🔅促销组接口迁移 Edith，长任务迁移 hawk ([595e25a](https://code.devops.xiaohongshu.com/fe/promotion/commits/595e25a283f450ad83a96305af38eb0244e5dade))
* 新增查看活动与子活动 ([f18184d](https://code.devops.xiaohongshu.com/fe/promotion/commits/f18184d6b2bec17d029aaff9d008eb8024438d9a))





## [1.2.11](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.2.11) (2022-10-22)

**Note:** Version bump only for package loki-promotion





## [1.2.10](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.2.10) (2022-10-19)


### Bug Fixes

* 修复笔记封面传参问题 ([015c6c3](https://code.devops.xiaohongshu.com/fe/promotion/commits/015c6c34544796304e85e80a33ffbdd011a26250))





## [1.2.9](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.2.9) (2022-10-17)


### Bug Fixes

* 修复创建促销组传参问题 ([fc358fb](https://code.devops.xiaohongshu.com/fe/promotion/commits/fc358fb2475e0e378f836b3070d97d67a4594013))





## [1.2.8](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.2.8) (2022-10-17)


### Bug Fixes

* 不抽奖权重字段不显示 ([d05ac95](https://code.devops.xiaohongshu.com/fe/promotion/commits/d05ac9584b1b25a3841da6b4fe2f57e8e3fdee72))
* 统一Copy Icon ([29fcb32](https://code.devops.xiaohongshu.com/fe/promotion/commits/29fcb32f286fbdb8d820e824445be21818766d85))
* 文案修改 ([ad6db4c](https://code.devops.xiaohongshu.com/fe/promotion/commits/ad6db4cf7c5cf5f1e3ddd55fb9c78f8275e30bc3))
* 文案修改 ([5c0e764](https://code.devops.xiaohongshu.com/fe/promotion/commits/5c0e7643dae46f4f6f6dec671d245676b73bc7db))
* 文案修改 ([64aab8a](https://code.devops.xiaohongshu.com/fe/promotion/commits/64aab8a67476a41956a269bdadc95797e5b10114))
* 文案修改 ([5175dcc](https://code.devops.xiaohongshu.com/fe/promotion/commits/5175dcc6ecca5ba749a23b242265c2a84fa62d77))





## [1.2.7](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.2.7) (2022-10-13)


### Bug Fixes

* 修复 ODIN_HOST 问题 ([d48bcc5](https://code.devops.xiaohongshu.com/fe/promotion/commits/d48bcc5201a8fa4ca3424818b5aeee3b5c9e9b06))





## [1.2.6](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.2.6) (2022-10-13)


### Bug Fixes

* 修复 odin_host 问题 ([ed02911](https://code.devops.xiaohongshu.com/fe/promotion/commits/ed029119fe0b157d5cc033e93fc083611900ccda))





## [1.2.5](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.2.5) (2022-10-12)

**Note:** Version bump only for package loki-promotion





## [1.2.4](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.2.4) (2022-10-11)


### Bug Fixes

* 登录后跳转地址 ([cb06f29](https://code.devops.xiaohongshu.com/fe/promotion/commits/cb06f297e904e2e562fbaa82750be68eec6ef744))
* 给loading加finally ([79a1b9d](https://code.devops.xiaohongshu.com/fe/promotion/commits/79a1b9d29963e0d2ed10968f97b741350c748bd1))
* UI ([6bbb712](https://code.devops.xiaohongshu.com/fe/promotion/commits/6bbb712827111f79d6a8bb3c1aca606569e8832d))
* UI ([0a7bed1](https://code.devops.xiaohongshu.com/fe/promotion/commits/0a7bed17652b66756284ab0d9d5caaeb1de0afb0))





## [1.2.3](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.2.3) (2022-10-10)

**Note:** Version bump only for package loki-promotion





## [1.2.2](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.2.2) (2022-09-29)


### Bug Fixes

* 修复促销组业务类型继承父活动 ([1b6f554](https://code.devops.xiaohongshu.com/fe/promotion/commits/1b6f55414524458c38db7b5aed50541fb82d25fe))





## [1.2.1](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.2.1) (2022-09-28)


### Features

* 大促运营权限接鹰眼 ([87d5562](https://code.devops.xiaohongshu.com/fe/promotion/commits/87d5562031321233aae3f15ee222db0ffdfa5708))
* fixUI ([720e60c](https://code.devops.xiaohongshu.com/fe/promotion/commits/720e60ca7ad364980ada70f0d5d1dbc32ffab50c))





# [1.2.0](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.2.0) (2022-09-28)

**Note:** Version bump only for package loki-promotion





## [1.1.4](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.1.4) (2022-09-28)


### Bug Fixes

* 修复 withCredentials ([75e39a7](https://code.devops.xiaohongshu.com/fe/promotion/commits/75e39a76849987bc96dfbf7992b92a304b934f71))
* 修复http验签 ([65b95f1](https://code.devops.xiaohongshu.com/fe/promotion/commits/65b95f19e56e168ab9137f0fb30a100560ae46c0))
* 修改 http config ([342d5e9](https://code.devops.xiaohongshu.com/fe/promotion/commits/342d5e9e85dc8198d84a729debdfda4cf5bc0a86))





## [1.1.3](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.1.3) (2022-09-28)


### Bug Fixes

* 修复CI变量问题 ([8ece2da](https://code.devops.xiaohongshu.com/fe/promotion/commits/8ece2da211f833a9a60e171e711a389f42baf211))





## [1.1.2](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.1.2) (2022-09-27)


### Bug Fixes

* 修复CI ([4510c2c](https://code.devops.xiaohongshu.com/fe/promotion/commits/4510c2c5960ff6f8b2b7896371d4514600812332))





## [1.1.1](https://code.devops.xiaohongshu.com/fe/promotion/compare/<EMAIL>-promotion@1.1.1) (2022-09-27)


### Bug Fixes

* 修复CI ([d82abbe](https://code.devops.xiaohongshu.com/fe/promotion/commits/d82abbe81e3bb535a67df4a1ccb4630f46a7e528))





# 1.1.0 (2022-09-27)


### Bug Fixes

* 表单问题修复 ([3b256f2](https://code.devops.xiaohongshu.com/fe/promotion/commits/3b256f2815571f1038430ed0dfa203b241fe2fbd))
* 促销组时间校验及联动 ([ffe8f6f](https://code.devops.xiaohongshu.com/fe/promotion/commits/ffe8f6fc88b073dd46063406c2270034a5f77c39))
* 导出及长任务修复 ([dec99e5](https://code.devops.xiaohongshu.com/fe/promotion/commits/dec99e572ffb96bfa4de9e4fe6f3babb508ee4f8))
* 第四步审核类型数据修复 ([808ed19](https://code.devops.xiaohongshu.com/fe/promotion/commits/808ed19211096ac26e07e5041144f36eaca60150))
* 日常活动 &大促枚举修复 ([c6a60e6](https://code.devops.xiaohongshu.com/fe/promotion/commits/c6a60e6d36ee81964faf74bbc12c0665f8c2f2df))
* 修复创建促销组 &邀约 ([2a8028a](https://code.devops.xiaohongshu.com/fe/promotion/commits/2a8028aff1109efc0aa56fb6556330c1f5b1d261))
* 修复创建促销组 &邀约 ([79b05ca](https://code.devops.xiaohongshu.com/fe/promotion/commits/79b05cac5efe8e3fc1a4d508894289fa64ebe31d))
* 修复创建促销组转换参数问题 ([33b8fc0](https://code.devops.xiaohongshu.com/fe/promotion/commits/33b8fc08592fc3418e3077f4512138a3c68a7d4d))
* 修复促销组创建 & 子活动权限问题 ([0f034cb](https://code.devops.xiaohongshu.com/fe/promotion/commits/0f034cb5f9ba97ec4509e279696367965a2530b6))
* 修复父活动删除 ([71d02e1](https://code.devops.xiaohongshu.com/fe/promotion/commits/71d02e1d4b63f77b66c49508ba61a299cbd10b15))
* 修复接口调用 ([0c72516](https://code.devops.xiaohongshu.com/fe/promotion/commits/0c72516dc31a35f330384d50033edb4213dae58a))
* 修复缺陷 ([c2dcf54](https://code.devops.xiaohongshu.com/fe/promotion/commits/c2dcf543f29065889295a0161f5e9d8552bc1472))
* 修复业务类型枚举 ([201f3c3](https://code.devops.xiaohongshu.com/fe/promotion/commits/201f3c3d008fb9f18ac95ac68e0ba5b2430cc4d0))
* 修复一下缺陷 ([0e9e4f9](https://code.devops.xiaohongshu.com/fe/promotion/commits/0e9e4f98c1773315f6d81f8dee1d3d9e64f723c6))
* 修复beta环境缺陷 ([eccc8bf](https://code.devops.xiaohongshu.com/fe/promotion/commits/eccc8bff57dd6c20228f1987d9ffe3d06f74f24a))
* 依赖及代码调整 ([6e2ebd5](https://code.devops.xiaohongshu.com/fe/promotion/commits/6e2ebd54840bf8167ca7b7837b6261dec5ec5520))
* 依赖及代码调整 ([b72057a](https://code.devops.xiaohongshu.com/fe/promotion/commits/b72057a96cabf81ad1e1e036235993fbc8604f46))
* fix http transform ([d36d545](https://code.devops.xiaohongshu.com/fe/promotion/commits/d36d54537268b2614c9c81775515c77d7dab70a0))
* fix lint ([54652f8](https://code.devops.xiaohongshu.com/fe/promotion/commits/54652f801ea08506fc045242aceba28b31e01a5b))
* fix lint ([b81a101](https://code.devops.xiaohongshu.com/fe/promotion/commits/b81a1013a6272d5bcb3f92d81638c4b40ba07c1c))
* fix lint ([e457297](https://code.devops.xiaohongshu.com/fe/promotion/commits/e457297787f0a258ad6c991443e703e22f133fbf))
* fix lint ([fa67a9e](https://code.devops.xiaohongshu.com/fe/promotion/commits/fa67a9e4acd4d4970980698871ccb5556cb75e2d))
* fix router ([163cc1b](https://code.devops.xiaohongshu.com/fe/promotion/commits/163cc1b47f66dd2630e115e18235b27dddb6e1d4))
* fix router ([2dc2e3e](https://code.devops.xiaohongshu.com/fe/promotion/commits/2dc2e3ee65660f180ff98a4a8e8a405c1eaab08e))
* fix router ([e5ed993](https://code.devops.xiaohongshu.com/fe/promotion/commits/e5ed9933fbb7565f1fc82897474c86ea04bf67f0))
* fixed lint ([efb1e57](https://code.devops.xiaohongshu.com/fe/promotion/commits/efb1e57fb2ac0946e84c812dd98db1dacba6f246))
* fixed lint ([0beee24](https://code.devops.xiaohongshu.com/fe/promotion/commits/0beee24f7a436b90207e98ba62871285d33341a2))
* fixed lint ([d3e4d3e](https://code.devops.xiaohongshu.com/fe/promotion/commits/d3e4d3ebe3daea1ec771201efd43838411bbb5bb))
* fixed lint ([1b0be83](https://code.devops.xiaohongshu.com/fe/promotion/commits/1b0be8349154eb05c5c21ff5551a874500825532))
* fixed lint ([fce48db](https://code.devops.xiaohongshu.com/fe/promotion/commits/fce48db83971e054f452f12a6d6d72eeb21352e8))
* fixed lint ([bc818fd](https://code.devops.xiaohongshu.com/fe/promotion/commits/bc818fde998b0cd99ef2e7bc4fd0e2ff2c12b22c))
* fixed login ([46f0618](https://code.devops.xiaohongshu.com/fe/promotion/commits/46f0618830e018065c292b3f6fe34ec8034cb1d5))
* hosts修复 ([4d16893](https://code.devops.xiaohongshu.com/fe/promotion/commits/4d16893beb3d823002982ef1dc621e0c25ec83a7))
* task 临时注释路由 ([2078955](https://code.devops.xiaohongshu.com/fe/promotion/commits/207895508939b05dbfc400280a3460f6bde55c44))


### Features

* 报名 & 路由调整 ([089dc24](https://code.devops.xiaohongshu.com/fe/promotion/commits/089dc24c5abf75973c100337615e95c652af20eb))
* 补充子活动列表等UI界面 ([c2438b5](https://code.devops.xiaohongshu.com/fe/promotion/commits/c2438b59e90deb91cb01a4e77fc9d7ef6f17d708))
* 补充子活动列表等UI界面 ([16f570b](https://code.devops.xiaohongshu.com/fe/promotion/commits/16f570b22911e5faee56fb8d9a9489fc0b68b002))
* 补充子活动列表等UI界面 ([eec3ae2](https://code.devops.xiaohongshu.com/fe/promotion/commits/eec3ae26dadd327f70a008c6448ab31656f0054d))
* 补充子活动列表等UI界面 ([34017e2](https://code.devops.xiaohongshu.com/fe/promotion/commits/34017e2dfe729a89e48eaeca0858e3df0f0ce772))
* 查询父活动详情时给按钮加上disabled ([e3881c2](https://code.devops.xiaohongshu.com/fe/promotion/commits/e3881c2047bd9ad885eea2f304d9ab2c081d6e88))
* 代码优化 ([dfbd938](https://code.devops.xiaohongshu.com/fe/promotion/commits/dfbd938f778dcf74f6b325cb79f263cac6540bf7))
* 多入口页面的面包屑优化 ([0e58db8](https://code.devops.xiaohongshu.com/fe/promotion/commits/0e58db8dceffb46ce98fcc1bd391ce7701360ca7))
* 父组件provide数据修改方法 ([6fb782e](https://code.devops.xiaohongshu.com/fe/promotion/commits/6fb782eeccbff57a6d7ee1e2cb2581319f005145))
* 父组件provide数据修改方法 ([b57f024](https://code.devops.xiaohongshu.com/fe/promotion/commits/b57f024eb207e61f819647d8193ec315b08f556f))
* 父组件provide数据修改方法 ([7018a29](https://code.devops.xiaohongshu.com/fe/promotion/commits/7018a29ac6bb44bccbf5378e1a848fc63c500401))
* 活动资质设置中新增商品条件表单 ([a611521](https://code.devops.xiaohongshu.com/fe/promotion/commits/a6115214df8e7e95b8cba35c04622ec451a12858))
* 活动资质设置中新增商品条件表单 ([b528e41](https://code.devops.xiaohongshu.com/fe/promotion/commits/b528e412e9ccf97b057809015e7998f46bdb8d50))
* 活动资质设置中新增商品条件表单 ([e16c654](https://code.devops.xiaohongshu.com/fe/promotion/commits/e16c6541730f280c82807ba45319c751ca334623))
* 活动资质设置中新增商品条件表单 ([9660dc4](https://code.devops.xiaohongshu.com/fe/promotion/commits/9660dc4e41cd51900996a36eb52e5433582dc65d))
* 接入sso登录功能 ([35bb8c2](https://code.devops.xiaohongshu.com/fe/promotion/commits/35bb8c280fc56223a121a26f38db8186d1dffe55))
* 解决缺陷 ([204c42f](https://code.devops.xiaohongshu.com/fe/promotion/commits/204c42fd13bb83b0cd7ea7868b635c18f4270767))
* 解决上次合并冲突出现的错误 ([467e4fd](https://code.devops.xiaohongshu.com/fe/promotion/commits/467e4fd21b76c258d3deb3599ff8728433091bd5))
* 解决上次合并冲突出现的错误 ([e7469a1](https://code.devops.xiaohongshu.com/fe/promotion/commits/e7469a1ea5c5c28e6390d0e54540aa027d200e3d))
* 解决一些缺陷 ([879f901](https://code.devops.xiaohongshu.com/fe/promotion/commits/879f901f96d8ee285f3321d58215ebc2836ef720))
* 解决formula lint暴露的error ([4c6d6ae](https://code.devops.xiaohongshu.com/fe/promotion/commits/4c6d6ae8516bee23bb92df31ac5d769f52bab9c9))
* 解决formula lint暴露的error ([ef003d0](https://code.devops.xiaohongshu.com/fe/promotion/commits/ef003d0b8002e49fd7e288f9c93180e4cd7dcca5))
* 联调接口修复 ([7b0336f](https://code.devops.xiaohongshu.com/fe/promotion/commits/7b0336ff56b7f5435a8ef9d853fbb9c1cf494296))
* 联调接口修复 ([042de05](https://code.devops.xiaohongshu.com/fe/promotion/commits/042de05ca1fe62baa97959311c05f02fc4f77c53))
* 路由纠正 ([787732b](https://code.devops.xiaohongshu.com/fe/promotion/commits/787732bef98d000481df9d49e04863c26d6f4584))
* 路由修正 ([fd9da96](https://code.devops.xiaohongshu.com/fe/promotion/commits/fd9da9613ecda31788b1c2e4b2946f75871c5b80))
* 品类接口迁edith,修复品类组价在第一次选择时会带出值的问题 ([28364e9](https://code.devops.xiaohongshu.com/fe/promotion/commits/28364e9cad012557c4e514433706ed7ad2f91d75))
* 平台大促权限接入 ([5b0576c](https://code.devops.xiaohongshu.com/fe/promotion/commits/5b0576c2227b94ecdd7a7749abce01d6ad761157))
* 删除大促编号 ([5d0c193](https://code.devops.xiaohongshu.com/fe/promotion/commits/5d0c1933342cf1a92e47f04e0307ac02ce9e303e))
* 审核入口修改 ([7c238c1](https://code.devops.xiaohongshu.com/fe/promotion/commits/7c238c107a5b5f9d89ef82788d1d3d41513212c2))
* 梳理路由传参和解决一些缺陷 ([96b4a8a](https://code.devops.xiaohongshu.com/fe/promotion/commits/96b4a8a337a4cbbbce5f2c112732e2c3e9e2de7a))
* 统一toast ([a4be179](https://code.devops.xiaohongshu.com/fe/promotion/commits/a4be17913b073b6227cde80349830f3c72283ef8))
* 完成父活动详情和创建的接口联调,对vuex做了持久化,刷新后仍可恢复数据 ([fc0f335](https://code.devops.xiaohongshu.com/fe/promotion/commits/fc0f335a94c3eba5a53e7197832e91146935fb59))
* 完成父活动详情和创建的接口联调,对vuex做了持久化,刷新后仍可恢复数据 ([4197a4a](https://code.devops.xiaohongshu.com/fe/promotion/commits/4197a4a16881b1e008c21d6ca78da54f9126d52b))
* 完成step1/3/4的数据转换规则 ([12dbb11](https://code.devops.xiaohongshu.com/fe/promotion/commits/12dbb114c8a8006b675e2165ee0f00789a8ed62c))
* 完善父子活动接口联调 ([a3a92e5](https://code.devops.xiaohongshu.com/fe/promotion/commits/a3a92e5342f4abb488dd770d115823e4ec99ef13))
* 完善面包屑跳转 ([8c9203b](https://code.devops.xiaohongshu.com/fe/promotion/commits/8c9203b526160cab597415ef5ef8603fb7c56865))
* 新增筛选栏组件Search.vue ([dbb60f8](https://code.devops.xiaohongshu.com/fe/promotion/commits/dbb60f8998d260ec627063145b5277680d7db521))
* 新增筛选栏组件Search.vue ([a2c86df](https://code.devops.xiaohongshu.com/fe/promotion/commits/a2c86dfa33a5c54c17c94567dd6b356cddca680c))
* 新增筛选栏组件Search.vue ([dee7e15](https://code.devops.xiaohongshu.com/fe/promotion/commits/dee7e1540ea5eaece2df953bb5eb18c7291a62c6))
* 新增数据列表组件DataList.vue ([4a684e4](https://code.devops.xiaohongshu.com/fe/promotion/commits/4a684e49ba3263f35d6ecf82c669a0a4626ddb15))
* 新增数据列表组件DataList.vue ([c7fef76](https://code.devops.xiaohongshu.com/fe/promotion/commits/c7fef762799832111af6bb1086d790e94df60d32))
* 新增数据列表组件DataList.vue ([f8c7db8](https://code.devops.xiaohongshu.com/fe/promotion/commits/f8c7db8eac2db33ac17c353ed4efd6d76b95b6c9))
* 新增MinStockInfo和CategoryCascader组件 ([8325a44](https://code.devops.xiaohongshu.com/fe/promotion/commits/8325a44b1fbf945bd6e208a424baee35fa34f0ea))
* 新增MinStockInfo和CategoryCascader组件 ([7572ca0](https://code.devops.xiaohongshu.com/fe/promotion/commits/7572ca057b7f3f0fdbf87d6df8f920f7f913636e))
* 修改 odin host sit ([41aa44d](https://code.devops.xiaohongshu.com/fe/promotion/commits/41aa44daf25ec1a89648dd24c81577400ea1e4cb))
* 修改平台大促和日常活动的枚举值 ([6a9d934](https://code.devops.xiaohongshu.com/fe/promotion/commits/6a9d934760f65357910d1bd0f5a4565f526da6ab))
* 修改平台大促和日常活动的枚举值 ([f1ca87d](https://code.devops.xiaohongshu.com/fe/promotion/commits/f1ca87d42a037edf9c061202b2d143922629a4ac))
* 修改邀约权限逻辑 ([58eb2ef](https://code.devops.xiaohongshu.com/fe/promotion/commits/58eb2efedf58836370f3e678f160bb67bbbd21ec))
* 邀约 ([c5e2aa6](https://code.devops.xiaohongshu.com/fe/promotion/commits/c5e2aa69df11a3ca393173728cb209fc182566e3))
* 邀约 ([eeee983](https://code.devops.xiaohongshu.com/fe/promotion/commits/eeee98376a0feda0955891cfb00c9f4f1087ce8a))
* 优化代码 ([a86df22](https://code.devops.xiaohongshu.com/fe/promotion/commits/a86df22715bd46218bbc0c515753ba9b7e09ce59))
* 优化代码 ([1c738d2](https://code.devops.xiaohongshu.com/fe/promotion/commits/1c738d27c5de3de044bb44933b42283bc1cfe208))
* 优化多入口页面的面包屑跳转逻辑 ([8b77237](https://code.devops.xiaohongshu.com/fe/promotion/commits/8b77237eeace9d0f476147c8419d4103d8c825e6))
* 优化体验 ([98d4f9d](https://code.devops.xiaohongshu.com/fe/promotion/commits/98d4f9d3b0fbcdb1d5c844e317d6ee80dd8579ac))
* 优化图片url ([6ff48f0](https://code.devops.xiaohongshu.com/fe/promotion/commits/6ff48f0189e85e5ae2115187a09878a2b73d7e0f))
* 优化CreateSubActivityPage的面包屑问题 ([82cd9c6](https://code.devops.xiaohongshu.com/fe/promotion/commits/82cd9c64058a6d25baaba5d876b11f77ba71ec51))
* 在极端情况手动获取父活动信息 ([aff3bf8](https://code.devops.xiaohongshu.com/fe/promotion/commits/aff3bf8e2d43035c2242816cf0c6e090f3d7d559))
* 长任务，邀约、下载 ([523bf3b](https://code.devops.xiaohongshu.com/fe/promotion/commits/523bf3bc831a318243cd68cd0d08a29d075ed917))
* 招商类型及信息 ([3d63511](https://code.devops.xiaohongshu.com/fe/promotion/commits/3d635111bbb33f6a5f7c6130a233f68fe8fb7f4a))
* 招商类型及信息 ([65ca0f7](https://code.devops.xiaohongshu.com/fe/promotion/commits/65ca0f7995dcfeca7872997be5498c3958a0d44a))
* 招商类型及信息 ([7db5ac6](https://code.devops.xiaohongshu.com/fe/promotion/commits/7db5ac69239c0e6888e2c4061e12203c6b662272))
* 招商类型及信息 ([1228bba](https://code.devops.xiaohongshu.com/fe/promotion/commits/1228bba52a2e26795da85b125c69bf95e319c871))
* 招商信息 ([5c1be94](https://code.devops.xiaohongshu.com/fe/promotion/commits/5c1be94d72aa80eea4fcaeae5dccf893dbdb773b))
* 招商信息 ([35abb8c](https://code.devops.xiaohongshu.com/fe/promotion/commits/35abb8cd95ede3f2060910e9126f9218e2479155))
* 招商重构1.0初始化 ([e6acc82](https://code.devops.xiaohongshu.com/fe/promotion/commits/e6acc8278a88d4d47e43d7377c778e5c5072981e))
* 招商重构1.0初始化 ([313d2d0](https://code.devops.xiaohongshu.com/fe/promotion/commits/313d2d03b590b72065ec999706833a9c0946b084))
* 招商重构1.0初始化 ([c9414ba](https://code.devops.xiaohongshu.com/fe/promotion/commits/c9414bad55267c3eb7073735ace70d55109b8c35))
* 招商重构1.0初始化 ([c3259de](https://code.devops.xiaohongshu.com/fe/promotion/commits/c3259de8e3df60b4ca41f4742530f587af9d8389))
* 中秋三天的优化,改动较多,但仍存在问题,欲改用delight formily实现step3,故先存档 ([34cc3c9](https://code.devops.xiaohongshu.com/fe/promotion/commits/34cc3c912567d2d3d55b769e2609160707a93b62))
* 中秋三天的优化,改动较多,但仍存在问题,欲改用delight formily实现step3,故先存档 ([890236c](https://code.devops.xiaohongshu.com/fe/promotion/commits/890236cad2a8fef5e5a412f73f04f6f2895ac334))
* 中秋三天的优化,改动较多,但仍存在问题,欲改用delight formily实现step3,故先存档 ([72e8454](https://code.devops.xiaohongshu.com/fe/promotion/commits/72e8454d69230b693884e5b6c299653dc34312ed))
* 中秋三天的优化,改动较多,但仍存在问题,欲改用delight formily实现step3,故先存档 ([f6032db](https://code.devops.xiaohongshu.com/fe/promotion/commits/f6032db68def7fdb4037f7d968502350ea7c46ea))
* 子活动的四个步骤表单已转为delight formily ([277ace5](https://code.devops.xiaohongshu.com/fe/promotion/commits/277ace55f5615c02d15b5ea347f43a2c703caf9b))
* 子活动的四个步骤表单已转为delight formily ([6d5a054](https://code.devops.xiaohongshu.com/fe/promotion/commits/6d5a05461472a6b0242da42d4ee721e061ee25d2))
* 子活动的四个步骤表单已转为delight formily ([715d8c5](https://code.devops.xiaohongshu.com/fe/promotion/commits/715d8c5e7b831fa34c93e16dd8fb958c00459ba1))
* 子活动的四个步骤表单已转为delight formily ([5aa9a74](https://code.devops.xiaohongshu.com/fe/promotion/commits/5aa9a74dc624563b09425d57367de80da5de97ac))
* 子活动列表联调 ([bc4d3e3](https://code.devops.xiaohongshu.com/fe/promotion/commits/bc4d3e3ccc07bb2f9cc8fe03b198ddbf4cef7331))
* 子活动列表联调 ([907c459](https://code.devops.xiaohongshu.com/fe/promotion/commits/907c45941865a2b546bf2f3cb02f913aedf88f3a))
* 子活动列表头部图片UI调整 ([55edb81](https://code.devops.xiaohongshu.com/fe/promotion/commits/55edb8187f7efbec387e9e5d9182c4350330689e))
* 子活动四步表单数据双向通信完成 ([3f5adc6](https://code.devops.xiaohongshu.com/fe/promotion/commits/3f5adc6bbcab7af394b69dccb82b44bf9c25b300))
* 子活动四步表单数据双向通信完成 ([b1bd7ee](https://code.devops.xiaohongshu.com/fe/promotion/commits/b1bd7ee986c99a8406090dedae5e357756572017))
* 子活动通信采用store ([4167c32](https://code.devops.xiaohongshu.com/fe/promotion/commits/4167c32da55ea54f274274112cd4b37998cd6437))
* ci ([7f8e6c5](https://code.devops.xiaohongshu.com/fe/promotion/commits/7f8e6c52a4bd914d45e426d9f0511a8d7a681f6d))
* CreateActivity页面改为delight formily类型 & ButtonGroup组件全量覆盖 ([eb268b5](https://code.devops.xiaohongshu.com/fe/promotion/commits/eb268b56d0fe88317e0930fb0d8b096445a43cec))
* CreateActivity页面改为delight formily类型 & ButtonGroup组件全量覆盖 ([aa4ace9](https://code.devops.xiaohongshu.com/fe/promotion/commits/aa4ace97489a91e92af37f88fd734003ce224833))
* data list ([b1d275e](https://code.devops.xiaohongshu.com/fe/promotion/commits/b1d275ed042f01deb7c1d2129e4671767f69f1db))
* data list ([c410fc0](https://code.devops.xiaohongshu.com/fe/promotion/commits/c410fc0bd495a50d525e258bfe74fb30f4ffba5e))
* delete useless code ([4f4026b](https://code.devops.xiaohongshu.com/fe/promotion/commits/4f4026bc5b62a41292bbeede6dc6af9499211ec8))
* delete useless code ([3818c6f](https://code.devops.xiaohongshu.com/fe/promotion/commits/3818c6f7b8d5d44e4f5f878f3e061175fb88df8a))
* formily 修复 ([3aea652](https://code.devops.xiaohongshu.com/fe/promotion/commits/3aea652ce05d97180dca983e31b0908a6c8dcd61))
* formily 修复 ([a1750fe](https://code.devops.xiaohongshu.com/fe/promotion/commits/a1750fedd4de86f4fdaf92c2727c240d6c75e290))
* formily 修复 ([d4d6223](https://code.devops.xiaohongshu.com/fe/promotion/commits/d4d62233188d6d262fa62eda6e33bf9f539ecba8))
* formily 修复 ([224e89e](https://code.devops.xiaohongshu.com/fe/promotion/commits/224e89e8b37f4b4f78a30d7a7eeea57cb645d03d))
* git merge feat-activity-yx and resolve conflicts ([9d25d29](https://code.devops.xiaohongshu.com/fe/promotion/commits/9d25d29dd59c8aa93d1f8c91bbc0f4481dc7d62c))
* git merge feat-activity-yx and resolve conflicts ([0ef5d7d](https://code.devops.xiaohongshu.com/fe/promotion/commits/0ef5d7de650b57d9f309f211a5d3c041e17d1e18))
* git merge feat-activity-yx and resolve conflicts ([6c5d609](https://code.devops.xiaohongshu.com/fe/promotion/commits/6c5d6093c20504869dc1e8e076bdaef15a1cf131))
* git merge feat-activity-yx and resolve conflicts ([6d9a6fd](https://code.devops.xiaohongshu.com/fe/promotion/commits/6d9a6fd06ffe91357e16fc22219d18fadeb0d3a5))
* git merge feat-activity-yx and resolve conflicts ([c54987a](https://code.devops.xiaohongshu.com/fe/promotion/commits/c54987a46388dde7cf0df6c3acdd6af5ef1839d6))
* git merge feat-activity-yx and resolve conflicts ([b41ceb0](https://code.devops.xiaohongshu.com/fe/promotion/commits/b41ceb0f5c49774800a720ee319ab8f5605d50cd))
* lerna & monorepo ([71f0366](https://code.devops.xiaohongshu.com/fe/promotion/commits/71f03660e60ff8fa45b83bfae34e688a066693cc))
* lint fix ([9a2486c](https://code.devops.xiaohongshu.com/fe/promotion/commits/9a2486c8abc6ef1bb31b520dfc3351cc894a278b))
* no use code ([ed14a06](https://code.devops.xiaohongshu.com/fe/promotion/commits/ed14a069153c198587150d87b3e0ba0a3f7effcb))
* no use code ([7847cc8](https://code.devops.xiaohongshu.com/fe/promotion/commits/7847cc8d617c2c9c5195024f5ba9db0bf5119f4e))
* package name ([abcea9d](https://code.devops.xiaohongshu.com/fe/promotion/commits/abcea9d1da7b76aa726bed26e95fa9f471d70dcb))
* rebase activity and resolve conflicts ([67b4c04](https://code.devops.xiaohongshu.com/fe/promotion/commits/67b4c049d5cd7c906b114a32d3ec1956b02653bc))
* retry CI ([f1684e9](https://code.devops.xiaohongshu.com/fe/promotion/commits/f1684e94d3d6bbf270f277f1de49c179dd8dfe23))
* router for micro ([cf1d846](https://code.devops.xiaohongshu.com/fe/promotion/commits/cf1d846a1c808996aa0d930c48efd4bd46fddd84))
* task ([15caba8](https://code.devops.xiaohongshu.com/fe/promotion/commits/15caba8333b56900a8a70e40332948d93c4864da))
* task ([c066e1e](https://code.devops.xiaohongshu.com/fe/promotion/commits/c066e1e9f47f3b7b219943a06f65be2248ac6fdf))
* task ([2509bb9](https://code.devops.xiaohongshu.com/fe/promotion/commits/2509bb9712eb554fe67c2156336d8a0be246045b))
* task ([a5303dd](https://code.devops.xiaohongshu.com/fe/promotion/commits/a5303ddb9c335471eb317ee518bc4891013fad75))
* UI初步完成 ([edebbdf](https://code.devops.xiaohongshu.com/fe/promotion/commits/edebbdfc3cd22ceea4fb406314be880c8d373d1e))
* UI初步完成 ([49fd60e](https://code.devops.xiaohongshu.com/fe/promotion/commits/49fd60eb67aa7c8bc99b5323e505dd6fec34a472))
* UI初步完成 ([ea99a7c](https://code.devops.xiaohongshu.com/fe/promotion/commits/ea99a7c6c6fbd45ed05a58688d2d1d9e19bfefd8))
* UI初步完成 ([8444953](https://code.devops.xiaohongshu.com/fe/promotion/commits/844495399099d1b4fa90f901df713f301efa5780))
* UI细化 ([acd7978](https://code.devops.xiaohongshu.com/fe/promotion/commits/acd7978ea531aa72540c45840909d01438862786))
* UI细化 ([2cf0bc5](https://code.devops.xiaohongshu.com/fe/promotion/commits/2cf0bc5bff1909ec841db2e56f4566c889d23e6b))
* UI细化 ([9ad9465](https://code.devops.xiaohongshu.com/fe/promotion/commits/9ad9465a2b58b8e1d947ce751cbc137148617d8a))
* UI细化 ([3048cd0](https://code.devops.xiaohongshu.com/fe/promotion/commits/3048cd020e4ac18a79512965ca622575ea9b7f30))
* upload 相关改造 ([9a8d6ab](https://code.devops.xiaohongshu.com/fe/promotion/commits/9a8d6ab7ba9ee159b43f1d492afe7998025b035d))
