import { toast } from '@xhs/delight'
import {
  deleteApiKolcategory, IDeleteApiKolcategoryPayload,
} from '~/services/buyer/edith_delete_api_kolcategory'
import {
  deleteKolcategoryKolcategoryuser, IDeleteKolcategoryKolcategoryuserPayload,
} from '~/services/buyer/edith_delete_kolcategory_kolcategoryuser'
import {
  deleteKolcategoryuserBatch, IDeleteKolcategoryuserBatchPayload,
} from '~/services/buyer/edith_delete_kolcategoryuser_batch'
import {
  getKolcategoryQuery, IGetKolcategoryQueryResponse,
} from '~/services/buyer/edith_get_kolcategory_query'
import {
  IPostKolcategoryAddPayload,
  postKolcategoryAdd,
} from '~/services/buyer/edith_post_kolcategory_add'
import {
  IPostKolcategoryAdjustPayload,
  postKolcategoryAdjust,
} from '~/services/buyer/edith_post_kolcategory_adjust'
import { IPostKolcategorySearchbyidResponse, postKolcategorySearchbyid } from '~/services/buyer/edith_post_kolcategory_searchbyid'
import { postKolcategorySearchbyname } from '~/services/buyer/edith_post_kolcategory_searchbyname'
import { IPostKolcategoryuserAddPayload, postKolcategoryuserAdd } from '~/services/buyer/edith_post_kolcategoryuser_add'
import {
  IPostKolcategoryuserBatchAddPayload,
  postKolcategoryuserBatchAdd,
} from '~/services/buyer/edith_post_kolcategoryuser_batch_add'
import {
  IPostKolcategoryuserEditPayload,
  postKolcategoryuserEdit,
} from '~/services/buyer/edith_post_kolcategoryuser_edit'
import { postKolcategoryuserQuery } from '~/services/buyer/edith_post_kolcategoryuser_query'
import {
  IPutKolcategoryUpdatePayload,
  putKolcategoryUpdate,
} from '~/services/buyer/edith_put_kolcategory_update'
import {
  Category, PaginationParams, SearchParams, TableRow,
} from '../types'

// 本地类型：买手查询请求/响应（避免依赖旧 GET 文件）
interface BuyerListRequest {
  request: {
    kolCategoryId?: number
    level?: number
    distributorIdList?: string[]
    kolName?: string
    showStatus?: number[]
    orderBy?: string
    orderType?: string
    page?: number
    pageSize?: number
  }
}

interface BuyerListResponse {
  distributorList?: TableRow[]
  hasNext?: boolean
  distributorCnt?: number
}

// 获取类目列表（后端结构：title / subCategoryList）
export const getCategoryList = async (): Promise<Category[]> => {
  const resp = (await getKolcategoryQuery({})) as unknown as IGetKolcategoryQueryResponse
  console.log(resp)
  return resp.categoryList || []
}

// 添加一级类目
export const addCategory = async (title: string): Promise<void> => {
  const payload: IPostKolcategoryAddPayload = {
    request: {
      name: title,
      level: 1,
    },
  }
  await postKolcategoryAdd(payload)
}

// 添加二级类目
export const addSubCategory = async (parentId: string | number, title: string): Promise<void> => {
  const payload: IPostKolcategoryAddPayload = {
    request: {
      name: title,
      level: 2,
      parentId: Number(parentId),
    },
  }
  await postKolcategoryAdd(payload)
}

// 更新类目
export const updateCategory = async (category: Category): Promise<Category> => {
  const payload: IPutKolcategoryUpdatePayload = {
    request: {
      id: Number(category.id),
      name: category.title || '',
    },
  }
  await putKolcategoryUpdate(payload)
  return category
}

// 删除类目
export const deleteCategory = async (id: string | number): Promise<void> => {
  const params: IDeleteApiKolcategoryPayload = { id: Number(id) }
  await deleteApiKolcategory(params)
}

// 更新类目排序：一级
export const updatePrimaryOrder = async (ids: (string | number)[]): Promise<void> => {
  const payload: IPostKolcategoryAdjustPayload = {
    request: {
      idIndexList: ids.map(id => Number(id)),
      level: 1,
    },
  }
  await postKolcategoryAdjust(payload)
}

// 更新类目排序：二级
export const updateSubCategoryOrder = async (params: { parentId: string | number; childIds: (string | number)[] }): Promise<void> => {
  const payload: IPostKolcategoryAdjustPayload = {
    request: {
      idIndexList: params.childIds.map(id => Number(id)),
      level: 2,
      parentId: Number(params.parentId),
    },
  }
  await postKolcategoryAdjust(payload)
}

// 批量删除买手（按 kolCategoryUserId）
export const batchDeleteBuyers = async (kolCategoryUserIds: number[]): Promise<void> => {
  const params: IDeleteKolcategoryuserBatchPayload = {
    request: {
      idList: kolCategoryUserIds,
    },
  }
  await deleteKolcategoryuserBatch(params)
}

// 批量新增买手（按 distributorId 列表）
export const batchAddBuyersToConfig = async (data: { buyerIds: string[]; kolCategoryId: number; level?: number }): Promise<void> => {
  const payload: IPostKolcategoryuserBatchAddPayload = {
    kolCategoryId: data.kolCategoryId,
    level: data.level ?? 2,
    distributorIdList: data.buyerIds.join(','),
  }
  await postKolcategoryuserBatchAdd(payload)
}

// 基于 91413 接口（单个新增）实现的批量新增买手
export const addBuyersByCreateApi = async (data: { buyers: { id: string; name: string; intro?: string }[]; kolCategoryId: number; firstKolCategoryId?: number }): Promise<void> => {
  const { buyers, kolCategoryId } = data
  const firstId = data.firstKolCategoryId
  if (!firstId) throw new Error('缺少所属一级类目ID')

  const tasks = buyers.map(buyer => {
    const payload: IPostKolcategoryuserAddPayload = {
      userId: buyer.id,
      name: buyer.name,
      kolDesc: buyer.intro || '',
      firstKolCategoryId: Number(firstId),
      kolCategoryId: Number(kolCategoryId),
    }
    return postKolcategoryuserAdd(payload)
  })
  const results = await Promise.allSettled(tasks)
  const rejected = results.filter(r => r.status === 'rejected')
  if (rejected.length > 0) {
    throw new Error(`有 ${rejected.length} 个买手新增失败`)
  }
}

// 查询买手列表
export const searchBuyers = async (
  params: Partial<SearchParams> & Partial<PaginationParams>,
): Promise<{ list: TableRow[]; total: number; hasNext?: boolean }> => {
  const raw = {
    kolCategoryId: params.kolCategoryId,
    level: params.level ?? 2,
    distributorIdList: params.distributorIdList,
    kolName: params.kolName,
    showStatus: params.showStatus,
    orderBy: params.orderBy,
    orderType: params.orderType,
    page: params.page ?? 1,
    pageSize: params.pageSize ?? 20,
  } as Record<string, any>

  // 过滤无效参数：undefined、null、空字符串、空数组
  Object.keys(raw).forEach(key => {
    const value = raw[key]
    const isEmptyString = typeof value === 'string' && value.trim() === ''
    const isEmptyArray = Array.isArray(value) && value.length === 0
    if (value === undefined || value === null || isEmptyString || isEmptyArray) {
      delete raw[key]
    }
  })

  const request: BuyerListRequest = {
    request: raw,
  }

  const res = (await postKolcategoryuserQuery(request as any)) as unknown as BuyerListResponse
  // @ts-ignore
  const resp = res?.resp

  // 如果后端返回 resp.success === false，则提示并返回空数据
  // @ts-ignore
  if (resp?.success !== true) {
    // @ts-ignore
    const msg = resp.message || '服务器开小差了~'
    toast.danger({ description: msg })
    return { list: [], total: 0, hasNext: false }
  }

  return {
    list: resp?.categoryList || [],
    total: resp?.distributorCnt ?? (resp?.categoryList?.length ?? 0),
    hasNext: resp?.hasNext,
  }
}

// 编辑买手简介
export const updateBuyerIntro = async (payload: { kolCategoryUserId: number; distributorId: string; distributorDescription: string }): Promise<void> => {
  const body: IPostKolcategoryuserEditPayload = {
    kolCategoryUserId: payload.kolCategoryUserId,
    distributorId: payload.distributorId,
    distributorDescription: payload.distributorDescription,
  }
  await postKolcategoryuserEdit(body)
}

// 单个删除买手
export const deleteBuyer = async (kolCategoryUserId: number): Promise<void> => {
  const params: IDeleteKolcategoryKolcategoryuserPayload = { id: Number(kolCategoryUserId) }
  await deleteKolcategoryKolcategoryuser(params)
}

// 占位：新增弹框远程搜索（替换为新 POST 接口）
export interface BuyerOption {
  id: string
  name: string
  avatar?: string
  followers?: string | number
  intro?: string
  statusText?: string
  value: string
  label: string
  extra?: {
    id: string
    name: string
    avatar?: string
    followers?: string | number
    intro?: string
    statusText?: string
  }
}

export interface SearchBuyerParams {
  keyword: string
  searchType: 'id' | 'name'
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const searchAvailableBuyers = async (_params: string | SearchBuyerParams): Promise<BuyerOption[]> => {
  const isStringParam = typeof _params === 'string'
  const keyword = (isStringParam ? _params : _params.keyword) || ''
  const searchType = (isStringParam ? 'id' : _params.searchType)
  const trimmed = keyword.trim()

  // 打开弹框默认请求：当关键词为空时，直接请求接口（由后端决定是否返回默认列表）
  if (!trimmed) {
    const resp = (await postKolcategorySearchbyid({ request: { distributorIdList: [] } })) as unknown as IPostKolcategorySearchbyidResponse
    console.log(resp)
    const list = resp?.resp?.distributorList || []
    return list.map(item => ({
      id: item.distributorId,
      name: item.distributorName,
      avatar: item.distributorLogo,
      followers: item.fansNumAccum,
      intro: item.distributorDescription,
      statusText: item.showStatus === 1 ? '展示中' : (item.showStatus === 0 ? '新增待生效' : '不满足条件'),
      value: item.distributorId,
      label: item.distributorName,
      extra: {
        id: item.distributorId,
        name: item.distributorName,
        avatar: item.distributorLogo,
        followers: item.fansNumAccum,
        intro: item.distributorDescription,
        statusText: item.showStatus === 1 ? '展示中' : (item.showStatus === 0 ? '新增待生效' : '不满足条件'),
      },
    }))
  }

  // 名称搜索：调用名称搜索接口（保持与ID搜索一致的字段映射）
  if (searchType === 'name') {
    const data = await postKolcategorySearchbyname({ distributorName: trimmed }) as any
    const list = (data?.distributorList || data?.data?.distributorList || []) as any[]
    return list.map(item => ({
      id: item.distributorId,
      name: item.distributorName,
      avatar: item.distributorLogo,
      followers: item.fansNumAccum,
      intro: item.distributorDescription,
      statusText: item.showStatus === 1 ? '展示中' : (item.showStatus === 0 ? '新增待生效' : '不满足条件'),
      value: item.distributorId,
      label: item.distributorName,
      extra: {
        id: item.distributorId,
        name: item.distributorName,
        avatar: item.distributorLogo,
        followers: item.fansNumAccum,
        intro: item.distributorDescription,
        statusText: item.showStatus === 1 ? '展示中' : (item.showStatus === 0 ? '新增待生效' : '不满足条件'),
      },
    }))
  }

  // 仅支持按 ID 搜索，支持逗号/空格分隔的多 ID
  const distributorIdList = trimmed
    .split(/[\s,，]+/)
    .map(s => s.trim())
    .filter(Boolean)

  const resp = (await postKolcategorySearchbyid({ request: { distributorIdList } })) as unknown as IPostKolcategorySearchbyidResponse
  console.log(resp)

  const list = resp?.resp?.distributorList || []

  return list.map(item => ({
    id: item.distributorId,
    name: item.distributorName,
    avatar: item.distributorLogo,
    followers: item.fansNumAccum,
    intro: item.distributorDescription,
    statusText: item.showStatus === 1 ? '展示中' : (item.showStatus === 0 ? '新增待生效' : '不满足条件'),
    value: item.distributorId,
    label: item.distributorName,
    extra: {
      id: item.distributorId,
      name: item.distributorName,
      avatar: item.distributorLogo,
      followers: item.fansNumAccum,
      intro: item.distributorDescription,
      statusText: item.showStatus === 1 ? '展示中' : (item.showStatus === 0 ? '新增待生效' : '不满足条件'),
    },
  }))
}
