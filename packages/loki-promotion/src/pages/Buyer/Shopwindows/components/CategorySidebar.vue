<template>
  <div class="category-sidebar">
    <!-- 类目头部 -->
    <div class="category-header">
      <div class="category-title">分类列表</div>
      <Select
        v-model="selectedFilters"
        :options="localFilterOptions"
        :disabled="false"
        :max-tag-count="2"
        multiple
        filterable
        clearable
        placeholder="筛选类目"
        style="width: 200px; margin-left: 12px;"
        @change="handleLocalFilterChange"
      />
      <Button
        type="light"
        :icon="Plus"
        @click="openAddPrimaryCategory"
      />
    </div>

    <!-- 类目列表 -->
    <div
      ref="listContainerRef"
      class="category-list-container"
      :class="{ 'dragging': isDragging }"
    >
      <div class="category-list">
        <Draggable
          v-model="displayedCategoryList"
          :disabled="isFilterActive"
          :animation="primaryAnimationMs"
          handle=".drag-handle"
          group="categories"
          item-key="id"
          :scroll="false"
          :ghost-class="'ghost'"
          :chosen-class="'chosen'"
          :drag-class="'drag'"
          @start="onPrimaryDragStart"
          @end="onPrimaryDragEnd"
        >
          <template #item="{ element: category }">
            <div
              :key="category.id"
              class="category-item primary"
            >
              <!-- 一级类目内容 -->
              <!-- @vue-ignore -->
              <div
                class="category-content"
                :class="{ 'has-children': category.subCategoryList && category.subCategoryList.length > 0 }"
                @click="handlePrimaryCategoryClick(category)"
              >
                <Icon
                  :icon="Drag"
                  class="drag-handle"
                  :class="{ disabled: isFilterActive }"
                  color="text-description"
                />

                <!-- 展开/收起箭头 -->
                <Icon
                  v-if="category.subCategoryList && category.subCategoryList.length > 0"
                  :icon="Down"
                  class="expand-icon"
                  :class="{ expanded: isCategoryExpanded(category.id) }"
                  color="text-description"
                />

                <span class="category-name">{{ category.title }}
                  <span
                    class="sub-count"
                  >·{{ category.totalKolUserCnt }}</span>
                </span>
                <!-- @vue-ignore -->
                <div
                  class="category-actions"
                  @click.stop.prevent="swallowClick"
                >
                  <Dropdown
                    auto-close
                    placement="bottom-end"
                  >
                    <Icon
                      :icon="More"
                      color="text-description"
                    />
                    <template #options>
                      <Option
                        label="重命名"
                        @click="openEditCategory(category)"
                      />
                      <Option
                        label="删除"
                        @click="confirmRemoveCategory(category)"
                      />
                    </template>
                  </Dropdown>
                  <Tooltip content="添加子类目">
                    <Button
                      type="light"
                      size="small"
                      :icon="Plus"
                      @click="openAddSubCategory(category)"
                    />
                  </Tooltip>
                </div>
              </div>

              <!-- 二级类目 -->
              <Transition name="expand">
                <div
                  v-if="category.subCategoryList && category.subCategoryList.length > 0 && isCategoryExpanded(category.id)"
                  :ref="el => setSubContainerRef(category.id, el as HTMLElement | null)"
                  class="sub-categories"
                >
                  <Draggable
                    :key="`sub-${category.id}-${category.subCategoryList?.length || 0}`"
                    :model-value="category.subCategoryList"
                    :disabled="isFilterActive"
                    :animation="subAnimationMs"
                    handle=".sub-drag-handle"
                    :group="`sub-${category.id}`"
                    item-key="id"
                    :clone="(item) => ({ ...item })"
                    :remove="() => {}"
                    @start="onSubDragStart(category)"
                    @end="onSubDragEnd(category)"
                    @update:model-value="(newList) => {
                      nextTick(() => {
                        category.subCategoryList = newList
                      })
                    }"
                  >
                    <template #item="{ element: subCategory }">
                      <div
                        :key="subCategory.id"
                        class="category-item secondary"
                        :class="{ 'selected': selectedCategoryId === subCategory.id }"
                      >
                        <!-- @vue-ignore -->
                        <div
                          class="category-content"
                          @click="handleSubCategoryClick(subCategory)"
                        >
                          <Icon
                            :icon="Drag"
                            class="sub-drag-handle"
                            :class="{ disabled: isFilterActive }"
                            color="text-description"
                          />
                          <span class="category-name">{{ subCategory.title }}
                            <span
                              v-if="subCategory.totalKolUserCnt !== undefined"
                              class="sub-count"
                            >·{{ subCategory.totalKolUserCnt }}</span>
                          </span>
                          <!-- @vue-ignore -->
                          <div
                            class="category-actions"
                            @click.stop.prevent="swallowClick"
                          >
                            <Button
                              type="light"
                              size="small"
                              :icon="Edit"
                              @click="openEditCategory(subCategory, category)"
                            />
                            <Button
                              type="light"
                              size="small"
                              :icon="Delete"
                              @click="confirmRemoveCategory(subCategory)"
                            />
                          </div>
                        </div>
                      </div>
                    </template>
                  </Draggable>
                </div>
              </Transition>
            </div>
          </template>
        </Draggable>
      </div>

      <!-- 类目编辑弹框 -->
      <CategoryEditModal
        ref="categoryEditModalRef"
        @confirm="handleCategorySubmit"
      />
    </div>
  </div></template>

<script setup lang="ts">
  import {
    Button,
    Dropdown,
    Icon,
    Modal,
    Option,
    Select,
    toast,
    Tooltip,
  } from '@xhs/delight'
  import {
    Delete,
    Down,
    Drag,
    Edit,
    More,
    Plus,
  } from '@xhs/delight/icons'
  import {
    computed,
    nextTick,
    onMounted,
    onUnmounted,
    ref,
    watch,
  } from 'vue'
  import Draggable from 'vuedraggable'
  import { useCategory } from '../hooks/useCategory'
  import type { Category, CategoryClickEvent, FilterOption } from '../types'
  import CategoryEditModal from './CategoryEditModal.vue'

  // 定义组件事件
  const emit = defineEmits<{(e: 'categoryClick', event: CategoryClickEvent): void
                            (e: 'categoryDeleted'): void
  }>()

  // 使用类目管理hook
  const {
    categoryList,
    selectedFilters,
    isFilterActive,
    selectedCategoryId,
    expandState,
    isCategoryExpanded,
    handleCategoryClick,
    addPrimaryCategory,
    addSubCategory,
    editCategory,
    removeCategory,
    updatePrimaryOrder,
    updateSubCategoryOrder,
    handleFilterChange,
    fetchCategoryList,
  } = useCategory() // 组件引用

  // 暴露刷新类目列表的方法给父组件调用
  const refreshCategoryList = async () => {
    await fetchCategoryList()
  }

  defineExpose({
    refreshCategoryList,
  })
  const categoryEditModalRef = ref()

  const swallowClick = (e: MouseEvent) => {
    e.stopPropagation()
    e.preventDefault()
  }

  // 基于类目列表生成筛选选项（保持层级结构）
  const localFilterOptions = computed(() => {
    const options: FilterOption[] = []

    categoryList.value.forEach(category => {
      // 添加一级类目
      const parentOption: FilterOption = {
        label: category.title || '',
        value: category.id,
      }

      // 如果有子类目，添加children属性
      if (category.subCategoryList && category.subCategoryList.length > 0) {
        parentOption.children = category.subCategoryList.map(child => ({
          label: child.title || '',
          value: child.id,
        }))
      }

      options.push(parentOption)
    })

    return options
  })

  // 显示的类目列表
  const displayedCategoryList = ref<Category[]>([])
  const primaryAnimationMs = ref(200)
  const subAnimationMs = ref(200)

  // 初始化显示列表
  const initDisplayedList = () => {
    displayedCategoryList.value = [...categoryList.value]
  }

  // 删除后：清理已不存在的筛选项，避免残留无效 ID 造成展示异常
  const collectValidIds = () => {
    const set = new Set<string | number>()
    categoryList.value.forEach(cat => {
      set.add(cat.id)
      if (Array.isArray(cat.subCategoryList)) {
        cat.subCategoryList.forEach(child => set.add(child.id))
      }
    })
    return set
  }
  const pruneSelectedFilters = () => {
    if (!selectedFilters.value || selectedFilters.value.length === 0) return
    const valid = collectValidIds()
    const next = selectedFilters.value.filter(id => valid.has(id))
    if (next.length !== selectedFilters.value.length) {
      selectedFilters.value = next
    }
  }

  // 筛选类目数据
  const filterCategoryList = () => {
    if (!isFilterActive.value) {
      displayedCategoryList.value = [...categoryList.value]
      return
    }

    const filterIds = selectedFilters.value
    const result: Category[] = []

    categoryList.value.forEach(category => {
      // 检查一级类目是否被选中
      const parentSelected = filterIds.includes(category.id)

      // 检查二级类目是否有被选中的
      let filteredChildren: Category[] = []
      if (category.subCategoryList) {
        filteredChildren = category.subCategoryList.filter(child => filterIds.includes(child.id))
      }

      // 如果一级类目被选中，显示整个类目及其所有子类目
      if (parentSelected) {
        result.push(category)
      } else if (filteredChildren.length > 0) {
        // 如果有子类目被选中，只显示被选中的子类目
        result.push({
          ...category,
          subCategoryList: filteredChildren,
        } as Category)
        // 自动展开有筛选结果的类目
        // eslint-disable-next-line no-param-reassign
        expandState.value[category.id] = true
      }
    })

    displayedCategoryList.value = result
  }

  // 监听类目列表变化，初始化显示列表
  watch(categoryList, () => {
    pruneSelectedFilters()
    if (isFilterActive.value) {
      filterCategoryList()
    } else {
      initDisplayedList()
    }
  }, { immediate: true })

  // 拖拽容器引用（使用内置滚动）
  const listContainerRef = ref<HTMLElement | null>(null)

  // 新增：删除前确认
  const confirmRemoveCategory = (category: Category) => {
    const hasChildren = Array.isArray(category.subCategoryList) && category.subCategoryList.length > 0
    const title = '是否确认删除？'
    const content = hasChildren
      ? '同步删除类目下的子类目和买手记录，删除后无法恢复。'
      : '同步删除类目下的买手记录，删除后无法恢复。'

    const warningModal = Modal.warning({
      title,
      content,
      onConfirm: async close => {
        warningModal.update({
          confirmButtonProps: { loading: true },
        })
        try {
          // 先记录删除前是否会影响当前选中的二级类目
          const preSelectedId = selectedCategoryId.value
          const willAffect = preSelectedId != null && (
            preSelectedId === category.id
            || (Array.isArray(category.subCategoryList) && category.subCategoryList.some(child => child.id === preSelectedId))
          )

          const success = await removeCategory(category)
          if (success && willAffect) {
            emit('categoryDeleted')
          }
          close?.()
        } finally {
          warningModal.update({
            confirmButtonProps: { loading: false },
          })
        }
      },
    })
  }

  // 记录每个父级的二级列表容器，用于越界校验
  const subContainerRefMap = new Map<string | number, HTMLElement>()
  const setSubContainerRef = (id: string | number, el: HTMLElement | null) => {
    if (el) subContainerRefMap.set(id, el)
    else subContainerRefMap.delete(id)
  }

  // 轻量越界判定（仅用于允许/拦截拖拽和在 End 时回滚）

  // 拖拽排序确认弹框文案
  const confirmTitle = '是否确认更改排序'
  const confirmContent = '结果会直接影响买手橱窗类目的展示顺序'

  // 保存拖拽前顺序用于回滚（一级）
  const originalPrimaryOrder = ref<Category[]>([])
  // 新增：拖拽状态管理
  const isDragging = ref(false)
  const dragStartTime = ref(0)
  const dragStartPerformanceTime = ref(0)

  // 新增：批量更新函数，减少响应式更新次数
  const batchUpdate = (updater: () => void) => {
    // 使用 nextTick 确保在下一个 tick 中批量更新
    nextTick(() => {
      updater()
    })
  }

  const isSameOrder = (a: Category[], b: Category[]) => (
    a.length === b.length && a.every((item, idx) => item.id === b[idx]?.id)
  )

  // 排序后刷新类目列表，并根据筛选状态更新展示数据
  // const refreshCategoryData = async () => {
  //   await fetchCategoryList()
  //   if (isFilterActive.value) {
  //     filterCategoryList()
  //   } else {
  //     initDisplayedList()
  //   }
  // }

  const onPrimaryDragStart = () => {
    if (isFilterActive.value) return

    originalPrimaryOrder.value = [...displayedCategoryList.value]
    isDragging.value = true
    dragStartTime.value = Date.now()
    dragStartPerformanceTime.value = Date.now()
  }

  const onPrimaryDragEnd = () => {
    if (isFilterActive.value) return

    isDragging.value = false

    // 先禁用动画，减少大列表回流/重绘阻塞
    primaryAnimationMs.value = 0
    const before = [...originalPrimaryOrder.value]
    const after = [...displayedCategoryList.value]

    // 简化越界检测：由于无法获取拖拽过程中的坐标，直接进行排序确认
    // 如果需要更精确的越界检测，可以在拖拽过程中记录坐标

    if (isSameOrder(before, after)) {
      // 无变化则恢复动画并退出
      setTimeout(() => {
        primaryAnimationMs.value = 200
      }, 100)
      return
    }

    // 直接显示弹框，不使用 requestAnimationFrame 延迟
    const primaryModal = Modal.warning({
      title: confirmTitle,
      content: confirmContent,
      onConfirm: async close => {
        primaryModal.update({
          confirmButtonProps: { loading: true },
        })
        try {
          // 先关闭弹框，避免阻塞
          close?.()

          // 异步更新排序
          await updatePrimaryOrder(after)

          toast.success({ description: '排序成功' })
        } catch (error) {
          // 如果失败，回滚数据
          batchUpdate(() => {
            displayedCategoryList.value = before
          })

          toast.danger({ description: '排序失败，已回滚' })
        } finally {
          // 延迟恢复动画
          setTimeout(() => {
            primaryAnimationMs.value = 200
          }, 100)
        }
      },
      onCancel: close => {
        // 取消时回滚数据
        batchUpdate(() => {
          displayedCategoryList.value = before
        })

        close?.()
        // 延迟恢复动画
        setTimeout(() => {
          primaryAnimationMs.value = 200
        }, 100)
      },
      onClose: () => {
        // 关闭时回滚数据
        batchUpdate(() => {
          displayedCategoryList.value = before
        })

        // 延迟恢复动画
        setTimeout(() => {
          primaryAnimationMs.value = 200
        }, 100)
      },
    })
  }

  // 保存拖拽前子类目顺序用于回滚（按父级记）
  const subOriginalChildrenMap = ref<Record<string | number, Category[]>>({})

  const onSubDragStart = (category: Category) => {
    if (isFilterActive.value) return

    subOriginalChildrenMap.value[category.id] = category.subCategoryList ? [...category.subCategoryList] : []
    isDragging.value = true
    dragStartTime.value = Date.now()
    dragStartPerformanceTime.value = Date.now()
  }

  const onSubDragEnd = (parent: Category) => {
    if (isFilterActive.value) return

    isDragging.value = false

    // 先禁用动画，减少大列表回流/重绘阻塞
    subAnimationMs.value = 0
    const before = subOriginalChildrenMap.value[parent.id] || []
    const after = parent.subCategoryList ? [...parent.subCategoryList] : []

    // 简化越界检测：由于无法获取拖拽过程中的坐标，直接进行排序确认
    // 如果需要更精确的越界检测，可以在拖拽过程中记录坐标

    if (isSameOrder(before, after)) {
      setTimeout(() => {
        subAnimationMs.value = 200
      }, 100)
      return
    }

    // 直接显示弹框，不使用 requestAnimationFrame 延迟
    const subModal = Modal.warning({
      title: confirmTitle,
      content: confirmContent,
      onConfirm: async close => {
        subModal.update({
          confirmButtonProps: { loading: true },
        })
        try {
          // 先关闭弹框，避免阻塞
          close?.()

          // 异步更新排序
          await updateSubCategoryOrder(parent.id, after)

          toast.success({ description: '排序成功' })
        } catch (error) {
          // 如果失败，回滚数据
          batchUpdate(() => {
            // eslint-disable-next-line no-param-reassign
            parent.subCategoryList = before
          })

          toast.danger({ description: '排序失败，已回滚' })
        } finally {
          // 延迟恢复动画
          setTimeout(() => {
            subAnimationMs.value = 200
          }, 100)
        }
      },
      onCancel: close => {
        // 取消时回滚数据
        batchUpdate(() => {
          // eslint-disable-next-line no-param-reassign
          parent.subCategoryList = before
        })

        close?.()
        // 延迟恢复动画
        setTimeout(() => {
          subAnimationMs.value = 200
        }, 100)
      },
      onClose: () => {
        // 关闭时回滚数据
        batchUpdate(() => {
          // eslint-disable-next-line no-param-reassign
          parent.subCategoryList = before
        })

        // 延迟恢复动画
        setTimeout(() => {
          subAnimationMs.value = 200
        }, 100)
      },
    })
  }

  // 组件挂载时拉取类目列表
  onMounted(() => {
    fetchCategoryList()
  })

  // 组件卸载时清理防抖定时器
  onUnmounted(() => {
    // 移除不再使用的防抖定时器
  })

  // 重写筛选变化处理函数
  const handleLocalFilterChange = (filters: string[]) => {
    // 调用原始的筛选处理函数
    handleFilterChange(filters)
    // 重新筛选显示数据
    filterCategoryList()
  }

  // 处理一级类目点击
  const handlePrimaryCategoryClick = (category: Category) => {
    const event = handleCategoryClick(category, false)
    emit('categoryClick', event)
  }
  // 处理二级类目点击
  const handleSubCategoryClick = (subCategory: Category) => {
    const event = handleCategoryClick(subCategory, true)
    emit('categoryClick', event)
  }

  // 打开添加一级类目弹框
  const openAddPrimaryCategory = () => {
    categoryEditModalRef.value?.openAddPrimary()
  }

  // 打开添加二级类目弹框
  const openAddSubCategory = (category: Category) => {
    categoryEditModalRef.value?.openAddSub(category)
  }

  // 打开编辑类目弹框
  const openEditCategory = (category: Category, parentCategory?: Category) => {
    categoryEditModalRef.value?.openEdit(category, parentCategory)
  }

  // 处理类目提交
  const handleCategorySubmit = async (data: { title: string; parentId?: string; id?: string }) => {
    categoryEditModalRef.value?.setLoading(true)
    try {
      let ok = false
      if (data.id) {
        // 编辑模式
        ok = await editCategory({
          id: Number(data.id),
          title: data.title,
        } as Category)
      } else if (data.parentId) {
        // 添加二级类目
        const parentCategory = categoryList.value.find(cat => String(cat.id) === String(data.parentId))
        if (parentCategory) {
          ok = await addSubCategory(parentCategory, data.title)
        }
      } else {
        // 添加一级类目
        ok = await addPrimaryCategory(data.title)
      }

      if (ok) {
        categoryEditModalRef.value?.close()
      }
    } finally {
      categoryEditModalRef.value?.setLoading(false)
    }
  }

</script>

<style scoped lang="stylus">
.category-sidebar
  width 300px
  background white
  border-radius 12px
  padding 20px
  box-shadow 0 2px 8px rgba(0, 0, 0, 0.06)
  display flex
  flex-direction column

  .category-header
    margin-bottom 20px
    flex-shrink 0
    display flex
    align-items center
    justify-content space-between

    .category-title
      font-size 14px
      font-weight 500
      color #333
      line-height 1.4
      white-space nowrap

  .category-list-container
    flex 1
    overflow-y auto
    padding-right 4px
    // 修改：使用auto滚动行为，避免平滑滚动影响速度
    scroll-behavior auto
    // 扩大滚动触发区域
    scroll-padding 20px

    &::-webkit-scrollbar
      width 8px

    &::-webkit-scrollbar-track
      background transparent
      border-radius 4px

    &::-webkit-scrollbar-thumb
      background #d9d9d9
      border-radius 4px
      // 新增：拖拽时高亮滚动条
      transition background-color 0.2s ease

    &::-webkit-scrollbar-thumb:hover
      background #bfbfbf

    // 新增：拖拽时显示滚动提示
    &.dragging
      scrollbar-color #1677ff #f0f0f0

      &::-webkit-scrollbar-thumb
        background #1677ff

  .category-list
    .category-item
      margin-bottom 2px

      &.primary
        background transparent
        border none

        .category-content
          padding 12px 16px
          border-radius 8px
          transition all 0.2s ease

          &.has-children
            cursor pointer

            &:hover
              background #f8f9fa

      &.secondary
        margin-left 12px
        margin-bottom 1px
        background transparent
        border none
        cursor pointer

        .category-content
          padding 10px 16px
          border-radius 6px
          transition all 0.2s ease
          position relative

          &:hover
            background #f5f7fa

        &.selected
          .category-content
            background #e6f4ff
            color #1677ff

            .category-name
              color #1677ff
              font-weight 500

            &::before
              content ''
              position absolute
              left 0
              top 50%
              transform translateY(-50%)
              width 3px
              height 16px
              background #1677ff
              border-radius 2px

      .category-content
        display flex
        align-items center
        gap 8px
        position relative

        .drag-handle
          cursor move
          opacity 0.4
          transition opacity 0.2s

          &.disabled
            cursor not-allowed
            opacity 0.2

        .sub-drag-handle
          cursor move
          opacity 0.4
          transition opacity 0.2s

          &.disabled
            cursor not-allowed
            opacity 0.2

        .expand-icon
          cursor pointer
          transition transform 0.2s
          color #8c8c8c
          font-size 12px
          width 16px
          display flex
          justify-content center

        .expand-icon:not(.expanded)
          transform rotate(-90deg)

        .category-name
          flex 1
          min-width 0
          font-size 14px
          user-select none
          color #333
          line-height 1.4
          overflow hidden
          text-overflow ellipsis
          white-space nowrap

        .sub-count
          color #00000085

        .category-actions
          display flex
          gap 4px
          opacity 0
          transition opacity 0.2s
          align-items center

      &:hover
        .category-content
          .drag-handle,
          .sub-drag-handle
            opacity 0.6

          .category-actions
            opacity 1

    .sub-categories
      margin-top 2px
      overflow hidden
      padding-left 8px

// 展开/收起动画
.expand-enter-active,
.expand-leave-active
  transition all 0.3s ease
  transform-origin top

.expand-enter-from,
.expand-leave-to
  opacity 0
  transform scaleY(0)

.expand-enter-to,
.expand-leave-from
  opacity 1
  transform scaleY(1)

// 拖拽性能优化样式
.ghost
  opacity 0.5
  background #f0f0f0
  border 2px dashed #ccc

.chosen
  background #e6f7ff
  border 1px solid #91d5ff

.drag
  opacity 0.8
  transform rotate(5deg)
  box-shadow 0 4px 12px rgba(0, 0, 0, 0.15)
</style>
