<template>
  <Modal
    v-model:visible="visible"
    :title="modalTitle"
    :width="400"
    @cancel="handleCancel"
  >
    <Form
      ref="formRef"
      :model="form"
      :rules="formRules"
    >
      <FormItem
        name="title"
      >
        <Input
          v-model="form.title"
          placeholder="请输入类目名称"
          :max-length="5"
        />
      </FormItem>
    </Form>
    <template #footer>
      <Space justify="end">
        <Button @click="handleCancel">取消</Button>
        <Button
          type="secondary"
          :loading="loading"
          @click="handleConfirm"
        >{{ isEditMode ? '保存' : '确定' }}</Button>
      </Space>
    </template>
  </Modal>
</template>

<script setup lang="ts">
  import {
    Button,
    Form2 as Form,
    FormItem2 as FormItem,
    Input,
    Modal,
    Space,
  } from '@xhs/delight'
  import { computed, reactive, ref } from 'vue'
  import { CATEGORY_FORM_RULES } from '../constants'
  import type { Category } from '../types'

  interface CategoryEditData {
    id: string
    title: string
    parentId?: string // 编辑时保存父级ID用于接口调用
  }

  interface Emits {
    (e: 'confirm', data: { title: string; parentId?: string; id?: string }): void
    (e: 'cancel'): void
  }

  const emit = defineEmits<Emits>()

  // 组件状态
  const visible = ref(false)
  const editData = ref<CategoryEditData | null>(null)
  const loading = ref(false)

  // 供父组件控制loading状态
  const setLoading = (val: boolean) => {
    loading.value = val
  }

  // 表单引用
  const formRef = ref()

  // 表单数据
  const form = reactive({
    title: '',
  })

  // 表单验证规则
  const formRules = CATEGORY_FORM_RULES

  // 计算属性
  const isEditMode = computed(() => !!editData.value?.id)
  const modalTitle = computed(() => {
    if (isEditMode.value) {
      return editData.value?.parentId ? '重命名二级类目' : '重命名一级类目'
    }
    // 新建：根据是否带有 parentId 来判断新建一级/二级
    return editData.value?.parentId ? '新建二级类目' : '新建一级类目'
  })

  // 暴露的方法
  const openAddPrimary = () => {
    editData.value = null
    form.title = ''
    visible.value = true
  }

  const openAddSub = (parent: Category) => {
    editData.value = {
      id: '',
      title: '',
      parentId: String(parent.id), // 保存父级ID用于接口
    }
    form.title = ''
    visible.value = true
  }

  const openEdit = (category: Category, parent?: Category | null) => {
    editData.value = {
      id: String(category.id),
      title: category.title || '',
      parentId: parent ? String(parent.id) : undefined,
    }
    form.title = category.title || ''
    visible.value = true
  }

  // 处理取消
  const handleCancel = () => {
    visible.value = false
    form.title = ''
    editData.value = null
    emit('cancel')
  }

  // 处理确认
  const handleConfirm = async () => {
    loading.value = true
    try {
      await formRef.value?.validate()

      const data: { title: string; parentId?: string; id?: string } = {
        title: form.title,
      }
      if (editData.value?.parentId) {
        data.parentId = editData.value?.parentId
      }
      if (editData.value?.id) {
        data.id = editData.value?.id
      }

      emit('confirm', data)
    } catch (error) {
      // 表单验证失败
      loading.value = false
    }
  }

  // 暴露方法给父组件使用
  defineExpose({
    openAddPrimary,
    openAddSub,
    openEdit,
    // 提供关闭方法，便于父组件在提交成功后主动关闭
    close: handleCancel,
    setLoading,
  })
</script>
