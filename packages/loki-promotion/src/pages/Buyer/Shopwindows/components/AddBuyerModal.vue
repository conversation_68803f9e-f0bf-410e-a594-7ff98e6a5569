<template>
  <Modal
    v-model:visible="visible"
    title="添加买手"
    :width="800"
    style="min-width: 800px;"
    :mask-closable="false"
    :loading="submitLoading"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="add-buyer-modal">
      <Form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <FormItem
          label="买手信息"
          name="buyerIds"
          required
        >
          <div class="search-input-group">
            <Dropdown
              v-model="searchFieldType"
              auto-close
              placement="bottom-start"
              trigger="click"
            >
              <div class="search-field-selector">
                <span class="search-field-label">{{ currentSearchField.label }}</span>
                <Icon
                  :icon="Down"
                  :size="14"
                />
              </div>
              <template #options>
                <Option
                  v-for="option in searchFieldOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </template>
            </Dropdown>
            <Select
              v-model="formData.buyerIds"
              :placeholder="currentSearchField.placeholder"
              :options="buyerOptions"
              :loading="searchLoading"
              :remote="true"
              multiple
              multi-line
              :filter="handleRemoteSearch"
              clearable
              filterable
              style="flex: 1"
            >
              <template #option="{ option, active, subActive, disabled }">
                <div
                  class="custom-option"
                  :style="{
                    display: 'flex',
                    alignItems: 'center',
                    borderRadius: 'var(--size-radius-default)',
                    padding: 'var(--size-space-step-default)',
                    gap: 'var(--size-space-step-default)',
                    cursor: 'pointer',
                    background: 'var(--color-bg-fill-light)',
                    color: (active || subActive) ? 'var(--color-primary)' : 'var(--color-text-title)',
                    opacity: disabled ? 0.6 : 1,
                  }"
                >
                  <div
                    style="width: 40px; height: 40px; border-radius: 50%; flex-shrink: 0;"
                  >
                    <img
                      v-if="option.extra?.avatar || option.avatar"
                      :src="option.extra?.avatar || option.avatar"
                      :alt="option.extra?.name || option.name"
                      style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;"
                    >
                    <div
                      v-else
                      style="width: 100%; height: 100%; border-radius: 50%; background: #f0f0f0; display: flex; align-items: center; justify-content: center; font-size: 14px; font-weight: 500; color: #666;"
                    >
                      {{ (option.extra?.name || option.name || option.label)?.charAt(0) || '?' }}
                    </div>
                  </div>
                  <div
                    style="flex: 1; min-width: 0;"
                  >
                    <div style="display: block; line-height: 20px; margin-bottom: 2px; font-weight: 400; font-size: 14px; color: #333;">
                      {{ option.extra?.name || option.name || option.label }}
                    </div>
                    <div style="display: flex; gap: 12px; font-size: 12px; color: #666; line-height: 18px;">
                      <span>ID: {{ option.extra?.id || option.id || option.value }}</span>
                      <span>粉丝: {{ option.extra?.followers ?? option.followers ?? '-' }}</span>
                    </div>
                  </div>
                </div>
              </template>
            </Select>
          </div>
        </FormItem>

      </Form>

      <!-- 已选择的买手预览 -->
      <div
        v-if="selectedBuyers.length > 0"
        class="selected-buyer-preview"
      >
        <Text>已选择买手（{{ selectedBuyers.length }}）：</Text>
        <div class="selected-buyer-list">
          <div
            v-for="buyer in selectedBuyers"
            :key="buyer.id"
            class="selected-buyer-card"
          >
            <div class="buyer-avatar">
              <img
                v-if="buyer.avatar"
                :src="buyer.avatar"
                :alt="buyer.name"
                class="avatar-img"
              >
              <div
                v-else
                class="avatar-placeholder"
              >
                {{ buyer.name?.charAt(0) || '?' }}
              </div>
            </div>
            <div class="buyer-info">
              <div class="buyer-name">{{ buyer.name }}</div>
              <div class="buyer-details">
                <span class="buyer-id">ID: {{ buyer.id }}</span>
                <span class="buyer-followers">粉丝: {{ buyer.followers }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Modal>
</template>

<script setup lang="ts">
  import {
    Dropdown,
    Form2 as Form,
    FormItem2 as FormItem,
    Icon,
    Modal,
    Option,
    Select,
    Text,
    toast,
  } from '@xhs/delight'
  import {
    Down,
  } from '@xhs/delight/icons'
  import {
    computed,
    ref,
    watch,
  } from 'vue'
  import { BuyerOption, searchAvailableBuyers } from '../services/index'

  // 表单数据类型
  interface FormData {
    buyerIds: string[]
  }

  // Props
  interface Props {
    show: boolean
  }

  // Emits
  interface Emits {
    (e: 'update:show', value: boolean): void
    (e: 'confirm', data: { buyerIds: string[]; buyerInfos: BuyerOption[] }): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 响应式数据
  const formRef = ref()
  const searchLoading = ref(false)
  const buyerOptions = ref<BuyerOption[]>([])
  const searchTimer = ref<ReturnType<typeof setTimeout>>()
  // 新增：缓存已选买手详情，避免切换搜索条件后预览丢失
  const selectedBuyerInfoMap = ref<Record<string, BuyerOption>>({})
  // 新增：确认按钮 loading
  const submitLoading = ref(false)

  // 搜索字段类型配置
  const searchFieldType = ref<'id' | 'name'>('name') // 恢复为默认按名称，保留可切换搜索
  const searchFieldOptions = [
    { label: 'ID', value: 'id' as const, placeholder: '请输入买手ID' },
    { label: '名称', value: 'name' as const, placeholder: '请输入买手名称' },
  ]

  // 当前搜索字段配置
  const currentSearchField = computed(() => searchFieldOptions.find(option => option.value === searchFieldType.value) || searchFieldOptions[1])

  // 表单数据
  const formData = ref<FormData>({
    buyerIds: [],
  })

  // 表单验证规则
  const formRules = {
    buyerIds: [
      { required: true, message: '请选择买手', trigger: 'change' as const },
    ],
  }

  // 弹框显示状态
  const visible = computed({
    get: () => props.show,
    set: (value: boolean) => emit('update:show', value),
  })

  // 重置表单
  const resetForm = () => {
    formData.value = {
      buyerIds: [],
    }
    buyerOptions.value = []
    selectedBuyerInfoMap.value = {}
    submitLoading.value = false
    formRef.value?.resetFields()
  }

  // 已选择的买手信息
  const selectedBuyers = computed(() => {
    const ids = formData.value.buyerIds || []
    if (ids.length === 0) return [] as BuyerOption[]
    // 从缓存中按当前选择顺序取详情
    return ids.map(id => selectedBuyerInfoMap.value[id]).filter(Boolean) as BuyerOption[]
  })

  // 监听选择变化：将当前 options 中能找到的详情写入缓存；移除未选中的缓存
  watch(
    () => formData.value.buyerIds.slice(),
    newIds => {
      const newSet = new Set(newIds)
      // 删除已取消选择的缓存项
      Object.keys(selectedBuyerInfoMap.value).forEach(id => {
        if (!newSet.has(id)) delete selectedBuyerInfoMap.value[id]
      })
      // 为新选择的 id 补齐详情
      newIds.forEach(id => {
        if (!selectedBuyerInfoMap.value[id]) {
          const found = buyerOptions.value.find(o => o.id === id)
          if (found) selectedBuyerInfoMap.value[id] = found
        }
      })
    },
    { deep: false },
  )

  // 监听 options 列表，若有已缓存的 id 出现更新，覆盖为最新数据
  watch(
    buyerOptions,
    list => {
      if (!list || list.length === 0) return
      const map = selectedBuyerInfoMap.value
      list.forEach(opt => {
        if (map[opt.id]) map[opt.id] = opt
      })
    },
    { deep: false },
  )

  // 远程搜索买手接口
  const searchBuyers = searchAvailableBuyers

  // 远程搜索处理
  const handleRemoteSearch = (keyword: string) => {
    // 清除之前的搜索定时器
    if (searchTimer.value) {
      clearTimeout(searchTimer.value)
    }

    // 防抖处理：必须有搜索条件才会触发请求
    searchTimer.value = setTimeout(async () => {
      searchLoading.value = true
      try {
        const trimmed = (keyword || '').trim()
        // 无搜索条件：不请求接口，且不展示已选项
        if (!trimmed) {
          buyerOptions.value = []
          return
        }
        // 仅当存在有效搜索条件时请求（根据下拉选择决定按ID或名称搜索）
        const results = await searchBuyers({ keyword: trimmed, searchType: searchFieldType.value })
        // 仅展示服务端返回的数据
        buyerOptions.value = results
      } catch (error) {
        console.error('搜索买手失败:', error)
        toast({
          type: 'warning',
          description: '搜索买手失败，请重试',
          duration: 2000,
        })
        // 失败时不展示已选项
        buyerOptions.value = []
      } finally {
        searchLoading.value = false
      }
    }, 500)
  }

  // 确认处理
  const handleConfirm = async () => {
    submitLoading.value = true
    try {
      await formRef.value?.validate()

      const selectedBuyerInfos = selectedBuyers.value
      if (!selectedBuyerInfos || selectedBuyerInfos.length === 0) {
        toast({
          type: 'warning',
          description: '请选择买手',
          duration: 2000,
        })
        return
      }

      // 确保 buyerIds 与所选 buyerInfos 对齐（以所选项的 id 为准）
      formData.value.buyerIds = selectedBuyerInfos.map(b => b.id)

      emit('confirm', {
        buyerIds: formData.value.buyerIds,
        buyerInfos: selectedBuyerInfos,
      })

      // 重置表单
      resetForm()
    } catch (error) {
      console.error('表单验证失败:', error)
    } finally {
      submitLoading.value = false
    }
  }

  // 取消处理
  const handleCancel = () => {
    resetForm()
    emit('update:show', false)
  }

  // 监听弹框显示状态，重置表单
  watch(
    () => props.show,
    async newShow => {
      if (newShow) {
        // 打开时不调用接口，仅重置表单与选项
        resetForm()
      } else {
        resetForm()
      }
    },
  )
</script>

<style scoped lang="stylus">
:deep(.custom-option) {
  display: flex;
  align-items: center;
  border-radius: var(--size-radius-default);
  padding: var(--size-space-step-default);
  gap: var(--size-space-step-default);
  cursor: pointer;

  /* 与预览卡片保持一致的结构与样式（全局生效，适配下拉 Teleport） */
  .buyer-avatar {
    flex-shrink: 0;
    width: 40px;
    height: 40px;

    .avatar-img {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      object-fit: cover;
    }

    .avatar-placeholder {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background: #f0f0f0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: 500;
      color: #666;
    }
  }

  .buyer-info {
    flex: 1;
    min-width: 0;

    .buyer-name {
      display: block;
      line-height: 20px;
      margin-bottom: 2px;
      font-weight: 400;
      font-size: 14px;
      color: #333;
    }

    .buyer-details {
      display: flex;
      gap: 12px;
      font-size: 12px;
      color: #666;
      line-height: 18px;
    }
  }
}

.add-buyer-modal {
  .search-field-selector {
    height: 32px;
    width: 50px;
    display: flex;
    align-items: center;
    font-size: 12px;
    gap: 4px;
    padding: 0 8px;
    color: rgba(0, 0, 0, 0.45);
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
    white-space: nowrap;
    justify-content: space-between;
    background-color: rgba(0, 0, 0, 0.03);
    border-radius: 4px;
    .search-field-label {
      display: inline-block;
      width: 32px; // 固定标签宽度，避免切换抖动
    }
  }

  .search-input-group {
    display: flex;
    align-items: center;
  }

  :deep(.custom-option) {
    display: flex;
    align-items: center;
    border-radius: var(--size-radius-default);
    padding: var(--size-space-step-default);
    gap: var(--size-space-step-default);
    cursor: pointer;

    /* 与预览卡片保持一致的结构与样式 */
    .buyer-avatar {
      flex-shrink: 0;
      width: 40px;
      height: 40px;

      .avatar-img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
      }

      .avatar-placeholder {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: #f0f0f0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: 500;
        color: #666;
      }
    }

    .buyer-info {
      flex: 1;
      min-width: 0;

      .buyer-name {
        display: block;
        line-height: 20px;
        margin-bottom: 2px;
        font-weight: 400;
        font-size: 14px;
        color: #333;
      }

      .buyer-details {
        display: flex;
        gap: 12px;
        font-size: 12px;
        color: #666;
        line-height: 18px;
      }
    }
  }

  .selected-buyer-preview {
    margin-top: 16px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;

    .selected-buyer-list {
      display: grid;
      gap: 8px;
      margin-top: 8px;
      max-height: 280px; // 默认高度上限，超出滚动
      overflow-y: auto;
      padding-right: 4px; // 预留滚动条空间，避免遮挡
    }

    .selected-buyer-card {
      display: flex;
      align-items: center;
      gap: var(--size-space-step-default);
      border-radius: var(--size-radius-default);
      padding: var(--size-space-step-default);
      background: var(--color-bg-fill-light);

      .buyer-avatar {
        flex-shrink: 0;
        width: 40px;
        height: 40px;

        .avatar-img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          object-fit: cover;
        }

        .avatar-placeholder {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          background: #f0f0f0;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          font-weight: 500;
          color: #666;
        }
      }

      .buyer-info {
        flex: 1;

        .buyer-name {
          font-size: 14px;
          font-weight: 400;
          color: #333;
          line-height: 20px;
          margin-bottom: 2px;
        }

        .buyer-details {
          display: flex;
          gap: 12px;
          font-size: 12px;
          color: #666;
          line-height: 18px;
        }
      }
    }
  }
}
</style>
