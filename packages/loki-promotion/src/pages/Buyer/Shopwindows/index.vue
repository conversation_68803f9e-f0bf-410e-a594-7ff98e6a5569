<template>
  <div class="buyer-shopwindows-page-container">
    <div class="buyer-shopwindows-page">
      <!-- 左侧类目区域 -->
      <CategorySidebar
        ref="categorySidebarRef"
        @category-click="handleCategoryClick"
        @category-deleted="handleCategoryDeleted"
      />

      <!-- 右侧内容区域 -->
      <BuyerTable
        ref="buyerTableRef"
        :refresh-sidebar-callback="refreshSidebarData"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import BuyerTable from './components/BuyerTable.vue'
  import CategorySidebar from './components/CategorySidebar.vue'
  import type { CategoryClickEvent } from './types'

  // 获取组件的引用
  const buyerTableRef = ref<InstanceType<typeof BuyerTable> | null>(null)
  const categorySidebarRef = ref<InstanceType<typeof CategorySidebar> | null>(null)

  // 处理类目点击事件
  const handleCategoryClick = async (event: CategoryClickEvent) => {
    if (buyerTableRef.value) {
      await buyerTableRef.value.handleCategoryClick(event)
    }
  }

  // 处理类目删除事件
  const handleCategoryDeleted = async () => {
    if (buyerTableRef.value) {
      // 通知表格清除数据，传空的类目id
      await buyerTableRef.value.clearCategorySelection()
    }
  }

  // 刷新sidebar数据的方法
  const refreshSidebarData = async () => {
    if (categorySidebarRef.value) {
      // 调用CategorySidebar组件的刷新方法
      await categorySidebarRef.value.refreshCategoryList()
    }
  }
</script>

<style scoped lang="stylus">
.buyer-shopwindows-page {
  flex: 1;
  display: flex;
  gap: 20px;
  overflow-x: hidden;
}
.buyer-shopwindows-page-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.buyer-shopwindows-page-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  margin-left: 16px;
}
</style>
