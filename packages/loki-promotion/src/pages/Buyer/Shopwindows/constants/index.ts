import type { Category, FilterOption } from '../types'

// 默认分页参数（对齐后端）
export const DEFAULT_PAGINATION = {
  page: 1,
  pageSize: 20,
  total: 0,
}

// 展示状态选项（对齐后端：0 不可展示，1 可展示）
export const STATUS_OPTIONS: FilterOption[] = [
  { label: '展示中', value: 1 },
  { label: '新增待生效', value: 0 },
  { label: '不满足条件', value: 11 },
]

// 默认类目数据（结构对齐：使用 title/subCategoryList，占位）
export const DEFAULT_CATEGORIES: Category[] = [
  {
    id: 1,
    title: '默认分类',
    priority: 0,
    level: 1,
    parentId: 0,
    subCategoryList: [
      {
        id: 11, title: '默认子类目A', priority: 0, level: 2, parentId: 1, subCategoryList: [], totalKolUserCnt: 0,
      },
      {
        id: 12, title: '默认子类目B', priority: 0, level: 2, parentId: 1, subCategoryList: [], totalKolUserCnt: 0,
      },
    ],
    totalKolUserCnt: 0,
  },
]

// 表单验证规则
export const CATEGORY_FORM_RULES = {
  title: [
    { required: true, message: '请输入类目名称' },
    { max: 5, message: '类目名称最多5个字符' },
  ],
}

// 拖拽配置
export const DRAG_OPTIONS = {
  animation: 200,
  ghostClass: 'drag-ghost',
  dragClass: 'drag-active',
}
