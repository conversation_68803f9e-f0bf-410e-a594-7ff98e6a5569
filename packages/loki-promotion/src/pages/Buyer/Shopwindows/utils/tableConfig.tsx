import { Text } from '@xhs/delight'
import type { TableRow } from '../types'

// 将可能为 JSON 字符串的数组安全转换为可读字符串
const toReadableList = (value: unknown): string => {
  if (value == null || value === '') return '-'
  if (Array.isArray(value)) {
    return (value as (string | number)[]).filter(Boolean).join('/') || '-'
  }
  if (typeof value === 'string') {
    const trimmed = value.trim()
    if (trimmed.startsWith('[') && trimmed.endsWith(']')) {
      try {
        const arr = JSON.parse(trimmed)
        if (Array.isArray(arr)) {
          return (arr as (string | number)[]).filter(Boolean).join('/') || '-'
        }
      } catch (_) {
        // ignore JSON parse error and fall through
      }
    }
    return value
  }
  return String(value)
}

// 表格列配置
export const createTableColumns = (
  editBuyer: (record: TableRow | null | undefined) => void,
  deleteBuyer: (record: TableRow) => void,
) => [
  {
    title: '买手信息',
    dataIndex: 'distributorName',
    key: 'distributorName',
    minWidth: 400,
    render: (e: any) => {
      const row = e?.rowData as TableRow
      const name = row?.distributorName || '-'
      const location = row?.distributorLocation || ''
      const rawMainCate = (row as any)?.mianGoodsFirstCategoryName
      const mainCate = Array.isArray(rawMainCate) ? (rawMainCate.filter(Boolean).join('/')) : (rawMainCate || '-')
      const contentCate = toReadableList((row as any)?.taxonomynameTagSetOnline)
      const userId = row?.distributorId || '-'
      const avatar = row?.distributorLogo
      const firstChar = (name || '').charAt(0) || '?'
      return (
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <div style={{
            width: '40px', height: '40px', borderRadius: '50%', overflow: 'hidden', flexShrink: 0,
          }}>
            {avatar ? (
              <img src={avatar} alt={name} style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
            ) : (
              <div style={{
                width: '100%', height: '100%', borderRadius: '50%', background: '#f0f0f0', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#666', fontSize: '14px', fontWeight: 500,
              }}>{firstChar}</div>
            )}
          </div>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '6px' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Text link onClick={() => row?.distributorId && window.open(`${window.location.origin}/buyer/kol/detail/${row.distributorId}`, '_blank', 'noopener,noreferrer')}>{name}</Text>
              {location ? <Text type="description">{location}</Text> : null}
            </div>
            <div style={{
              display: 'flex', gap: '16px', fontSize: '12px', color: '#8C8C8C',
            }}>
              <span>主推类目：{mainCate || '-'}</span>
            </div>
            <div style={{
              display: 'flex', gap: '16px', fontSize: '12px', color: '#8C8C8C',
            }}>
              <span>内容类目：{contentCate || '-'}</span>
            </div>
            <div style={{ fontSize: '12px', color: '#8C8C8C' }}>
              <span>用户ID：{userId || '-'}</span>
            </div>
          </div>
        </div>
      )
    },
  },
  {
    title: '账号粉丝',
    dataIndex: 'fansNumAccum',
    key: 'fansNumAccum',
    align: 'center' as const,
    minWidth: 120,
  },
  {
    title: '橱窗商品数',
    dataIndex: 'itemCnt',
    key: 'itemCnt',
    align: 'center' as const,
    minWidth: 120,
  },
  {
    title: '买手简介',
    dataIndex: 'distributorDescription',
    key: 'distributorDescription',
    minWidth: 300,
  },
  // 新增：展示状态列
  {
    title: '展示状态',
    dataIndex: 'showStatus',
    key: 'showStatus',
    align: 'center' as const,
    minWidth: 140,
    render: (e: any) => {
      const rowData = e?.rowData as TableRow
      const status = rowData?.showStatus
      const map: Record<number, { text: string; color: string }> = {
        1: { text: '展示中', color: '#52C41A' }, // 绿
        0: { text: '新增待生效', color: '#FA8C16' }, // 橙
        11: { text: '不满足条件', color: '#D9D9D9' }, // 灰
      }
      const info = (status !== undefined && map[status]) || { text: '-', color: '#D9D9D9' }
      return (
        <div style={{
          display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '6px',
        }}>
          <span style={{
            width: '8px', height: '8px', borderRadius: '50%', backgroundColor: info.color,
          }} />
          <Text>{info.text}</Text>
        </div>
      )
    },
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    fixed: 'right' as const,
    align: 'center' as const,
    minWidth: 160,
    render: (e: any) => {
      const rowData = e?.rowData as TableRow
      return (
        <div style={{ display: 'flex', gap: '10px' }}>
          <Text link onClick={() => editBuyer(rowData)}>编辑</Text>
          <Text link onClick={() => deleteBuyer(rowData)}>删除</Text>
        </div>
      )
    },
  },
]
