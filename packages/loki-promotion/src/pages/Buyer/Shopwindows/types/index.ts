import { ICategoryList } from '~/services/buyer/edith_get_kolcategory_query'

// 类目相关类型（对齐后端：使用 title 与 subCategoryList）
export type Category = ICategoryList

// 本地表格行类型（对齐后端 distributorList 字段）
export interface IDistributorList {
  kolCategoryUserId: number
  distributorId: string
  distributorName: string
  distributorLocation?: string
  mianGoodsFirstCategoryName?: string
  taxonomynameTagSetOnline?: string
  distributorLogo?: string
  fansNumAccum?: number
  itemCnt?: number
  distributorDescription?: string
  showStatus?: number
}

// 类目展开状态类型
export interface CategoryExpandState {
  [categoryId: string | number]: boolean
}

// 搜索参数类型（对齐后端查询买手列表 IRequest）
export interface SearchParams {
  kolCategoryId?: number
  level?: number
  distributorIdList?: string[]
  kolName?: string
  showStatus?: number[]
  orderBy?: string
  orderType?: string
}

// 表格行数据类型（对齐后端 distributorList）
export type TableRow = IDistributorList

// 分页参数类型（对齐后端命名）
export interface PaginationParams {
  page: number
  pageSize: number
  total: number
}

// 表单数据类型（类目编辑，仅保留名称字段）
export interface CategoryFormData {
  title: string
}

// 筛选选项类型
export interface FilterOption {
  label: string
  value: string | number
  children?: FilterOption[]
}

// 类目点击事件类型
export interface CategoryClickEvent {
  category: Category
  isSubCategory: boolean
  firstKolCategoryId?: number
}
