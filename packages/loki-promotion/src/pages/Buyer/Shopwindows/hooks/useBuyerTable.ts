import { Modal, Text, toast } from '@xhs/delight'
import {
  computed,
  h,
  nextTick,
  reactive,
  ref,
  watch,
} from 'vue'

import * as XLSX from 'xlsx'
import { DEFAULT_PAGINATION, STATUS_OPTIONS } from '../constants'
import * as buyerService from '../services/index'
import type {
  CategoryClickEvent,
  PaginationParams,
  SearchParams, TableRow,
} from '../types'
// 使用 SheetJS 解析表格
// eslint-disable-next-line import/no-duplicates

export function useBuyerTable(refreshSidebarCallback?: () => Promise<void>) {
  const loading = ref(false)
  const tableData = ref<TableRow[]>([])
  // 支持跨页多选：使用持久化集合保存所有页的已选主键（kolCategoryUserId）
  const selectedIdSet = reactive(new Set<number>())
  // 当前页在表格上展示的选中 keys（用于 v-model:selected）
  const selectedRows = ref<number[]>([])
  const tableKey = ref(0) // 用于强制重新渲染表格

  // 新增：保存当前选中二级类目的所属一级类目ID
  const firstKolCategoryId = ref<number | undefined>(undefined)

  // Table 行选择配置（Delight Table）
  const rowSelection = ref({
    getCheckboxProps: () => ({}),
    onSelect: (keys: number[]) => {
      handleSelectionChange(keys)
    },
  })

  // 搜索字段类型配置
  const searchFieldType = ref<'id' | 'name'>('id') // 默认搜索ID，对齐后端
  const searchFieldOptions = [
    { label: 'ID', value: 'id' as const, placeholder: '多个ID通过换行分隔' },
    { label: '名称', value: 'name' as const, placeholder: '请输入用户名称' },
  ]

  // 当前搜索字段配置
  const currentSearchField = computed(() => searchFieldOptions.find(option => option.value === searchFieldType.value) || searchFieldOptions[0])

  // 搜索参数（对齐后端）
  const searchParams = reactive<Partial<SearchParams>>({
    kolCategoryId: undefined,
    level: 2,
    distributorIdList: undefined,
    kolName: '',
    showStatus: [],
    orderBy: undefined,
    orderType: undefined,
  })

  // 是否已选择二级类目
  const isCategorySelected = computed(() => !!searchParams.kolCategoryId)

  // 分页参数（对齐后端）
  const pagination = reactive<PaginationParams>({ ...DEFAULT_PAGINATION })

  // 重置搜索
  const resetSearch = () => {
    searchParams.kolName = ''
    searchParams.distributorIdList = undefined
    searchParams.showStatus = [];
    (searchParams as any).searchValue = ''
  }

  // 完全重置搜索（包括类目）
  const resetAllSearch = () => {
    resetSearch()
    searchParams.kolCategoryId = undefined
    firstKolCategoryId.value = undefined
  }

  // 将输入的字符串解析为ID数组
  const parseIdList = (value: string | undefined) => {
    if (!value) return undefined
    const arr = value
      .replace(/\r?\n+/g, ',')
      .split(',')
      .map(v => v.trim())
      .filter(Boolean)
    return arr.length ? arr : undefined
  }

  // 根据当前页数据回填选中 keys
  const syncCurrentPageSelectionFromSet = () => {
    const currentPageIds = new Set((tableData.value || []).map(r => r.kolCategoryUserId))
    selectedRows.value = Array.from(selectedIdSet).filter(id => currentPageIds.has(id))
  }

  // 清空所有已选
  const clearAllSelection = () => {
    selectedIdSet.clear()
    selectedRows.value = []
  }

  // 搜索数据（正常分页，每次覆盖数据）
  const handleSearch = async (resetPage = true) => {
    if (resetPage) {
      pagination.page = 1
      pagination.total = 0
      tableData.value = []
      // 重置所有页的已选
      clearAllSelection()
    }

    // 如果未选择二级类目，不触发请求，直接清空数据并返回
    if (!searchParams.kolCategoryId) {
      toast.warning({ description: '请先选择二级类目', duration: 2000 })
      if (resetPage) {
        tableData.value = []
        clearAllSelection()
        tableKey.value += 1
      }
      pagination.total = 0
      return
    }

    loading.value = true

    try {
      // 根据搜索字段类型构造搜索参数
      const distributorIdList = searchFieldType.value === 'id' ? parseIdList((searchParams as any).searchValue || '') : undefined
      const kolName = searchFieldType.value === 'name' ? ((searchParams as any).searchValue || '') : ''

      const result = await buyerService.searchBuyers({
        kolCategoryId: searchParams.kolCategoryId,
        level: 2,
        distributorIdList,
        kolName,
        showStatus: searchParams.showStatus,
        page: pagination.page,
        pageSize: pagination.pageSize,
      })

      tableData.value = result.list
      // 回填当前页选中状态（跨页多选）
      syncCurrentPageSelectionFromSet()
      // 更新 tableKey 强制重新渲染
      tableKey.value += 1
      await nextTick()

      pagination.total = result.total
    } catch (error) {
      console.error('搜索失败:', error)
      toast.danger({ description: error.message })
    } finally {
      loading.value = false
    }
  }

  // 页码变化
  const onPageChange = async (page: number) => {
    pagination.page = page
    await handleSearch(false)
  }

  // 每页条数变化
  const onPageSizeChange = async (size: number) => {
    pagination.pageSize = size
    pagination.page = 1
    await handleSearch(true)
  }

  // Excel/CSV 导入处理（解析 -> 批量提交）
  const handleExcelImport = async () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.xlsx,.xls,.csv'
    input.onchange = async e => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (!file) return

      loading.value = true

      try {
        let buyerIds: string[] = []

        // 解析阶段
        try {
          // 读取为 ArrayBuffer
          const arrayBuffer = await file.arrayBuffer()
          // 解析工作簿
          const workbook = XLSX.read(arrayBuffer)
          if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
            throw new Error('文件无有效工作表')
          }
          const worksheet = workbook.Sheets[workbook.SheetNames[0]]

          // 优先尝试解析为对象（带表头）
          const rowsAsObjects = XLSX.utils.sheet_to_json<Record<string, any>>(worksheet, { defval: '' })

          if (rowsAsObjects.length > 0) {
            // 智能识别列名
            const first = rowsAsObjects[0]
            const candidateKeys = ['buyerId', 'id', '买手ID', '买手id', '用户ID', '用户id']
            const key = candidateKeys.find(k => Object.prototype.hasOwnProperty.call(first, k))
            if (key) {
              buyerIds = rowsAsObjects
                .map(r => String((r as any)[key]).trim())
                .filter(v => !!v)
            }
          }

          // 若未识别到表头列，退化为按首列提取（header:1 数组）
          if (buyerIds.length === 0) {
            const rowsAsAoA = XLSX.utils.sheet_to_json<any[]>(worksheet, { header: 1, defval: '' }) as any[]
            buyerIds = (rowsAsAoA || [])
              .map((r: any) => (Array.isArray(r) ? String(r[0]).trim() : ''))
              .filter(v => !!v && v.toLowerCase() !== 'buyerid' && v.toLowerCase() !== 'id')
          }

          // 去重
          buyerIds = Array.from(new Set(buyerIds))

          if (buyerIds.length === 0) {
            throw new Error('未识别到有效的买手ID')
          }

          if (!searchParams.kolCategoryId) {
            throw new Error('请选择二级类目后再导入')
          }
        } catch (parseErr: any) {
          toast.warning({ description: parseErr?.message || String(parseErr) })
          return
        }

        // 提交阶段
        try {
          await buyerService.batchAddBuyersToConfig({
            buyerIds,
            kolCategoryId: Number(searchParams.kolCategoryId),
            level: 2,
          })
        } catch (apiErr: any) {
          toast.danger({ description: apiErr?.message || String(apiErr) })
          return
        }

        // 刷新数据（重置分页并清空选择）
        await handleSearch()
        // 刷新sidebar数据
        if (refreshSidebarCallback) {
          await refreshSidebarCallback()
        }
        // 成功提示
        toast({ type: 'success', description: `成功导入 ${buyerIds.length} 位买手`, duration: 2000 })
      } catch (error: any) {
        console.error('导入失败:', error)
        toast.danger({ description: error?.message || String(error) })
      } finally {
        loading.value = false
      }
    }
    input.click()
  }

  // 批量删除处理（使用跨页已选集合）
  const handleBatchDelete = async () => {
    const selectedIds = Array.from(selectedIdSet)
    if (selectedIds.length === 0) {
      toast.warning({ description: '请选择要删除的数据' })
      return
    }

    // 确认弹窗
    Modal.warning({
      title: '批量删除',
      content: h('div', {}, [
        h(Text, {}, { default: () => '删除后无法恢复记录' }),
        h('div', { style: 'margin-top: 8px; color: var(--delight-color-text-weak);' }, `覆盖商家数：${selectedIds.length}`),
      ]),
      onConfirm: async close => {
        try {
          loading.value = true
          await buyerService.batchDeleteBuyers(selectedIds)
          toast.success({ description: `已删除${selectedIds.length}条数据` })
          clearAllSelection()
          // 重新加载数据（重置分页）
          await handleSearch()
          // 刷新sidebar数据
          if (refreshSidebarCallback) {
            await refreshSidebarCallback()
          }
        } catch (error) {
          toast.danger({ description: error.message })
        } finally {
          loading.value = false
          close?.()
        }
      },
      onCancel: close => close?.(),
    })
  }

  // // 批量导出处理（暂无后端导出接口，保留占位）
  // const handleBatchExport = async () => {
  //   const count = selectedIdSet.value.size
  //   Modal.warning({
  //     title: '批量下载',
  //     content: h('div', {}, [
  //       h(Text, {}, { default: () => '是否确认下载全表数据？' }),
  //       h('div', { style: 'margin-top: 8px; color: var(--delight-color-text-weak);' }, `覆盖商家数：${count}`),
  //     ]),
  //     onConfirm: close => {
  //       // 暂无后端导出，保留占位
  //       toast.warning({ description: '暂不支持导出功能' })
  //       close?.()
  //     },
  //     onCancel: close => close?.(),
  //   })
  // }

  // 计算批量操作按钮状态（基于跨页已选集合）
  const batchOperationOptions = computed(() => {
    const selectedCount = selectedIdSet.size
    const options = [
      {
        label: 'EXCEL导入',
        value: 'excelImport',
        onClick: handleExcelImport,
        disabled: selectedCount > 0 || !searchParams.kolCategoryId, // 未选类目或有选中时禁用
      },
      {
        label: '删除',
        value: 'delete',
        onClick: handleBatchDelete,
        disabled: selectedCount === 0, // 没有选中时禁用
      },
      // {
      //   label: '下载',
      //   value: 'batchExport',
      //   onClick: handleBatchExport,
      //   disabled: false, // 启用下载，点击后弹确认框
      // },
    ]
    return options
  })

  // 处理行选择变化（更新跨页集合与当前页选中）
  function handleSelectionChange(currentPageKeys: unknown) {
    const currentPageIdSet = new Set((tableData.value || []).map(r => r.kolCategoryUserId))

    // 将入参规范化为数组，兼容 Array/Set/可迭代类型/异常值
    const currentKeysArr: number[] = Array.isArray(currentPageKeys)
      ? (currentPageKeys as number[])
      : currentPageKeys && typeof (currentPageKeys as any)[Symbol.iterator] === 'function'
        ? Array.from(currentPageKeys as Iterable<number>)
        : []

    // 移除本页中被取消选中的项
    for (const id of currentPageIdSet) {
      if (!currentKeysArr.includes(id) && selectedIdSet.has(id)) {
        selectedIdSet.delete(id)
      }
    }
    // 添加本页新选中的项
    for (const id of currentKeysArr) {
      selectedIdSet.add(id)
    }
    // 同步当前页展示
    selectedRows.value = currentKeysArr
  }

  // 处理类目点击事件
  const handleCategoryClick = async (event: CategoryClickEvent) => {
    if (event.isSubCategory) {
      // 清空搜索条件（保留类目ID)
      resetSearch()

      // 设置类目ID与所属一级类目ID
      searchParams.kolCategoryId = Number(event.category.id)
      firstKolCategoryId.value = event.firstKolCategoryId

      // 重新获取数据（重置分页并清空选中）
      await handleSearch(true)
    } else {
      // 一级类目点击只是展开/收起，不需要获取数据
    }
  }

  // 清空类目选择
  const clearCategorySelection = async () => {
    searchParams.kolCategoryId = undefined
    firstKolCategoryId.value = undefined
    await handleSearch(true)
  }

  // 刷新数据
  const handleRefresh = () => {
    resetSearch()
    handleSearch()
  }

  // 编辑买手（弹框数据）
  const editBuyer = (record: TableRow | null | undefined) => {
    if (!record) return null
    return {
      id: record.distributorId || '',
      name: record.distributorName || '',
      intro: record.distributorDescription || '',
      kolCategoryUserId: record.kolCategoryUserId,
    }
  }

  // 删除买手
  const deleteBuyer = async (record: TableRow) => {
    Modal.warning({
      title: '是否确认删除',
      content: h(Text, {}, { default: () => '删除后无法恢复记录' }),
      onConfirm: async close => {
        try {
          await buyerService.deleteBuyer(record.kolCategoryUserId)
          // 重新搜索数据（重置分页并清空选中）
          await handleSearch()
          // 刷新sidebar数据
          if (refreshSidebarCallback) {
            await refreshSidebarCallback()
          }
          toast({ type: 'success', description: '删除成功', duration: 2000 })
        } catch (error) {
          console.error('删除失败:', error)
          toast({ type: 'warning', description: error.message, duration: 2000 })
        }
        close?.()
      },
      onCancel: close => close?.(),
    })
  }

  // 处理新增买手（使用批量新增接口）
  const handleAddBuyer = async (data: { buyerId?: string; buyerIds?: string[]; buyerInfos?: { id: string; name: string; intro?: string }[] }) => {
    try {
      const ids: string[] = (Array.isArray(data.buyerIds) && data.buyerIds.length > 0)
        ? data.buyerIds
        : (data.buyerId ? [data.buyerId] : [])

      if (ids.length === 0) {
        toast({ type: 'warning', description: '未选择买手', duration: 2000 })
        return
      }

      if (!searchParams.kolCategoryId) {
        toast({ type: 'warning', description: '请先选择二级类目', duration: 2000 })
        return
      }

      await buyerService.batchAddBuyersToConfig({
        buyerIds: ids,
        kolCategoryId: Number(searchParams.kolCategoryId),
        level: 2,
      })

      // 重新加载表格数据（重置分页并清空选中）
      await handleSearch()

      // 刷新sidebar数据
      if (refreshSidebarCallback) {
        await refreshSidebarCallback()
      }

      // 显示成功提示
      toast({ type: 'success', description: `成功添加 ${ids.length} 位买手`, duration: 2000 })
    } catch (error) {
      console.error('添加买手失败:', error)
      toast({ type: 'warning', description: error.message, duration: 2000 })
    }
  }

  // 处理编辑买手简介
  const handleEditBuyerIntro = async (data: { id: string; intro: string }) => {
    try {
      // 在表格数据中找到对应行，获取 kolCategoryUserId
      const row = tableData.value.find(r => r.distributorId === data.id)
      if (!row) {
        toast({ type: 'warning', description: '未找到买手记录', duration: 2000 })
        return
      }
      await buyerService.updateBuyerIntro({
        kolCategoryUserId: row.kolCategoryUserId,
        distributorId: row.distributorId,
        distributorDescription: data.intro,
      })

      // 重新加载表格数据（不重置分页，保留跨页选择）
      await handleSearch(false)

      // 刷新sidebar数据
      if (refreshSidebarCallback) {
        await refreshSidebarCallback()
      }

      // 显示成功提示
      toast({ type: 'success', description: '简介更新成功', duration: 2000 })
    } catch (error) {
      console.error('更新简介失败:', error)
      toast({ type: 'warning', description: error.message, duration: 2000 })
    }
  }

  // 监听搜索字段类型变化
  watch(searchFieldType, (newValue, oldValue) => {
    if (newValue !== oldValue) {
      (searchParams as any).searchValue = ''
    }
  })

  // 已选总数（跨页）
  const selectedCount = computed(() => selectedIdSet.size)

  return {
    loading,
    tableData,
    searchParams,
    pagination,
    selectedRows, // 当前页选中 keys（用于表格 v-model）
    selectedCount, // 跨页总选中数量
    tableKey,
    rowSelection,
    batchOperationOptions,
    statusOptions: STATUS_OPTIONS,
    searchFieldType,
    searchFieldOptions,
    currentSearchField,
    resetSearch,
    resetAllSearch,
    handleSearch,
    handleCategoryClick,
    clearCategorySelection,
    handleRefresh,
    handleSelectionChange,
    editBuyer,
    deleteBuyer,
    handleAddBuyer,
    handleEditBuyerIntro,
    isCategorySelected,
    onPageChange,
    onPageSizeChange,
  }
}
