import { toast } from '@xhs/delight'
import { computed, ref } from 'vue'
import * as categoryService from '../services/index'
import type {
  Category,
  CategoryClickEvent,
  CategoryExpandState,
} from '../types'

export function useCategory() {
  const categoryList = ref<Category[]>([])
  const selectedFilters = ref<(string | number)[]>([])
  const expandState = ref<CategoryExpandState>({})
  const selectedCategoryId = ref<string | number | null>(null)

  // 是否正在筛选状态
  const isFilterActive = computed(() => selectedFilters.value.length > 0)

  // 获取类目列表
  const fetchCategoryList = async () => {
    try {
      const list = await categoryService.getCategoryList()
      categoryList.value = list
      return true
    } catch (error) {
      console.error('获取类目列表失败:', error)
      toast.danger({ description: error.message })
      return false
    }
  }

  // 切换一级类目展开/收起状态
  const toggleCategoryExpand = (categoryId: string | number) => {
    expandState.value[categoryId] = !expandState.value[categoryId]
  }

  // 检查类目是否展开
  const isCategoryExpanded = (categoryId: string | number) => expandState.value[categoryId] || false

  // 处理类目点击事件
  const handleCategoryClick = (category: Category, isSubCategory: boolean = false) => {
    if (isSubCategory) {
      // 二级类目点击 - 选中该类目并触发数据获取
      selectedCategoryId.value = category.id
      return {
        category,
        isSubCategory: true,
        firstKolCategoryId: Number(category.parentId),
      } as CategoryClickEvent
    }
    // 一级类目点击 - 切换展开状态
    toggleCategoryExpand(category.id)
    return {
      category,
      isSubCategory: false,
    } as CategoryClickEvent
  }

  // 添加一级类目
  const addPrimaryCategory = async (name: string) => {
    try {
      await categoryService.addCategory(name)
      toast.success({ description: '添加成功' })
      // 重新获取类目列表
      await fetchCategoryList()
      return true
    } catch (error) {
      console.error('添加一级类目失败:', error)
      toast.danger({ description: error.message })
      return false
    }
  }

  // 添加二级类目
  const addSubCategory = async (parentCategory: Category, name: string) => {
    try {
      await categoryService.addSubCategory(parentCategory.id, name)
      toast.success({ description: '添加成功' })
      // 重新获取类目列表
      await fetchCategoryList()
      return true
    } catch (error) {
      toast.danger({ description: error.message })
      return false
    }
  }

  // 编辑类目
  const editCategory = async (category: Category) => {
    try {
      await categoryService.updateCategory(category)
      toast.success({ description: '编辑成功' })
      // 重新获取类目列表
      await fetchCategoryList()
      return true
    } catch (error) {
      console.error('编辑类目失败:', error)
      toast.danger({ description: error.message })
      return false
    }
  }

  // 删除类目
  const removeCategory = async (category: Category) => {
    try {
      await categoryService.deleteCategory(category.id)
      toast.success({ description: '删除成功' })
      // 如果删除的是当前选中的类目，清空选中状态
      if (selectedCategoryId.value === category.id) {
        selectedCategoryId.value = null
      }

      // 重新获取类目列表
      await fetchCategoryList()
      return true
    } catch (error) {
      toast.danger({ description: error.message })
      return false
    }
  }

  // 新：更新一级类目排序，按后端要求仅上报一级ID数组
  const updatePrimaryOrder = async (orderedCategories: Category[]) => {
    try {
      categoryList.value = [...orderedCategories]
      const ids = orderedCategories.map(c => c.id)
      await categoryService.updatePrimaryOrder(ids)
      console.log('一级类目排序已保存', ids)
    } catch (error) {
      console.error('保存一级排序失败:', error)
      toast.danger({ description: error.message })
    }
  }

  // 新：更新二级类目排序，上报父ID与该父下的二级ID数组
  const updateSubCategoryOrder = async (parentId: string | number, orderedChildren: Category[]) => {
    try {
      const childIds = orderedChildren.map(c => c.id)
      await categoryService.updateSubCategoryOrder({ parentId, childIds })
      console.log('二级类目排序已保存', parentId, childIds)
    } catch (error) {
      console.error('保存二级排序失败:', error)
      toast.danger({ description: error.message })
    }
  }

  // 筛选变化处理
  const handleFilterChange = (values: (string | number)[]) => {
    selectedFilters.value = values
    console.log('筛选变化:', values)
  }

  return {
    categoryList,
    selectedFilters,
    isFilterActive,
    expandState,
    selectedCategoryId,
    fetchCategoryList,
    toggleCategoryExpand,
    isCategoryExpanded,
    handleCategoryClick,
    addPrimaryCategory,
    addSubCategory,
    editCategory,
    removeCategory,
    updatePrimaryOrder,
    updateSubCategoryOrder,
    handleFilterChange,
  }
}
