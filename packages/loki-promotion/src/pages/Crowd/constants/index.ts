export const USER_LIFECYCLE_OPTIONS = [
  {
    name: '潜新',
    value: 'potential',
    tooltip: '实时拿到用户订单信息，平台下单=0',
  },
  {
    name: '老客',
    value: 'old_full',
    tooltip: '实时拿到用户订单信息，平台下单＞0',
  },
  {
    name: '1/2单新客',
    value: 'new',
    tooltip: '实时拿到用户订单信息，平台下单=1or2，且最后一单发生在时间<=180天里',
  },
  {
    name: '1单新客',
    value: 'new_one_order',
    tooltip: '实时拿到用户订单信息，平台下单=1，且最后一单发生在时间<=180天里',
  },
  {
    name: '2单新客',
    value: 'new_two_order',
    tooltip: '实时拿到用户订单信息，平台下单=2，且最后一单发生在时间<=180天里',
  },
  {
    name: '3单+老客',
    value: 'old',
    tooltip: '实时拿到用户订单信息，平台下单≥3，且最后一单发生在时间<=180天里',
  },
  {
    name: '180天流失',
    value: 'lost',
    tooltip: '实时拿到用户订单信息，时间＞180天里，平台下单＞0，且时间<=180天里，平台下单=0',
  },
  {
    name: '直播潜新',
    value: 'live_stream_potential_new',
    tooltip: '实时拿到用户订单信息，判定from直播渠道的下单订单=0',
  },
  {
    name: '直播老客',
    value: 'live_stream_repeat_customer_old_full',
    tooltip: '实时拿到用户订单信息，判定from直播渠道的下单订单＞0',
  },
  {
    name: '直播1/2单新客',
    value: 'live_stream_one_or_two_order_new',
    tooltip: '实时拿到用户订单信息，判定from直播渠道的下单订单=1or2',
  },
  {
    name: '直播1单新客',
    value: 'live_stream_one_order_new',
    tooltip: '实时拿到用户订单信息，判定from直播渠道的下单订单=1',
  },
  {
    name: '直播2单新客',
    value: 'live_stream_two_orders_new',
    tooltip: '实时拿到用户订单信息，判定from直播渠道的下单订单=2',
  },
  {
    name: '直播3单+老客',
    value: 'live_stream_repeat_customer',
    tooltip: '实时拿到用户订单信息，判定from直播渠道的下单订单＞=3',
  },
  {
    name: '平台老直播潜新',
    value: 'platform_old_live_stream_potential_new',
    tooltip: '实时拿到用户订单信息，平台下单>0，且from 直播渠道的下单订单=0',
  },
  {
    name: '平台新直播潜新',
    value: 'platform_potential_new_live_stream_potential_new',
    tooltip: '实时拿到用户订单信息，平台下单=0，且from直播渠道的下单订单=0',
  },
]

// 兼容老数据
export const USER_LIFECYCLE_OPTIONS_SHOW_OLD = [
  {
    name: '潜新',
    value: 'potential',
    tooltip: '实时拿到用户订单信息，平台下单=0',
  },
  {
    name: '老客',
    value: 'old_full',
    tooltip: '实时拿到用户订单信息，平台下单＞0',
    disabled: true,
  },
  {
    name: '1/2单新客',
    value: 'new',
    tooltip: '实时拿到用户订单信息，平台下单=1or2，且最后一单发生在时间<=180天里',
  },
  {
    name: '1单新客',
    value: 'new_one_order',
    tooltip: '实时拿到用户订单信息，平台下单=1，且最后一单发生在时间<=180天里',
  },
  {
    name: '2单新客',
    value: 'new_two_order',
    tooltip: '实时拿到用户订单信息，平台下单=2，且最后一单发生在时间<=180天里',
  },
  {
    name: '3单+老客',
    value: 'old',
    tooltip: '实时拿到用户订单信息，平台下单≥3，且最后一单发生在时间<=180天里',
  },
  {
    name: '180天流失',
    value: 'lost',
    tooltip: '实时拿到用户订单信息，时间＞180天里，平台下单＞0，且时间<=180天里，平台下单=0',
  },
  {
    name: '直播潜新',
    value: 'live_stream_potential_new',
    tooltip: '实时拿到用户订单信息，判定from直播渠道的下单订单=0',
    disabled: true,
  },
  {
    name: '直播老客',
    value: 'live_stream_repeat_customer_old_full',
    tooltip: '实时拿到用户订单信息，判定from直播渠道的下单订单＞0',
    disabled: true,
  },
  {
    name: '直播1/2单新客',
    value: 'live_stream_one_or_two_order_new',
    tooltip: '实时拿到用户订单信息，判定from直播渠道的下单订单=1or2',
    disabled: true,
  },
  {
    name: '直播1单新客',
    value: 'live_stream_one_order_new',
    tooltip: '实时拿到用户订单信息，判定from直播渠道的下单订单=1',
    disabled: true,
  },
  {
    name: '直播2单新客',
    value: 'live_stream_two_orders_new',
    tooltip: '实时拿到用户订单信息，判定from直播渠道的下单订单=2',
    disabled: true,
  },
  {
    name: '直播3单+老客',
    value: 'live_stream_repeat_customer',
    tooltip: '实时拿到用户订单信息，判定from直播渠道的下单订单＞=3',
    disabled: true,
  },
  {
    name: '平台老直播潜新',
    value: 'platform_old_live_stream_potential_new',
    tooltip: '实时拿到用户订单信息，平台下单>0，且from 直播渠道的下单订单=0',
    disabled: true,
  },
  {
    name: '平台新直播潜新',
    value: 'platform_potential_new_live_stream_potential_new',
    tooltip: '实时拿到用户订单信息，平台下单=0，且from直播渠道的下单订单=0',
    disabled: true,
  },
]

export const USER_LIFECYCLE_OPTIONS_V2 = [
  {
    name: '潜新',
    value: 'potential',
    tooltip: '实时拿到用户订单信息，平台下单=0',
  },
  {
    name: '1/2单新客',
    value: 'new',
    tooltip: '实时拿到用户订单信息，平台下单=1or2，且最后一单发生在时间<=180天里',
  },
  {
    name: '1单新客',
    value: 'new_one_order',
    tooltip: '实时拿到用户订单信息，平台下单=1，且最后一单发生在时间<=180天里',
  },
  {
    name: '2单新客',
    value: 'new_two_order',
    tooltip: '实时拿到用户订单信息，平台下单=2，且最后一单发生在时间<=180天里',
  },
  // 1. 新客近7天， 下单天数为1：new_order1d_in7d
  {
    name: '新客近7天，下单天数为1',
    value: 'new_order1d_in7d',
    tooltip: '首单距今7天内的新客（1、2单）且近7天仅一天下单且当日无下单',
  },
  // 2. 新客近14天，下单天数为1：new_order1d_in14d
  {
    name: '新客近14天，下单天数为1',
    value: 'new_order1d_in14d',
    tooltip: '首单距今14天内的新客（1、2单）且近14天仅一天下单且当日无下单',
  },
  {
    name: '3单+老客',
    value: 'old',
    tooltip: '实时拿到用户订单信息，平台下单≥3，且最后一单发生在时间<=180天里',
  },
  {
    name: '180天流失',
    value: 'lost',
    tooltip: '实时拿到用户订单信息，时间＞180天里，平台下单＞0，且时间<=180天里，平台下单=0',
  },
]

export const USE_LIFECYCLE_OPTIONS_LIVE = [
  {
    name: '直播潜新',
    value: 'live_stream_potential_new',
    tooltip: '实时拿到用户订单信息，判定from直播渠道的下单订单=0',
  },
  {
    name: '直播老客',
    value: 'live_stream_repeat_customer_old_full',
    tooltip: '实时拿到用户订单信息，判定from直播渠道的下单订单＞0',
  },
  {
    name: '直播1/2单新客',
    value: 'live_stream_one_or_two_order_new',
    tooltip: '实时拿到用户订单信息，判定from直播渠道的下单订单=1or2',
  },
  {
    name: '直播1单新客',
    value: 'live_stream_one_order_new',
    tooltip: '实时拿到用户订单信息，判定from直播渠道的下单订单=1',
  },
  {
    name: '直播2单新客',
    value: 'live_stream_two_orders_new',
    tooltip: '实时拿到用户订单信息，判定from直播渠道的下单订单=2',
  },
  {
    name: '直播3单+老客',
    value: 'live_stream_repeat_customer',
    tooltip: '实时拿到用户订单信息，判定from直播渠道的下单订单＞=3',
  },
]
export const CROWD_STATUS_MAP = {
  0: '成功',
  1: '生成中',
}

export const CREATE_TYPE_MAP = {
  1: '规则',
  2: '上传文件',
  3: 'Hive表',
}
export const LIFE_CYCLES_DAVID_CROWDS_RELATION_OPTION = [
  { value: 'AND', label: '取交集' },
  { value: 'DIFFERENCE', label: '取差集（实时标签人群减去用户画像人群）' },
]
export const ORDER_DATA_END_DATE_OPTIONS = [
  {
    name: '近0天',
    value: 'order_end_day_0',
  },
  {
    name: '近1天',
    value: 'order_end_day_1',
  },
  {
    name: '近14天',
    value: 'order_end_day_14',
  },
  {
    name: '近15天',
    value: 'order_end_day_15',
  },
  {
    name: '近30天',
    value: 'order_end_day_30',
  },
  {
    name: '近31天',
    value: 'order_end_day_31',
  },
]
export const ORDER_DATE_OPTIONS = [
  {
    name: '今天',
    value: 'order_day_1',
  },
  {
    name: '近7天',
    value: 'order_day_7',
  },
  {
    name: '近14天',
    value: 'order_day_14',
  },
  {
    name: '近30天',
    value: 'order_day_30',
  },
  {
    name: '近31天',
    value: 'order_day_31',
  },
  {
    name: '近61天',
    value: 'order_day_61',
  },
  {
    name: '近91天',
    value: 'order_day_91',
  },
  {
    name: '近180天',
    value: 'order_day_180',
  },
]

export const VALID_ORDER_OPTIONS = [
  {
    name: '无有效订单',
    value: 'valid_order_0',
  },
  {
    name: '有效订单大于0',
    value: 'valid_order_gt_0',
  },
  {
    name: '有效订单大于3',
    value: 'valid_order_gt_3',
  },
]

// 新增: 解析规则与校验/过滤工具
export const extractDayNumber = (value?: string): number => {
  if (!value) return NaN
  const match = value.match(/_day_(\d+)$/)
  return match ? Number(match[1]) : NaN
}

export const isOrderRangeValid = (orderEndDate?: string, orderDate?: string): boolean => {
  if (!orderEndDate || !orderDate) return true
  const endNum = extractDayNumber(orderEndDate)
  const dateNum = extractDayNumber(orderDate)
  if (Number.isNaN(endNum) || Number.isNaN(dateNum)) return true
  // 规则: orderEndDate 末尾数字 < ORDER_DATE_OPTIONS 末尾数字
  return endNum < dateNum
}

export const filterOrderDateOptionsByEndDate = (orderEndDate?: string) => {
  const endNum = extractDayNumber(orderEndDate)
  if (Number.isNaN(endNum)) return ORDER_DATE_OPTIONS
  return ORDER_DATE_OPTIONS.filter(option => extractDayNumber(option.value) > endNum)
}
