<template>
  <HeaderBreadcrumb :items="breadcrumbList" />
  <Spinner :spinning="loading">
    <Panel
      title="人群基础信息"
      style="flex-direction: column; gap: 10px; padding: 20px 40px 40px 40px"
    >
      <Form
        ref="formRef"
        :model="formValue"
        :disabled="formIsView"
        label-position="Left"
        label-width="200px"
      >
        <FormItem
          label="人群名称"
          name="name"
          :rules="[{ required: true, message: '请输入人群名称！' }]"
        >
          <Input
            v-model="formValue.name"
            placeholder="请输入活动名称"
            :max-length="30"
          />
        </FormItem>
        <FormItem
          label="实时标签-生命周期"
          name="label"
        >
          <div style="display: flex; flex-direction: column">
            <Select
              v-model="formValue.userLifeCycles"
              placeholder="请选择"
              :options="processedLifecycleOptions"
              filterable
              multiple
              @update:model-value="handleLifeCycleChange"
            >
              <template #optionContent="{ option, active, subActive }">
                <div
                  :class="[
                    'custom-option',
                    (active || subActive) && '--color-primary'
                  ]"
                >
                  <Text
                    color="current"
                    :style="{ fontSize: '14px' }"
                  >
                    {{ option.name }}
                  </Text>
                  <Tooltip
                    v-if="option.tooltip"
                    :content="option.tooltip"
                  >
                    <Icon
                      :icon="Help"
                      color="text-description"
                    />
                  </Tooltip>
                </div>
              </template>
            </Select>
            <div
              style="color: rgba(0,0,0,0.65); font-size: 14px; margin-top: 10px"
            >
              多选取并集生效
            </div>
          </div>
        </FormItem>
        <FormItem
          label="实时标签-直播生命周期"
          name="label"
        >
          <div style="display: flex; flex-direction: column">
            <Select
              v-model="formValue.userLiveLifeCycles"
              placeholder="请选择"
              :options="USE_LIFECYCLE_OPTIONS_LIVE"
              filterable
              multiple
            >
              <template #optionContent="{ option, active, subActive }">
                <div
                  :class="[
                    'custom-option',
                    (active || subActive) && '--color-primary'
                  ]"
                >
                  <Text
                    color="current"
                    :style="{ fontSize: '14px' }"
                  >
                    {{ option.name }}
                  </Text>
                  <Tooltip
                    v-if="option.tooltip"
                    :content="option.tooltip"
                  >
                    <Icon
                      :icon="Help"
                      color="text-description"
                    />
                  </Tooltip>
                </div>
              </template>
            </Select>
            <div
              style="color: rgba(0,0,0,0.65); font-size: 14px; margin-top: 10px"
            >
              多选取并集生效
            </div>
          </div>
        </FormItem>
        <FormItem
          label="实时标签-近期订单"
          name="recentOrders"
          description="与「实时标签-生命周期」取交集生效"
        >
          <div style="display: flex; gap: 20px">
            <Select
              v-model="formValue.orderEndDate"
              placeholder="请选择起始日期"
              :options="ORDER_DATA_END_DATE_OPTIONS"
              filterable
              clearable
              style="width: 160px;"
            />
            <span style="display: flex; align-items: center; height: 32px;">到</span>
            <Select
              v-model="formValue.orderDate"
              placeholder="请选择截止周期"
              :options="filteredOrderDateOptions"
              filterable
              clearable
              style="width: 160px;"
            />
            <Select
              v-model="formValue.orderCount"
              placeholder="请选择订单数量"
              :options="VALID_ORDER_OPTIONS"
              filterable
              clearable
            />
          </div>
        </FormItem>
        <FormItem
          label="用户画像平台"
          name="davidType"
          :rules="[{ required: true }]"
        >
          <RadioGroup
            v-model="formValue.davidType"
            :options="DAVID_TYPE_OPTION"
            @change="davidTypeChange"
          />
        </FormItem>
        <FormItem
          v-if="formValue.davidType === 'pkg'"
          label="人群包"
          name="davidCrowdIds"
          :rules="[{ required: true, message: '请添加人群包' }]"
        >
          <CrowdPack
            :update="updateCrowdPack"
            :init-list="initPackList"
            :disabled="formIsView"
          />
        </FormItem>
        <FormItem
          v-if="relationShow"
          label="实时标签/用户画像平台人群包生效关系"
          name="lifeCyclesDavidCrowdsRelation"
          :rules="[{ required: true, message: '请选择生效关系' }]"
        >
          <Select
            v-model="formValue.lifeCyclesDavidCrowdsRelation"
            placeholder="请选择"
            :options="LIFE_CYCLES_DAVID_CROWDS_RELATION_OPTION"
          />
        </FormItem>
        <FormItem
          label="实时标签-生效范围"
          name="frontierType"
        >
          <RadioGroup
            v-model="formValue.frontierType"
            :options="frontierTypeOptions"
          />
        </FormItem>
        <FormItem
          label="最终生效人群包"
        >
          <Text>{{ finalEffectiveCrowdPackage }}</Text>
        </FormItem>

      </Form>
    </Panel>
    <ButtonGroup :buttons="buttons" />
  </Spinner>
</template>

<script setup lang="tsx">
  import {
    ref, withDefaults, computed, onMounted, h, watch,
  } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import {
    Spinner, toast, Form2 as Form, FormItem2 as FormItem, Input, Select, RadioGroup, Tooltip, Icon, Modal, Text,
  } from '@xhs/delight'
  import { Help } from '@xhs/delight/icons'
  import Panel from '~/components/Panel/Index.vue'
  import ButtonGroup from '~/components/ButtonGroup/Index.vue'
  import CrowdPack from '../CrowdPack/CrowdPack.vue'
  import { FORM_TYPE, FORM_TYPE_TEXT } from '~/hooks/useFormilyType'
  import HeaderBreadcrumb from '~/components/HeaderBreadcrumb/Index.vue'
  import { getRuleDetail, createOrUpdateRule, searchCrowdUsage } from '~/services/compass'
  import {
    USER_LIFECYCLE_OPTIONS_V2, USE_LIFECYCLE_OPTIONS_LIVE, LIFE_CYCLES_DAVID_CROWDS_RELATION_OPTION, ORDER_DATE_OPTIONS, VALID_ORDER_OPTIONS, USER_LIFECYCLE_OPTIONS_SHOW_OLD, ORDER_DATA_END_DATE_OPTIONS, filterOrderDateOptionsByEndDate, isOrderRangeValid,
  } from '../constants'
  import { handleError } from '../../ActivityCenter/utils/handleError'
  import { useBroadcastChannel } from '~/hooks/useCrossPageCommunication'

  const props = withDefaults(defineProps<{
    formType: FORM_TYPE
  }>(), {})

  const formRef = ref()
  const formValue = ref({
    name: '',
    userLifeCycles: [],
    userLiveLifeCycles: [],
    davidType: 'none',
    davidCrowdIds: [],
    orderEndDate: '',
    orderDate: '',
    orderCount: '',
    lifeCyclesDavidCrowdsRelation: 'AND',
    frontierType: '',
  })

  const crowdList = ref([])

  const loading = ref(false)
  const router = useRouter()
  const route = useRoute()
  const initPackList = ref([])
  const formTypeText = computed(() => FORM_TYPE_TEXT[props.formType])
  const formIsView = computed(() => props.formType === FORM_TYPE.VIEW)
  const formIsEdit = computed(() => props.formType === FORM_TYPE.EDIT)
  const formIsCopy = computed(() => props.formType === FORM_TYPE.COPY)

  const frontierTypeOptions = [
    {
      label: '境内',
      value: 'domestic',
    },
    {
      label: '境外',
      value: 'abroad',
    },
  ]

  const relationShow = computed(() => {
    const hasLifeCycle = formValue.value.userLifeCycles?.length > 0
    const hasRecentOrders = formValue.value.orderDate && formValue.value.orderEndDate && formValue.value.orderCount
    return formValue.value.davidType === 'pkg' && (hasLifeCycle || hasRecentOrders)
  })

  watch(relationShow, v => {
    if (!v) {
      formValue.value.lifeCyclesDavidCrowdsRelation = 'AND'
    }
  })

  // 监听 orderEndDate 变化，若不满足规则则清空 orderDate
  watch(() => formValue.value.orderEndDate, () => {
    if (!isOrderRangeValid(formValue.value.orderEndDate, formValue.value.orderDate)) {
      formValue.value.orderDate = ''
    }
  })

  const { crowdId } = route.params

  const breadcrumbList = ref([
    { title: '人群列表', to: { name: 'CrowdList' } },
    { title: `${formTypeText.value}人群` },
  ])

  const DAVID_TYPE_OPTION = [
    { value: 'none', label: '无限制' },
    { value: 'pkg', label: '人群包' },
  ]

  const finalEffectiveCrowdPackage = computed(() => {
    // 获取实时标签名称（生命周期）
    const lifeCycleLabels = formValue.value.userLifeCycles
      .map(v => USER_LIFECYCLE_OPTIONS_V2.find(opt => opt.value === v)?.name)
      .filter(Boolean)

    // 获取实时标签名称（直播生命周期）
    const liveLifeCycleLabels = formValue.value.userLiveLifeCycles
      .map(v => USE_LIFECYCLE_OPTIONS_LIVE.find(opt => opt.value === v)?.name)
      .filter(Boolean)

    // 获取近期订单条件
    const orderDateLabel = ORDER_DATE_OPTIONS.find(opt => opt.value === formValue.value.orderDate)?.name
    const orderEndDateLabel = ORDER_DATA_END_DATE_OPTIONS.find(opt => opt.value === formValue.value.orderEndDate)?.name
    const orderCountLabel = VALID_ORDER_OPTIONS.find(opt => opt.value === formValue.value.orderCount)?.name
    const hasRecentOrders = formValue.value.orderDate && formValue.value.orderEndDate && formValue.value.orderCount

    // 获取人群包名称（添加davidType判断）
    const davidCrowdNames = formValue.value.davidType === 'pkg'
      ? crowdList.value.map(pack => pack.crowdName)
      : []

    // 构建实时标签组合（生命周期 或 近期订单）
    const realTimeConditions = []
    const realTimeParts = []
    if (lifeCycleLabels.length) realTimeParts.push(`（${lifeCycleLabels.join(' 或 ')}）`)
    if (liveLifeCycleLabels.length) realTimeParts.push(`（${liveLifeCycleLabels.join(' 或 ')}）`)
    if (hasRecentOrders) realTimeParts.push(`（${orderEndDateLabel}到${orderDateLabel} ${orderCountLabel}）`)

    if (realTimeParts.length === 1) {
      realTimeConditions.push(realTimeParts[0])
    } else if (realTimeParts.length > 1) {
      realTimeConditions.push(`{${realTimeParts.join(' 且 ')}}`)
    }

    const realTimeStr = realTimeConditions.join(' 且 ')
    const davidStr = davidCrowdNames.length > 0 ? `（${davidCrowdNames.join(' 或 ')}）` : ''

    if (formValue.value.lifeCyclesDavidCrowdsRelation === 'AND') {
      if (!realTimeStr && !davidStr) return '无'
      if (!realTimeStr) return davidStr
      if (!davidStr) return realTimeStr
      return `${realTimeStr} 且 ${davidStr}`
    }

    if (!realTimeStr) return '无'
    if (!davidStr) return realTimeStr
    return `${realTimeStr} 去除 ${davidStr}`
  })

  const davidTypeChange = v => {
    if (v === 'none') {
      formValue.value.davidCrowdIds = []
    }
    if (v === 'pkg') {
      formValue.value.davidCrowdIds = initPackList.value.map(items => items.crowdId)
    }
  }

  const { checkAndSendInstanceNode } = useBroadcastChannel({
    name: 'SOP-channel',
  })

  const updateCrowdPack = crowdpack => {
    crowdList.value = crowdpack
    initPackList.value = crowdpack

    if (!formValue.value.name) {
      formValue.value.name = crowdpack[0].crowdName
    }
    formValue.value.davidCrowdIds = crowdpack.map(items => items.crowdId)
    formRef.value.validateField('davidCrowdIds')
  }

  // 点击取消
  const clickCancel = () => {
    router.go(-1)
  }

  // 提交
  const submit = async close => {
    try {
      loading.value = true
      const params = { ...formValue.value }
      if (params.davidType === 'none') {
        params.davidCrowdIds = []
      }
      if (!relationShow.value) {
        delete params.lifeCyclesDavidCrowdsRelation
      }

      delete params.davidType
      if (formIsEdit.value) {
        params.crowdRuleId = crowdId
        await createOrUpdateRule(params)
      } else {
        const res = await createOrUpdateRule(params)
        checkAndSendInstanceNode([res?.crowdRuleId])
      }
      toast.success({ description: '保存成功', closeable: true })
      close?.()
      router.go(-1)
    } catch (err: any) {
      handleError(err, { title: '保存失败' })
    } finally {
      loading.value = false
    }
  }

  const checkUsage = async () => {
    if (crowdId && formIsEdit.value) {
      try {
        const res = await searchCrowdUsage({ crowdId })
        if (res?.desc) {
          Modal.warning({
            title: '是否确定保存',
            centered: true,
            content: h(
              Text,
              {},
              {
                default: () => res.desc,
              },
            ),
            async onConfirm(close) {
              submit(close)
            },
          })
          return
        }
      } catch (e) {
        return
      }
    }
    await submit()
  }

  // 点击保存，校验表单
  const handleSubmit = async () => {
    if (loading.value || formIsView.value) {
      return
    }
    await formRef.value.validate()
    if (formValue.value.davidType === 'pkg' && !formValue.value.davidCrowdIds?.length) {
      return
    }
    await checkUsage()
  }

  interface IButton {
    label: string
    type?: string
    onClick: () => void
  }

  const buttons = ref<IButton[]>([
    {
      label: formIsView.value ? '返回' : '取消',
      onClick: () => clickCancel(),
    },
  ])

  if (!formIsView.value) {
    buttons.value.push({
      label: `保存${formTypeText.value}`,
      type: 'primary',
      onClick: () => handleSubmit(),
    })
  }

  // 获取活动数据
  const fetchCrowdDetail = async () => {
    loading.value = true
    const res = await getRuleDetail({ crowdId })
    console.log('getRes: ', res)
    const crowd = res?.data
    const davidCrowdIds = crowd.davidCrowdIds || []
    Object.assign(formValue.value, {
      name: `${crowd.name}${formIsCopy.value ? '副本' : ''}`,
      userLifeCycles: crowd.userLifeCycles || [],
      userLiveLifeCycles: crowd.userLiveLifeCycles || [],
      davidCrowdIds,
      davidType: crowd.davidCrowdIds?.length ? 'pkg' : 'none',
      orderDate: crowd.orderDate || '',
      orderEndDate: crowd.orderEndDate || '',
      orderCount: crowd.orderCount || '',
      lifeCyclesDavidCrowdsRelation: crowd?.lifeCyclesDavidCrowdsRelation || 'AND',
      frontierType: crowd.frontierType || 'domestic',
    })
    initPackList.value = crowd.davidCrowds || []
    crowdList.value = crowd.davidCrowds || []
  }

  const init = async () => {
    try {
      if (formIsView.value || formIsEdit.value || formIsCopy.value) {
        await fetchCrowdDetail()
      }
    } catch (err) {
      toast.danger({ description: `表单信息初始化失败 ${err?.message || ''}`, closeable: true })
    } finally {
      loading.value = false
    }
  }

  // 新增校验函数
  // const validateRecentOrders = (_, value, callback) => {
  //   const { orderDate, orderEndDate, orderCount } = formValue.value
  //   if ([orderDate, orderEndDate, orderCount].some(v => !v)) {
  //     callback(new Error('请同时选择周期和订单数量'))
  //   } else {
  //     callback()
  //   }
  // }

  // 兼容老数据
  const userLifecycleOptions = computed(() => {
    if ((formIsView.value || formIsEdit.value) && Number(crowdId) <= 10624) {
      return USER_LIFECYCLE_OPTIONS_SHOW_OLD
    }
    return USER_LIFECYCLE_OPTIONS_V2
  })

  // 处理生命周期选项，动态控制disabled状态
  const processedLifecycleOptions = computed(() => {
    const options = userLifecycleOptions.value.map(option => {
      // 复制选项对象，避免修改原始对象
      const newOption = { ...option }

      // 如果选项在当前选中列表中，移除disabled属性以允许它被删除
      if (formValue.value.userLifeCycles.includes(newOption.value)) {
        newOption.disabled = false
      }

      return newOption
    })

    return options
  })

  // 处理生命周期选项变更
  const handleLifeCycleChange = newValues => {
    formValue.value.userLifeCycles = newValues
  }

  // 过滤订单日期选项，根据 orderEndDate 动态变化（末尾数字需要更大）
  const filteredOrderDateOptions = computed(() => filterOrderDateOptionsByEndDate(formValue.value.orderEndDate))

  onMounted(() => {
    init()
  })

</script>

<style>
.d-datepicker-dates {
  .d-datepicker-row {
    display: flex;
  }
}

.custom-option {
  display: flex;
  align-items: center;
  overflow: auto;
  border-radius: var(--size-radius-default);
  padding: var(--size-space-step-default);
  grid-gap: var(--size-space-step-default);
  gap: var(--size-space-step-default);
  cursor: pointer;
}
</style>
