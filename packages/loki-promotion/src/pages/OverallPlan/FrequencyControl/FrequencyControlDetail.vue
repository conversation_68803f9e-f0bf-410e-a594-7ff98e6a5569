<template>
  <Spinner
    :spinning="sceneOptions === null"
    tip="加载中"
    size="large"
  >
    <Text
      v-if="!isSelect"
      link
      style="margin-bottom: 20px;"
      @click="()=>{$router.back()}"
    >{{ backText }}</Text>
    <Text
      type="h4"
      bold
    >频控</Text><br>
    <DelightForm
      v-if="(type === 'create' || (type !== 'create' && hasInit)) && totalSceneOptions.length"
      ref="formRef"
      name="layout"
      :config="config"
      :components="{
        DelightForm,
        FormLayout,
        FormItem,
        Input,
        Select,
        DatePicker,
        Radio,
        Checkbox,
        Switch,
        MultiLevelCheckbox,
        FrequencyInput
      }"
      :scope="{
        disabled,
        updateSceneOptions,
        updateTriggerOptions,
        sceneOptions,
        formType: type,
      }"
    />
    <div
      v-if="!disabled"
      class="footer"
    >
      <Button
        class="btn"
        type="default"
        @click="()=>{$router.back()}"
      >
        取消
      </Button>
      <Button
        class="btn"
        type="primary"
        @click="create"
      >
        {{ buttonText }}
      </Button>
    </div>
  </Spinner>
</template>
  <script setup lang="tsx">
  import {
    Button,
    Modal,
    Spinner,
    Text,
    toast,
  } from '@xhs/delight'
  import {
    Checkbox,
    DatePicker,
    DelightForm,
    Input,
    Layout,
    Radio,
    Select,
    Switch,
  } from '@xhs/delight-formily'
  import {
    computed, h,
    onMounted,
    ref,
  } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  // import dayjs from 'dayjs'
  import MultiLevelCheckbox from '~/components/MultiLevelCheckbox'
  import { getFrequencyRule } from '~/services/usergrowth/edith_get_frequency_rule'
  import { postFrequencyRuleEdit } from '~/services/usergrowth/edith_post_frequency_rule_edit'
  import { postFrequencyRuleAdd } from '~/services/usergrowth/edith_post_frequency_rule_add'
  import {
    getBizSceneListV2,
    getTriggerInfo,
  } from '~/services/intelligentMarketing'
  import { config } from './config'
  import FrequencyInput from '~/pages/IntelligentMarketing/Group/Nodes/Frequency/FrequencyInput.vue'

  const FormLayout = Layout.FormLayout
  const FormItem = Layout.FormItem

  const backText = '< 返回'
  const route = useRoute()
  const router = useRouter()
  const isSelect = route.query.isSelect
  const type = route.query.type
  const disabled = type === 'view'
  const bizsceneList = ref({})
  const originName = ref('')
  const hasInit = ref(false)
  const isCreating = ref(false)
  const sceneOptions = computed(() => {
    const { bizscenes } = bizsceneList.value
    if (bizscenes) {
      return bizscenes.map(item => ({
        name: item.name,
        value: item.id,
        ...item,
      }))
    }
    return []
  })

  const totalSceneOptions = ref([])

  const formRef = ref()

  const buttonText = computed(() => {
    if (type === 'copy') {
      return '确认复制'
    } if (type === 'edit') {
      return '保存编辑'
    } if (type === 'create') {
      return '保存创建'
    }
    return ''
  })

  // 更新可选择的场景
  const updateSceneOptions = async (field: any, form: any) => {
    if (!form.values?.frontierType) return
    const res = await getBizSceneListV2(form.values?.frontierType)
    bizsceneList.value = res
    field.componentProps.options = res?.bizscenes.map(item => ({
      name: item.name,
      value: item.id,
      status: item.status || '',
    })) || []
    // 清空原来的场景选择
    if (!field.componentProps.options.map(item => item.value).includes(field.value)) {
      field.value = undefined
    }
  }

  // 更新可选择的触发点
  const updateTriggerOptions = async (field: any, form: any) => {
    if (!form.values?.bizsceneId) return

    const res = await getTriggerInfo({
      bizsceneId: form.values.bizsceneId,
      bizsceneConfig: form.values?.bizsceneConfig || {},
    })
    field.componentProps.options = res?.triggers.map(item => ({
      name: item.name,
      value: item.id,
      status: item.status || '',
    })) || []
    // field.componentProps.options 剔除掉 status 为 OFFLINE 但保留 form.values?.triggerId， 如果是该项status 是 OFFLINE 中 添加属性 disabled
    // 移除 status 为 OFFLINE 的选项，但保留与 triggerId 相同的选项
    field.componentProps.options = field.componentProps.options.filter(option => option.status !== 'OFFLINE' || option.value === form.values?.triggerId).map(option => {
      // 如果是 triggerId 对应的选项，并且 `status` 是 OFFLINE，则加上 `disabled: true`
      if (option.value === form.values?.triggerId && option.status === 'OFFLINE') {
        return {
          ...option,
          disabled: true, // 禁用当前项
        }
      }
      return option // 其他保持不变
    })

    // 清空原来的触发点选择
    if (!field.componentProps.options.map(item => item.value).includes(field.value)) {
      field.value = undefined
    }
  }

  const getDetail = () => {
    const params = {
      frequencyRuleId: Number(route.query.id),
    }
    getFrequencyRule(params).then(res => {
      if (res.frequencyRule) {
        const setValues: any = {
          ...res.frequencyRule,
        }
        hasInit.value = true
        setTimeout(() => {
          formRef.value?.form.setValues(setValues)
        })
      }
    })
  }

  const create = async () => {
    try {
      if (isCreating.value) {
        return
      }

      // 第一步：表单验证
      try {
        await formRef.value.form.validate()
      } catch (error) {
        toast.danger({
          description: '表单校验失败',
          closeable: true,
        })
        return
      }

      // 准备参数
      const formValues = formRef.value.form.values
      const params = {
        // launchProject: {
        ...formValues,
        frequencyRuleId: Number(route.query.id),
        // },
      }

      if (type === 'copy' && params.name === originName.value) {
        const isContinue = await new Promise(resolve => {
          Modal.warning({
            title: '提示',
            content: h(Text, {}, {
              default: () => '名称与原名称相同, 是否确认提交？',
            }),
            onConfirm: close => {
              close?.()
              resolve(true)
            },
            onCancel: close => {
              close?.()
              resolve(false)
            },
          })
        })
        if (!isContinue) {
          return
        }
      }
      if (type !== 'edit') {
        delete params?.frequencyRuleId
      }
      // 第三步：根据类型选择请求方法
      const requestMap = {
        create: postFrequencyRuleAdd,
        edit: postFrequencyRuleEdit,
        copy: postFrequencyRuleAdd,
      }

      const request = requestMap[type]
      if (!request) {
        throw new Error('未知的操作类型')
      }

      // 第四步：发起创建请求
      isCreating.value = true
      const res = await request(params)
      console.log('lxc=====res', res)

      if (res.success || res?.response?.success) {
        toast.success({ description: '创建成功', closeable: true })
        setTimeout(() => {
          router.back()
        }, 500)
      } else {
        throw new Error(res?.msg)
      }
    } catch (error) {
      toast.danger({
        description: error?.message || '操作失败',
        duration: 1500,
      })
    } finally {
      isCreating.value = false
    }
  }

  const initBizSceneList = async () => {
    const res = await getBizSceneListV2()
    const { bizscenes } = res
    totalSceneOptions.value = bizscenes.map(item => ({
      name: item.name,
      value: item.id,
      ...item,
    }))
    // bizsceneList.value = res
    if (type !== 'create') {
      getDetail()
    } else {
      hasInit.value = true

      setTimeout(() => {
        if (formRef.value?.form) {
          const currentValues = formRef.value.form.values
          const newValues = {
            ...currentValues,
          }
          formRef.value.form.setValues(newValues)
        }
      }, 100)
    }
  }
  onMounted(() => {
    initBizSceneList()
  })

</script>

<style lang="stylus" scoped>
  .footer {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 20px;
    .btn {
      margin: 0 10px;
    }
  }
</style>
