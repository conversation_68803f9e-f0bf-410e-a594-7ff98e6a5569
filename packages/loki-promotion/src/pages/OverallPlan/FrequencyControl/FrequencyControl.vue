<template>
  <Space
    direction="vertical"
    block
    size="large"
    align="unset"
  >
    <OutlineFilter
      v-model="filterParam"
      :config="filterConfig"
      style="overflow: auto"
    />
    <Space
      :style="{ background: '#FFFFFF', borderRadius: '8px', padding: '0 24px 24px'}"
      direction="vertical"
      size="small"
      align="unset"
    >
      <Toolbar
        style="margin-top: -24px;"
        :config="toolBarConfig"
      />
      <Table
        v-model:selected="selectedData"
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
      >
        <template #range="{ rowData }">
          <div class="tableText">{{ getRangeName(rowData.range) }}</div>
        </template>
        <template #dimension="{ rowData }">
          <div class="tableText">{{ getDimensionName(rowData.dimension) }}</div>
        </template>
        <template #startAt="{ rowData }">
          <div class="tableText">{{ formatTime(rowData.startAt) }} - {{ formatTime(rowData.endAt) }}</div>
        </template>
        <template #operation="{ rowData }">
          <Space>
            <Text
              link
              @click="()=>{goDetail('view',rowData.id)}"
            >查看</Text>
            <Text
              v-if="!rowData.containsPriceRangeCondition"
              link
              @click="()=>{goDetail('copy',rowData.id)}"
            >复制</Text>

            <Text
              link
              @click="()=>{goDetail('edit',rowData.id)}"
            >编辑</Text>

            <Text
              link
              @click="()=>{handleDelete(rowData)}"
            >删除</Text>
          </Space>
        </template>
      </Table>
      <Pagination
        v-model="filterParam.page"
        v-model:page-size="filterParam.pageSize"
        :total="filterParam.total"
        style="margin-top: 24px"
        @change="() => fetchList(false)"
      />
    </Space>

  </Space>

</template>

<script lang="ts" setup>
  import {
    Table, Text, Space, Pagination, Modal, toast,
  } from '@xhs/delight'
  import {
    onBeforeMount, h, ref, computed,
  } from 'vue'
  import Toolbar from '@xhs/delight-material-ultra-toolbar'
  import { useRouter } from 'vue-router'
  import OutlineFilter from '@xhs/delight-material-ultra-outline-filter'
  import dayjs from 'dayjs'
  import { useListFilter } from './useListFilter'
  import { getFrequencyRuleQuery } from '~/services/usergrowth/edith_get_frequency_rule_query'
  import { postFrequencyRuleDelete } from '~/services/usergrowth/edith_post_frequency_rule_delete'
  import { getRangeName, getDimensionName } from './config'

  const formatTime = (time: string) => dayjs.unix(time).format('YYYY-MM-DD HH:mm:ss')

  const dataSource = ref([])
  const selectedData = ref([])
  const router = useRouter()

  const loading = ref(false)
  const { filterConfig, filterParam } = useListFilter(fetchList)

  const toolBarConfig = computed(() => ({
    actions: [
      {
        text: '新建频控',
        props: {
          type: 'primary',
        },
        onClick: () => {
          router.push({ name: 'FrequencyControlDetail', query: { type: 'create' } })
        },
      },
    ],
    maxCount: 5,
  }))

  async function fetchList(pageReset = true) {
    if (pageReset) {
      filterParam.value.page = 1
      selectedData.value = []
    }

    loading.value = true
    const {
      page, pageSize, name,
    } = filterParam.value
    const params = {
      // status: 'ONLINE',
      page,
      pageSize,
      name,
    }

    const res = await getFrequencyRuleQuery(params)
    // res.prizeRuleList = mockData
    loading.value = false
    if (res?.ruleList) {
      dataSource.value = res.ruleList.map(item => ({
        ...item,
        key: item.id,
      }))
      filterParam.value.total = res?.total || 0
    } else {
      toast.danger({ description: '获取频控列表失败', closeable: true })
    }
  }

  const handleDelete = async rowData => {
    Modal.confirm({
      title: '删除频控',
      content: h(Text, {}, {
        default: () => `确定删除频控${rowData.name}吗？`,
      }),
      onConfirm: close => {
        postFrequencyRuleDelete({
          frequencyRuleId: rowData.id,
        }).then(() => {
          toast.success({ description: '删除成功' })
          fetchList(false)
          close?.()
        }).catch(e => {
          console.log(e)
          toast.danger({ description: e.msg || e.message || '删除失败' })
        })
      },
    })
  }

  const goDetail = (type, id) => {
    // const routeData = router.resolve({
    //   name: 'PlanDetail',
    //   query: {
    //     type,
    //     id,
    //   },
    // })
    // window.open(routeData.href, '_blank')
    router.push({
      name: 'FrequencyControlDetail',
      query: {
        type,
        id,
      },
    })
  }

  const columns = [
    {
      title: '频控id',
      dataIndex: 'id',
      minWidth: 120,
      fixed: true,
    },
    {
      title: '频控名称',
      dataIndex: 'name',
      minWidth: 100,
    },
    {
      title: '频控范围',
      dataIndex: 'range',
      minWidth: 100,
    },
    {
      title: '频控维度',
      dataIndex: 'dimension',
      minWidth: 100,
    },
    {
      title: '编辑',
      dataIndex: 'operation',
      minWidth: 300,
    },
  ]

  onBeforeMount(() => {
    fetchList()
  })

</script>

<style lang="stylus" scoped>
.search-bar {
  width: 70%;
  display: flex;
  gap: 14px;
}
.main-content {
  margin-top: 10px;
}
.tableText {
  color: rgba(0,0,0,0.65);
  font-size: 14px
}

</style>
