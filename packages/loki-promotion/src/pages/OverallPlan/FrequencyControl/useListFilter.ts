import { ref } from 'vue'
import { Input } from '@xhs/delight'
import type { IFilter } from '@xhs/delight-material-ultra-outline-filter'

export function useListFilter(fetchList: () => Promise<void>) {
  const filterParam = ref({
    // 搜索参数
    name: undefined,
    page: undefined,
    pageSize: 10,
    total: 0,
  })

  const filterConfig: IFilter = {
    handleFilter: fetchList,
    filterItems: [
      {
        label: '频控名称',
        name: 'name',
        component: {
          is: Input,
          props: {
            placeholder: '请输入投放方案名称',
          },
        },
      },
    ],
  }

  return {
    filterParam,
    filterConfig,
  }
}
