const frontierTypeOptions = [
  {
    label: '境内',
    value: 'domestic',
  },
  {
    label: '境外',
    value: 'abroad',
  },
]

// 通用频控周期
export const COMMON_FREQUENCY_OPTIONS = [
  {
    name: '每个自然日',
    value: 'NATURAL_DAY',
  },
  {
    name: '每个自然周',
    value: 'NATURAL_WEEK',
  },
  {
    name: '每个自然月',
    value: 'NATURAL_MONTH',
  },
  {
    name: '每周六到下周五',
    value: 'NATURAL_WEEK_START_AT_SATURDAY',
  },
  {
    name: '每N个自然日',
    value: 'NEAR_N_DAYS',
  },
  {
    name: '每20秒',
    value: 'NEAR_20_SECONDS',
  },
  {
    name: '每40秒',
    value: 'NEAR_40_SECONDS',
  },
  {
    name: '小时',
    value: 'RELATIVE_HOUR',
  },
  {
    name: '近7天',
    value: 'NEAR_7_DAYS',
  },
  {
    name: '近30天',
    value: 'NEAR_30_DAYS',
  },
]

// 特殊频控周期(策略才有)
export const SPECIAL_FREQUENCY_OPTIONS = [
  {
    name: '策略有效期内',
    value: 'STRATEGY_LIFECYCLE',
  },
  {
    name: '无频控',
    value: 'NO_LIMIT',
  },
]

const rangeOptions = [
  {
    label: '场景频控',
    value: 'bizsceneFrequency',
  }, {
    label: '时机频控',
    value: 'triggerFrequency',
  },
]

const dimensionOptions = [
  {
    label: '投放方案',
    value: 'launchProject',
  },
]
export const getRangeName = (value: string) => rangeOptions.find(item => item.value === value)?.label

export const getDimensionName = (value: string) => dimensionOptions.find(item => item.value === value)?.label

// 策略频控
export const STRATEGY_FREQUENCY_OPTIONS = [
  ...COMMON_FREQUENCY_OPTIONS,
  ...SPECIAL_FREQUENCY_OPTIONS,
]

export const config = {
  type: 'void',
  'x-decorator': 'FormLayout',
  'x-disabled': '{{ disabled }}',
  properties: {
    name: {
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '频控名称',
        required: true,
      },
      'x-component': 'Input',
      'x-component-props': {
        maxLength: 30,
        placeholder: '请输入',
        clearable: true,
      },
      'x-validator': {
        required: true,
      },
    },
    range: {
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '频控范围',
        required: true,
      },
      'x-component': 'Select',
      'x-component-props': {
        options: rangeOptions,
      },
      placeholder: '请选择',
      'x-validator': {
        required: true,
      },
    },
    frontierType: {
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '生效范围',
        required: true,
      },
      'x-component': 'Radio.RadioGroup',
      'x-component-props': {
        options: frontierTypeOptions,
      },
      default: '{{ formType === "create" ? "domestic" : ""}}',
      'x-validator': {
        required: true,
      },
    },
    //  触发场景
    bizsceneId: {
      type: 'number',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '场景',
        required: true,
      },
      'x-component': 'Select',
      'x-component-props': {
        placeholder: '请选择',
        clearable: true,
        // options: '{{ sceneOptions }}',
      },
      'x-validator': {
        required: true,
      },
      'x-reactions': [
        {
          dependencies: ['frontierType'],
          fulfill: {
            run: '{{ updateSceneOptions($self, $form) }}',
          },
        },
      ],
    },
    // 触发点
    triggerId: {
      type: 'number',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '时机',
        required: true,
      },
      'x-component': 'Select',
      'x-component-props': {
        placeholder: '请选择',
        clearable: true,
      },
      'x-validator': {
        required: true,
      },
      'x-reactions': {
        dependencies: ['bizsceneId'],
        fulfill: {
          run: '{{ updateTriggerOptions($self, $form) }}',
        },
      },
    },
    dimension: {
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '频控维度',
        required: true,
      },
      'x-component': 'Select',
      'x-component-props': {
        options: dimensionOptions,
      },
      placeholder: '请选择',
      'x-validator': {
        required: true,
      },
    },
    triggerFrequency: {
      type: 'object',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '触发频控',
        required: true,
      },
      'x-component': 'FrequencyInput',
      'x-component-props': {
        options: STRATEGY_FREQUENCY_OPTIONS,
        multiple: true,
      },
      'x-validator': {
        required: true,
        triggerType: 'onBlur',
        validateFrequencyInfo: true,
      },
      default: [
        {
          unitType: '',
          unitNum: 1,
          allowNum: undefined,
        },
      ],
    },
    // 领取频控
    claimFrequency: {
      type: 'object',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '领取频控',
        required: true,
      },
      'x-component': 'FrequencyInput',
      'x-component-props': {
        options: STRATEGY_FREQUENCY_OPTIONS,
        multiple: true,
      },
      'x-validator': {
        required: true,
        triggerType: 'onBlur',
        validateFrequencyInfo: true,
      },
      default: [
        {
          unitType: '',
          unitNum: 1,
          allowNum: undefined,
        },
      ],
    },

  },
}
