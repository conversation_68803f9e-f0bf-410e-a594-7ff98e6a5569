<template>
  <div class="intelligent-marketing-page">
    <div class="tab-list">
      <div
        v-for="(item, index) in menuList"
        :key="index"
        class="tab-item"
        :class="[getLastSegment(route.path) === item.path ? 'selected-tab' : '']"
        @click="handleChangeTab(index)"
      >
        {{ item.name }}
      </div>
    </div>
    <div class="page-content">
      <router-view />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, ref } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { useStore } from 'vuex'
  import { getBizSceneList, getBizSceneListV2 } from '~/services/intelligentMarketing'
  import { getTriggerList } from '~/services/trigger'
  import { ISceneItem, ITriggerItem, IContainerItem } from '~/types/intelligentMarketing'

  const router = useRouter()
  const route = useRoute()
  const store = useStore()

  const menuList = ref([
    {
      name: '投放方案',
      to: 'PlanList',
      path: 'plan-list',
    },
    {
      name: '统一频控',
      to: 'FrequencyControl',
      path: 'frequency-control',
    },
    {
      name: '统一优先级',
      to: 'PriorityControl',
      path: 'priority-control',
    },
  ])

  const handleChangeTab = (index: number) => {
    router.push({ name: menuList.value[index].to })
  }

  function getLastSegment(url:string) {
    if (typeof url !== 'string' || url.trim() === '') {
      return null
    }

    // 正则表达式匹配最后一节地址，不包含结尾的斜杠
    const match = url.match(/\/([^/]+)\/?$/)
    if (match) {
      return match[1]
    }
    return null
  }

  onMounted(async () => {
    const res2: {
      bizscenes: ISceneItem[]
      triggers: ITriggerItem[]
      containerConfigs: IContainerItem
    } = await getBizSceneListV2()

    store.commit('intelligentMarketing/setBizSceneInfoV2', res2)

    const res: {
      bizscenes: ISceneItem[]
      triggers: ITriggerItem[]
      containerConfigs: IContainerItem
    } = await getBizSceneList()

    // 更新到store中
    store.commit('intelligentMarketing/setBizSceneInfo', res)

    const res1 = await getTriggerList({
      pageNum: 1,
      pageSize: 999,
    })
    store.commit('intelligentMarketing/setTriggerPointList', res1.triggerPointList.map(item => ({
      id: item.id,
      name: item.name,
    })) || [])
  })
</script>

<style lang="stylus" scoped>
  .intelligent-marketing-page {
    min-height: 480px;

    .tab-list {
      display: flex;
      color: rgba(0,0,0,0.65);
      gap: 20px;
      height: 30px;

      .tab-item {
        height: 30px;
        position: relative;
        cursor: pointer;
      }

      .selected-tab {
        color: #000000;
        font-weight: 500;

        &::after {
          content: '';
          position absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 100%;
          height: 2px;
          border-radius: 2px;
          background-color: var(--color-primary);
        }
      }
    }

    .page-content {
      min-height: 600px;
      background-color: #fff;
      border-radius: 8px;
      margin-top: 10px;
      padding: 20px;

      .v-enter-active,
      .v-leave-active {
        transition: all 0.5s ease;
      }

      .v-enter-from,
      .v-leave-to {
        opacity: 0;
        transform: translateX(12px);
      }
    }
  }
</style>
