<template>
  <Space
    direction="vertical"
    block
    size="large"
    align="unset"
  >
    <OutlineFilter
      v-model="filterParam"
      :config="filterConfig"
      style="overflow: auto"
    />
    <Space
      :style="{ background: '#FFFFFF', borderRadius: '8px', padding: '0 24px 24px'}"
      direction="vertical"
      size="small"
      align="unset"
    >
      <Toolbar
        style="margin-top: -24px;"
        :config="toolBarConfig"
      />
      <Table
        v-model:selected="selectedData"
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
      >
        <template #status="{ rowData }">
          <div class="tableText">{{ getStatusName(rowData.status) }}</div>
        </template>
        <template #startAt="{ rowData }">
          <div class="tableText">{{ formatTime(rowData.startAt) }} - {{ formatTime(rowData.endAt) }}</div>
        </template>
        <template #operation="{ rowData }">
          <Space>
            <Text
              link
              @click="()=>{goDetail('view',rowData.id)}"
            >查看</Text>
            <Text
              v-if="!rowData.containsPriceRangeCondition"
              link
              @click="()=>{goDetail('copy',rowData.id)}"
            >复制</Text>

            <Text
              link
              @click="()=>{goDetail('edit',rowData.id)}"
            >编辑</Text>

            <Text
              link
              @click="()=>{handleDelete(rowData)}"
            >删除</Text>
          </Space>
        </template>
      </Table>
      <Pagination
        v-model="filterParam.page"
        v-model:page-size="filterParam.pageSize"
        :total="filterParam.total"
        style="margin-top: 24px"
        @change="() => fetchList(false)"
      />
    </Space>

  </Space>

</template>

<script lang="ts" setup>
  import {
    Table, Text, Space, Pagination, Modal, toast,
  } from '@xhs/delight'
  import {
    onBeforeMount, h, ref, computed,
  } from 'vue'
  import Toolbar from '@xhs/delight-material-ultra-toolbar'
  import { useRouter } from 'vue-router'
  import OutlineFilter from '@xhs/delight-material-ultra-outline-filter'
  import dayjs from 'dayjs'
  import { useListFilter } from './useListFilter'
  import { postLaunchProjectQuery } from '~/services/usergrowth/edith_post_launch_project_query'
  import { postLaunchProjectDelete } from '~/services/usergrowth/edith_post_launch_project_delete'
  import { getStatusName } from './config'

  const formatTime = (time: string) => dayjs.unix(time).format('YYYY-MM-DD HH:mm:ss')

  const dataSource = ref([])
  const selectedData = ref([])
  const router = useRouter()

  const loading = ref(false)
  const { filterConfig, filterParam } = useListFilter(fetchList)

  const toolBarConfig = computed(() => ({
    actions: [
      {
        text: '新建投放方案',
        props: {
          type: 'primary',
        },
        onClick: () => {
          router.push({ name: 'PlanDetail', query: { type: 'create' } })
        },
      },
    ],
    maxCount: 5,
  }))

  async function fetchList(pageReset = true) {
    if (pageReset) {
      filterParam.value.page = 1
      selectedData.value = []
    }

    loading.value = true
    const {
      page, pageSize, launchProjectId, nameLike, triggerId, createdBy,
    } = filterParam.value
    const params = {
      // status: 'ONLINE',
      page,
      pageSize,
      launchProjectId,
      nameLike,
      triggerId,
      createdBy,
    }

    const filteredParams = Object.entries(params).reduce((acc, [key, value]) => {
      if (value) acc[key] = value
      return acc
    }, {})
    const res = await postLaunchProjectQuery(filteredParams)
    // res.prizeRuleList = mockData
    loading.value = false
    if (res?.launchProjectList) {
      dataSource.value = res.launchProjectList.map(item => ({
        ...item,
        key: item.id,
      }))
      filterParam.value.total = res?.total || 0
    } else {
      toast.danger({ description: '获取投放方案列表失败', closeable: true })
    }
  }

  const handleDelete = async rowData => {
    Modal.confirm({
      title: '删除投放方案',
      content: h(Text, {}, {
        default: () => `确定删除投放方案${rowData.name}吗？`,
      }),
      onConfirm: close => {
        postLaunchProjectDelete({
          id: rowData.id,
        }).then(() => {
          toast.success({ description: '删除成功' })
          fetchList(false)
          close?.()
        }).catch(e => {
          console.log(e)
          toast.danger({ description: e.msg || e.message || '删除失败' })
        })
      },
    })
  }

  const goDetail = (type, id) => {
    // const routeData = router.resolve({
    //   name: 'PlanDetail',
    //   query: {
    //     type,
    //     id,
    //   },
    // })
    // window.open(routeData.href, '_blank')
    router.push({
      name: 'PlanDetail',
      query: {
        type,
        id,
      },
    })
  }

  const columns = [
    {
      title: '投放方案id',
      dataIndex: 'id',
      minWidth: 120,
      fixed: true,
    },
    {
      title: '投放方案名称',
      dataIndex: 'name',
      minWidth: 100,
    },
    {
      title: '状态',
      dataIndex: 'status',
      minWidth: 100,
    },
    {
      title: '触点',
      dataIndex: 'triggerName',
      minWidth: 100,
    },
    {
      title: '投放方案时间',
      dataIndex: 'startAt',
      minWidth: 100,
    },
    {
      title: '创建人',
      dataIndex: 'createdBy',
      minWidth: 100,
    },
    {
      title: '编辑',
      dataIndex: 'operation',
      minWidth: 300,
    },
  ]

  onBeforeMount(() => {
    fetchList()
  })

</script>

<style lang="stylus" scoped>
.search-bar {
  width: 70%;
  display: flex;
  gap: 14px;
}
.main-content {
  margin-top: 10px;
}
.tableText {
  color: rgba(0,0,0,0.65);
  font-size: 14px
}

</style>
