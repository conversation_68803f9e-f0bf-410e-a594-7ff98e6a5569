const frontierTypeOptions = [
  {
    label: '境内',
    value: 'domestic',
  },
  {
    label: '境外',
    value: 'abroad',
  },
]

const projectTypeOptions = [
  {
    label: '策略',
    value: 'strategy',
  }, {
    label: '玩法',
    value: 'play',
  },
]

const statusOptions = [
  {
    label: '已删除',
    value: -1,
  },
  {
    label: '未开始',
    value: 0,
  },
  {
    label: '进行中',
    value: 1,
  },
  {
    label: '已下线',
    value: 2,
  },
  {
    label: '已暂停',
    value: 3,
  },
  {
    label: '已结束',
    value: 4,
  },
]

export const getStatusName = (value: number) => statusOptions.find(item => item.value === value)?.label

export const config = {
  type: 'void',
  'x-decorator': 'FormLayout',
  'x-disabled': '{{ disabled }}',
  properties: {
    name: {
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '投放方案名称',
        required: true,
      },
      'x-component': 'Input',
      'x-component-props': {
        maxLength: 30,
        placeholder: '请输入',
        clearable: true,
      },
      'x-validator': {
        required: true,
      },
    },
    projectType: {
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '投放方案类型',
        required: true,
      },
      'x-component': 'Select',
      'x-component-props': {
        options: projectTypeOptions,
      },
      placeholder: '请选择',
      'x-validator': {
        required: true,
      },
    },
    projectInfo: {
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '自定义参数',
        required: false,
      },
      'x-component': 'Input.Textarea',
      'x-component-props': {
        rows: 5,
        placeholder: '请输入',
      },
    },
    frontierType: {
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '生效范围',
        required: true,
      },
      'x-component': 'Radio.RadioGroup',
      'x-component-props': {
        options: frontierTypeOptions,
      },
      default: '{{ formType === "create" ? "domestic" : ""}}',
      'x-validator': {
        required: true,
      },
    },
    //  触发场景
    bizsceneId: {
      type: 'number',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '场景',
        required: true,
      },
      'x-component': 'Select',
      'x-component-props': {
        placeholder: '请选择',
        clearable: true,
        // options: '{{ sceneOptions }}',
      },
      'x-validator': {
        required: true,
      },
      'x-reactions': [
        {
          dependencies: ['frontierType'],
          fulfill: {
            run: '{{ updateSceneOptions($self, $form) }}',
          },
        },
      ],
    },
    // 触发点
    triggerId: {
      type: 'number',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '时机',
        required: true,
      },
      'x-component': 'Select',
      'x-component-props': {
        placeholder: '请选择',
        clearable: true,
      },
      'x-validator': {
        required: true,
      },
      'x-reactions': {
        dependencies: ['bizsceneId'],
        fulfill: {
          run: '{{ updateTriggerOptions($self, $form) }}',
        },
      },
    },
    planTime: {
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '时间',
        required: true,
      },
      'x-component': 'DatePicker',
      'x-component-props': {
        isRange: true,
        unit: 'second',
        placeholder: {
          start: '请选择开始时间',
          end: '请选择结束时间',
        },
      },
      'x-validator': {
        required: true,
        // validateStrategyTimeRange: true,
      },
    },
  },
}
