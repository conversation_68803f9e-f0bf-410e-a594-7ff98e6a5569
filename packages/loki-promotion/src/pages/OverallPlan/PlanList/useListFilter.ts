import { ref } from 'vue'
import { Input } from '@xhs/delight'
import type { IFilter } from '@xhs/delight-material-ultra-outline-filter'

export function useListFilter(fetchList: () => Promise<void>) {
  const filterParam = ref({
    // 搜索参数
    launchProjectId: undefined,
    nameLike: undefined,
    createdBy: undefined,
    triggerId: undefined,
    page: undefined,
    pageSize: 10,
    total: 0,
  })

  const filterConfig: IFilter = {
    handleFilter: fetchList,
    filterItems: [
      {
        label: '投放方案id',
        name: 'launchProjectId',
        component: {
          is: Input,
          props: {
            placeholder: '请输入投放方案id',
            clearable: true,
          },
        },
      },
      {
        label: '投放方案名称',
        name: 'nameLike',
        component: {
          is: Input,
          props: {
            placeholder: '请输入投放方案名称',
          },
        },
      },
      // {
      //   label: '触点',
      //   name: 'triggerId',
      //   component: {
      //     is: Select,
      //     props: {
      //       options: [],
      //       clearable: true,
      //     },
      //   },
      // },
      {
        label: '创建人',
        name: 'createdBy',
        component: {
          is: Input,
          props: {
            placeholder: '请输入创建人',
          },
        },
      },
    ],
  }

  return {
    filterParam,
    filterConfig,
  }
}
