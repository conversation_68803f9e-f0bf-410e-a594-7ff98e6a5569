<template>
  <Space
    direction="vertical"
    block
    size="large"
    align="unset"
  >
    <OutlineFilter
      v-model="filterParam"
      :config="filterConfig"
      style="overflow: auto"
    />
    <Space
      :style="{ background: '#FFFFFF', borderRadius: '8px', padding: '0 24px 24px'}"
      direction="vertical"
      size="small"
      align="unset"
    >
      <Toolbar
        style="margin-top: -24px;"
        :config="toolBarConfig"
      />
      <Table
        v-model:selected="selectedData"
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
      >
        <template #order="{ rowData, index: dataIndex }">
          <div
            class="item"
            :style="{ flex: 1.5 }"
          >
            <Tag v-if="dataIndex !== clickIndex">{{ rowData.order }}</Tag>
            <InputNumber
              v-else
              v-model:model-value="currentValue"
              style="width: 100px;"
              placeholder="优先级"
              @enter="handleUpdatePriority"
            />
            <Icon
              style="margin-left: 10px;"
              :style="{ cursor: 'pointer'}"
              :icon="dataIndex === clickIndex ? CloseOne : Edit"
              color="text-description"
              @click="handleClickEditBtn(dataIndex, rowData)"
            />
          </div>
        </template>
        <template #dimension="{ rowData }">
          <div class="tableText">{{ getDimensionName(rowData.dimension) }}</div>
        </template>
        <template #operation="{ rowData }">
          <Space>
            <Text
              link
              @click="()=>{handleDelete(rowData)}"
            >删除</Text>
          </Space>
        </template>
      </Table>
      <Pagination
        v-model="filterParam.page"
        v-model:page-size="filterParam.pageSize"
        :total="filterParam.total"
        style="margin-top: 24px"
        @change="() => fetchList(false)"
      />
    </Space>

  </Space>

</template>

<script lang="ts" setup>
  import {
    Table, Text, Space, Pagination, Modal, toast, Icon, InputNumber, Tag,
  } from '@xhs/delight'
  import {
    onBeforeMount, h, ref, computed, reactive, toRefs,
  } from 'vue'
  import Toolbar from '@xhs/delight-material-ultra-toolbar'
  import { useRouter } from 'vue-router'
  import OutlineFilter from '@xhs/delight-material-ultra-outline-filter'
  import { CloseOne, Edit } from '@xhs/delight/icons'
  import { useListFilter } from './useListFilter'
  import { getPriorityRuleQuery } from '~/services/usergrowth/edith_get_priority_rule_query'
  import { postPriorityRuleDelete } from '~/services/usergrowth/edith_post_priority_rule_delete'
  import { getDimensionName } from './config'
  import { postPriorityRuleEdit } from '~/services/usergrowth/edith_post_priority_rule_edit'

  const dataSource = ref([])
  const selectedData = ref([])
  const router = useRouter()
  const state = reactive({
    clickIndex: -1,
    currentValue: 0,
    currentRowData: {},
  })

  const loading = ref(false)
  const { filterConfig, filterParam } = useListFilter(fetchList)

  const toolBarConfig = computed(() => ({
    actions: [
      {
        text: '新建优先级',
        props: {
          type: 'primary',
        },
        onClick: () => {
          router.push({ name: 'PriorityControlDetail', query: { type: 'create' } })
        },
      },
    ],
    maxCount: 5,
  }))

  async function fetchList(pageReset = true) {
    if (pageReset) {
      filterParam.value.page = 1
      selectedData.value = []
    }

    loading.value = true
    const {
      page, pageSize, launchProjectId, nameLike, triggerId, createdBy, dimension, bizsceneId, triggerName,
    } = filterParam.value
    const params = {
      // status: 'ONLINE',
      page,
      pageSize,
      launchProjectId,
      nameLike,
      triggerId,
      createdBy,
      dimension,
      bizsceneId,
      triggerName,
    }

    const res = await getPriorityRuleQuery(params)
    // res.prizeRuleList = mockData
    loading.value = false
    if (res?.ruleList) {
      dataSource.value = res.ruleList.map(item => ({
        ...item,
        key: item.id,
      }))
      filterParam.value.total = res?.total || 0
    } else {
      toast.danger({ description: '获取优先级列表失败', closeable: true })
    }
  }

  const handleDelete = async rowData => {
    Modal.confirm({
      title: '删除优先级',
      content: h(Text, {}, {
        default: () => `确定删除优先级${rowData.name}吗？`,
      }),
      onConfirm: close => {
        postPriorityRuleDelete({
          priorityRuleId: rowData.id,
        }).then(() => {
          toast.success({ description: '删除成功' })
          fetchList(false)
          close?.()
        }).catch(e => {
          console.log(e)
          toast.danger({ description: e.msg || e.message || '删除失败' })
        })
      },
    })
  }

  // 点击编辑优先级按钮
  const handleClickEditBtn = (index: number, rowData: any) => {
    if (index === state.clickIndex) {
      state.clickIndex = -1
    } else {
      state.clickIndex = index
      state.currentValue = rowData.order
      state.currentRowData = rowData
    }
  }

  // 手动输入更新优先级
  const handleUpdatePriority = () => {
    postPriorityRuleEdit({
      priorityRuleId: state.currentRowData.id,
      order: state.currentValue,
    }).then(() => {
      toast.success({ description: '更新成功' })
      fetchList(false)
      // 复位优先级输入框
      state.clickIndex = -1
    }).catch(e => {
      toast.danger({ description: e.msg || e.message || '更新失败' })
    })
  }

  const columns = [
    {
      title: '优先级序号',
      dataIndex: 'order',
      minWidth: 150,
      fixed: true,
    },
    {
      title: '维度',
      dataIndex: 'dimension',
      minWidth: 100,
    },
    {
      title: '实例名称',
      dataIndex: 'name',
      minWidth: 100,
    },
    {
      title: '场景',
      dataIndex: 'bizsceneName',
      minWidth: 100,
    },
    {
      title: '时机',
      dataIndex: 'triggerName',
      minWidth: 100,
    },
    {
      title: '创建人',
      dataIndex: 'creator',
      minWidth: 100,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      minWidth: 300,
    },
  ]

  onBeforeMount(() => {
    fetchList()
  })

  const { clickIndex, currentValue } = toRefs(state)

</script>

<style lang="stylus" scoped>
.search-bar {
  width: 70%;
  display: flex;
  gap: 14px;
}
.main-content {
  margin-top: 10px;
}
.tableText {
  color: rgba(0,0,0,0.65);
  font-size: 14px
}

</style>
