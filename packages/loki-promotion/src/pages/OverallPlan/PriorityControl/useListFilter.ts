import { ref } from 'vue'
import { Select, Input } from '@xhs/delight'
// import type { IFilter } from '@xhs/delight-material-ultra-outline-filter'
import { dimensionOptions } from './config'
import {
  getBizSceneList,
} from '~/services/intelligentMarketing'

export function useListFilter(fetchList: () => Promise<void>) {
  const filterParam = ref({
    // 搜索参数
    launchProjectId: undefined,
    nameLike: undefined,
    createdBy: undefined,
    triggerId: undefined,
    bizsceneId: undefined,
    triggerName: undefined,
    page: undefined,
    dimension: undefined,
    pageSize: 10,
    total: 0,
  })

  const filterConfig = ref({
    handleFilter: fetchList,
    filterItems: [
      {
        label: '优先级维度',
        name: 'dimension',
        component: {
          is: Select,
          props: {
            options: dimensionOptions,
            clearable: true,
          },
        },
      },
      {
        label: '场景',
        name: 'bizsceneId',
        component: {
          is: Select,
          props: {
            options: [],
          },
        },
      },
      {
        label: '时机名称',
        name: 'triggerName',
        component: {
          is: Input,
          props: {
            placeholder: '请输入时机名称',
          },
        },
      },
    ],
  })

  const updateBizSceneOptions = () => {
    getBizSceneList().then(res => {
      filterConfig.value.filterItems.find(item => item.name === 'bizsceneId').component.props.options = res.bizscenes.map(item => ({
        label: item.name,
        value: item.id,
      }))
    })
  }

  // 初始化时拉取一次
  updateBizSceneOptions()

  return {
    filterParam,
    filterConfig,
  }
}
