import debounce from 'lodash/debounce'
import { DataField } from '@xhs/delight-formily'
import { postLaunchProjectQuery } from '~/services/usergrowth/edith_post_launch_project_query'

const frontierTypeOptions = [
  {
    label: '境内',
    value: 'domestic',
  },
  {
    label: '境外',
    value: 'abroad',
  },
]

export const dimensionOptions = [
  {
    label: '统一投放',
    value: 'launchProject',
  },
  // {
  //   label: '策略',
  //   value: 'strategy',
  // },
]

export const getDimensionName = (dimension: string) => dimensionOptions.find(item => item.value === dimension)?.label

const xReactionForInstanceId = (field: DataField) => {
  const service = async (filterVal: any) => {
    const res = await postLaunchProjectQuery({
      page: 1,
      pageSize: 999,
      status: '1',
      nameLike: filterVal || '',
    })

    field.setComponentProps({
      options: res.launchProjectList?.map(act => ({
        label: `${act.name}(ID:${act.id})`,
        value: act.id,
      })),
    })
  }

  field.setComponentProps({
    filterable: true,
    remote: true,
    filter: debounce(
      filterVal => service(filterVal),
      300,
    ),
    immediate: true,
  })
  service('')
}

export const config = {
  type: 'void',
  'x-decorator': 'FormLayout',
  'x-disabled': '{{ disabled }}',
  properties: {
    name: {
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '优先级维度',
        required: true,
      },
      'x-component': 'Select',
      'x-component-props': {
        options: dimensionOptions,
        placeholder: '请选择',
        clearable: true,
      },
      'x-validator': {
        required: true,
      },
    },

    frontierType: {
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '生效范围',
        required: true,
      },
      'x-component': 'Radio.RadioGroup',
      'x-component-props': {
        options: frontierTypeOptions,
      },
      default: '{{ formType === "create" ? "domestic" : ""}}',
      'x-validator': {
        required: true,
      },
    },
    //  触发场景
    bizsceneId: {
      type: 'number',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '场景',
        required: true,
      },
      'x-component': 'Select',
      'x-component-props': {
        placeholder: '请选择',
        clearable: true,
        // options: '{{ sceneOptions }}',
      },
      'x-validator': {
        required: true,
      },
      'x-reactions': [
        {
          dependencies: ['frontierType'],
          fulfill: {
            run: '{{ updateSceneOptions($self, $form) }}',
          },
        },
      ],
    },
    // 触发点
    triggerId: {
      type: 'number',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '时机',
        required: true,
      },
      'x-component': 'Select',
      'x-component-props': {
        placeholder: '请选择',
        clearable: true,
      },
      'x-validator': {
        required: true,
      },
      'x-reactions': {
        dependencies: ['bizsceneId'],
        fulfill: {
          run: '{{ updateTriggerOptions($self, $form) }}',
        },
      },
    },
    // 选择实例
    instanceId: {
      type: 'array',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '选择实例',
      },
      'x-component': 'Select',
      'x-component-props': {
        placeholder: '请选择',
        multiple: true,
        filterable: true,
        multiLine: true,
        virtualize: true,
        virtualizeOptions: {
          renderLimit: 50,
        },
      },
      'x-reactions': [xReactionForInstanceId],
    },

  },
}
