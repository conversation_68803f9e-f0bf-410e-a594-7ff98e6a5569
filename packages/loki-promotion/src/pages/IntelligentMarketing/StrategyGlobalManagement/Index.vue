<template>
  <div class="strategy-global-filter-wrapper">
    <OutlineFilter
      v-model="filterParam"
      :config="filterConfig"
      style="overflow: auto;"
    />
    <JsMindViewer
      :loading="loading"
      :data="jsMindData"
    >
      <template #toolbar-right>
        <Button
          type="secondary"
          size="small"
          @click="openAIDrawer"
        >AI总结</Button>
      </template>
    </JsMindViewer>
    <AIDrawer
      v-model:visible="aiDrawerVisible"
      :present="present"
      title="AI智能总结"
      width="420px"
    />
  </div>
</template>

<script lang="tsx" setup>
  import { onMounted, ref } from 'vue'
  import { Button } from '@xhs/delight'
  import OutlineFilter from '@xhs/delight-material-ultra-outline-filter'
  import { useStrategyGlobalFilter } from './useStrategyGlobalFilter'
  import JsMindViewer from './components/JsMindViewer.vue'
  import AIDrawer from './components/AIDrawer.vue'

  const jsMindData = ref(null)
  const {
    present, filterParam, filterConfig, fetchList, loading,
  } = useStrategyGlobalFilter(jsMindData)

  const aiDrawerVisible = ref(false)
  function openAIDrawer() {
    aiDrawerVisible.value = true
  }

  onMounted(() => {
    fetchList()
  })
</script>

<style scoped>
.strategy-global-filter-wrapper .ultra-material-outline-filter {
  padding: 0 20px 16px;
}
:deep(.ultra-material-grid-filter-items) {
  gap: 12px 20px !important;
}
:deep(.d-select-placeholder) {
  word-break: keep-all;
}
</style>
