<template>
  <Drawer
    :visible="props.visible"
    :title="title"
    :width="width"
    :closable="closable"
    :mask-closable="maskClosable"
    @update:visible="handleVisibleChange"
  >
    <Card style="width: 100%;">
      <div
        v-if="!aiContent && !showRetry"
        style="color: rgba(0, 0, 0, .85);"
      >
        AI总结生成中<span class="dot-loading"><span>.</span><span>.</span><span>.</span></span>
      </div>
      <div
        v-else-if="!aiContent && showRetry"
        style="color: rgba(0, 0, 0, .85);"
      >AI总结生成失败，请重试</div>
      <div
        v-else
        class="markdown-body"
        style="word-break: break-all;color: rgba(0, 0, 0, .85);"
        v-html="aiContentHtml"
      />
    </Card>
    <div class="toolbar">
      <Button
        v-if="showBtn && aiContent"
        type="light"
        size="small"
        title="复制"
        style="opacity: .8;"
        @click="copyContent"
      >
        <Icon
          :icon="Copy"
          style="opacity: .8;"
          size="large"
        />
      </Button>
      <Button
        v-if="showRetry"
        type="light"
        size="small"
        @click="startSSE"
      >
        <Icon
          :icon="Refresh"
          style="opacity: .7;"
          size="large"
        />
      </Button>
    </div>
    <template #footer>
      <div class="ai-footer-tip">
        内容由 AI 大模型生成，请仔细甄别
      </div>
    </template>
  </Drawer>
</template>

<script setup lang="ts">
  import {
    ref, watch, defineProps, defineEmits, onBeforeUnmount, computed,
  } from 'vue'
  import {
    Drawer, Card, Icon, Button, toast,
  } from '@xhs/delight'
  import { Copy, Refresh } from '@xhs/delight/icons'
  import copyToClipboard from 'loki-shared/utils/copy'
  import { env } from '../../../../config/env.config'
  import { getLoganUrlByQuery } from '~/utils/index'
  import { renderMarkdown } from '~/utils/renderMarkdown'

  const props = defineProps({
    visible: Boolean,
    present: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: 'AI智能总结',
    },
    width: {
      type: [String, Number],
      default: '420px',
    },
    closable: {
      type: Boolean,
      default: true,
    },
    maskClosable: {
      type: Boolean,
      default: true,
    },
    footer: {
      type: [String, Object],
      default: null,
    },
  })

  const URL_HOST = {
    development: getLoganUrlByQuery(), // 配置logan地址
    prerelease: 'https://ug-assistant-ark.devops.beta.xiaohongshu.com',
    production: 'https://ug-assistant-ark.devops.xiaohongshu.com',
  }

  const emit = defineEmits(['update:visible'])

  const aiContent = ref('')
  const aiContentChunks = ref<string[]>([])
  let eventSource: EventSource | null = null
  let mockTimer: ReturnType<typeof setInterval> | null = null
  let sseUpdateTimer: ReturnType<typeof setTimeout> | null = null
  const showBtn = ref(false)
  const showRetry = ref(false)
  const aiLoading = ref(false) // 新增 loading 状态

  function startSSE() {
    stopSSE()
    aiContent.value = ''
    aiContentChunks.value = []
    showRetry.value = false
    showBtn.value = false
    aiLoading.value = true // 开始 loading

    const base = URL_HOST[env] || URL_HOST.production
    const payload = JSON.parse(props.present || '{}')

    const url = buildUrlWithParams(`${base}/api/ug/agent/strategy/summary`, { ...payload })
    eventSource = new EventSource(url)
    eventSource.onmessage = event => {
      if (event.data) {
        if (event.data === 'stream start') {
          return
        }
        aiContentChunks.value.push(event.data)
      }
      if (!sseUpdateTimer && aiContentChunks.value.length) {
        sseUpdateTimer = setTimeout(() => {
          aiContent.value = aiContentChunks.value.join('')
          sseUpdateTimer = null
        }, 60) // 60ms 节流
      }
    }
    eventSource.onerror = () => {
      eventSource?.close?.()
      eventSource = null
      aiContent.value = aiContentChunks.value.join('')
      showBtn.value = true
      showRetry.value = true
      aiLoading.value = false // 超时/异常结束 loading
    }
  }

  function stopSSE() {
    if (eventSource) {
      eventSource.close()
      eventSource = null
    }
    if (mockTimer) {
      clearInterval(mockTimer)
      mockTimer = null
    }
    if (sseUpdateTimer) {
      clearTimeout(sseUpdateTimer)
      sseUpdateTimer = null
    }
    aiContentChunks.value = []
  }

  watch(() => props.visible, v => {
    if (v) {
      aiContent.value = ''
      showBtn.value = false
      aiLoading.value = true // 打开时 loading
      startSSE()
    } else {
      stopSSE()
    }
  })

  // 处理抽屉显示状态变化
  const handleVisibleChange = (visible: boolean) => {
    emit('update:visible', visible)
    if (!visible) {
      stopSSE()
      aiLoading.value = false // 关闭时也重置 loading
    }
  }

  onBeforeUnmount(() => {
    stopSSE()
  })

  const copyContent = () => {
    const text = aiContent.value
    if (!text) {
      toast.danger('暂无可复制内容')
      return
    }
    copyToClipboard(text).then(() => {
      toast.success({ description: '已复制到剪贴板', closeable: true })
    }).catch(() => {
      toast.danger('复制失败')
    })
  }

  function buildUrlWithParams(baseUrl: string, params: Record<string, string | number | boolean | null | undefined>): string {
    const searchParams = new URLSearchParams()

    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        searchParams.append(key, String(value))
      }
    })

    const queryString = searchParams.toString()
    return `${baseUrl}${queryString ? `?${queryString}` : ''}`
  }

  const aiContentHtml = computed(() => renderMarkdown(aiContent.value))
</script>
<style lang="stylus" scoped>
:deep(.markdown-body) {
  font-size: 15px;
  color: #222;
  word-break: break-all;
  line-height: 1.7;
}
:deep(.markdown-body) p,
:deep(.markdown-body) li {
  margin-bottom: 6px !important;
}
:deep(.markdown-body) ul, :deep(.markdown-body) ol {
  margin: 4px 0 8px 18px;
  padding-left: 18px;
}
:deep(.markdown-body) h1,
:deep(.markdown-body) h2,
:deep(.markdown-body) h3,
:deep(.markdown-body) h4,
:deep(.markdown-body) h5,
:deep(.markdown-body) h6 {
  margin: 12px 0 6px 0 !important;
  font-weight: bold;
}
:deep(.markdown-body) h1 { font-size: 2em; margin-top: 18px; margin-bottom: 8px !important; }
:deep(.markdown-body) h2 { font-size: 1.5em; margin-top: 16px; margin-bottom: 6px !important; }
:deep(.markdown-body) h3 { font-size: 1.2em; margin-top: 14px; margin-bottom: 4px !important; }
:deep(.markdown-body) h4 { font-size: 1.05em; margin-top: 12px; margin-bottom: 2px !important; }
:deep(.markdown-body) h5, :deep(.markdown-body) h6 { font-size: 1em; margin-top: 10px; margin-bottom: 2px !important; }
:deep(.markdown-body) ul, :deep(.markdown-body) ol {
  margin: 4px 0 4px 16px;
  padding-left: 10px;
}
:deep(.markdown-body) li {
  margin: 2px 0 2px 0;
  line-height: 1.6;
}
:deep(.markdown-body) blockquote {
  margin: 4px 0;
  padding: 4px 10px;
  border-left: 4px solid #e0e0e0;
  background: #f7f7fa;
  color: #666;
  font-style: italic;
}
:deep(.markdown-body) a {
  color: #3c66ff;
  text-decoration: underline;
  word-break: break-all;
}
:deep(.markdown-body) table {
  display: block;
  border-collapse: collapse;
  margin: 12px 0;
  width: max-content;
  max-width: 100%;
  table-layout: auto;
  background: #fff;
  overflow-x: auto;
}
:deep(.markdown-body) th, :deep(.markdown-body) td {
  border: 1px solid #e0e0e0;
  padding: 6px 12px;
  text-align: left;
  white-space: nowrap;
}
:deep(.markdown-body) hr {
  border: none;
  border-top: 1px solid #e0e0e0;
  margin: 16px 0;
}
:deep(.markdown-body) strong, :deep(.markdown-body) b {
  font-weight: 700;
  color: #1a1a1a;
}
.toolbar {
  display: flex;
  align-items: center;
  width: 90%;
  margin: 4px 0 12px 0;
}
.dot-loading {
  display: inline-block;
  width: 24px;
  text-align: left;
}
.dot-loading span {
  opacity: 0.2;
  animation: dot-blink 1.2s infinite;
  font-weight: bold;
  font-size: 18px;
}
.dot-loading span:nth-child(1) {
  animation-delay: 0s;
}
.dot-loading span:nth-child(2) {
  animation-delay: 0.2s;
}
.dot-loading span:nth-child(3) {
  animation-delay: 0.4s;
}
@keyframes dot-blink {
  0%, 80%, 100% { opacity: 0.2; }
  40% { opacity: 1; }
}
.ai-footer-tip {
  text-align: center;
  color: #999;
  font-size: 13px;
  background: transparent;
  margin: 0;
  padding: 0;
}
</style>
