<template>
  <div class="jsmind-viewer-wrapper">
    <div
      v-loading="loading || !props.data"
      class="jsmind-container"
    >
      <div class="jsmind-toolbar">
        <div style="display: flex; flex: 1; gap: 8px;">
          <Button
            v-for="btn in buttons"
            :key="btn.key"
            size="small"
            @click="btn.onClick"
          >
            <Icon
              v-if="btn.icon"
              :icon="btn.icon"
            />
          </Button>
        </div>
        <div
          v-if="!isFullscreen"
          style="margin-left: auto;"
        >
          <slot name="toolbar-right" />
        </div>
      </div>
      <div
        ref="jsmindContainer"
        class="jsmind-mindmap"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import {
    ref, watch, onMounted, onBeforeUnmount, nextTick,
  } from 'vue'
  import { Button, Icon, vLoading } from '@xhs/delight'
  import {
    FullScreen, ZoomIn, ZoomOut, Refresh, Focus, TreeDiagram,
  } from '@xhs/delight/icons'
  import JsMind from 'jsmind'
  import 'jsmind/style/jsmind.css'

  const jsmindContainer = ref<HTMLElement | null>(null)
  let jm: any = null
  const isFullscreen = ref(false)

  // 全屏事件处理函数，便于解绑
  function onFullscreenChange() {
    const el = jsmindContainer.value?.parentElement
    isFullscreen.value = !!(el && (document.fullscreenElement === el || (document as any).webkitFullscreenElement === el || (document as any).mozFullScreenElement === el || (document as any).msFullscreenElement === el))
  }

  const props = defineProps({
    data: {
      type: Object,
      default: null,
    },
    options: {
      type: Object,
      default: () => ({}),
    },
    loading: {
      type: Boolean,
      default: false,
    },
  })

  function renderJsMind() {
    if (!jsmindContainer.value || !props.data) return
    if (jm) {
      jm.show(props.data)
      return
    }
    const options = {
      editable: false,
      theme: 'xhs_theme',
      container: jsmindContainer.value,
      view: {
        engine: 'svg',
        enable_device_pixel_ratio: true,
        draggable: true,
        // hide_scrollbars_when_draggable: true,
        support_html: true,
        node_overflow: 'wrap',
        line_width: 1,
        zoom: {
          min: 0.1, max: 2.1, step: 0.1, mask_key: 4096,
        },
      },
      ...props.options,
    }
    jm = new JsMind(options)
    jm.show(props.data)
  }

  function zoomIn() {
    jm?.view.zoom_in()
  }

  function zoomOut() {
    jm?.view.zoom_out()
  }

  function resetZoom() {
    jm?.view.set_zoom(1)
  }

  function toggleFullscreen() {
    const el = jsmindContainer.value?.parentElement
    if (!el) return
    if (!isFullscreen.value) {
      if (el.requestFullscreen) el.requestFullscreen()
      else if ((el as any).webkitRequestFullscreen) (el as any).webkitRequestFullscreen()
      else if ((el as any).mozRequestFullScreen) (el as any).mozRequestFullScreen()
      else if ((el as any).msRequestFullscreen) (el as any).msRequestFullscreen()
      isFullscreen.value = true
    } else {
      if (document.exitFullscreen) document.exitFullscreen()
      else if ((document as any).webkitExitFullscreen) (document as any).webkitExitFullscreen()
      else if ((document as any).mozCancelFullScreen) (document as any).mozCancelFullScreen()
      else if ((document as any).msExitFullscreen) (document as any).msExitFullscreen()
      isFullscreen.value = false
    }
  }

  const buttons = [
    {
      key: 'zoomOut', icon: ZoomOut, onClick: zoomOut, label: '缩小',
    },
    {
      key: 'zoomIn', icon: ZoomIn, onClick: zoomIn, label: '放大',
    },
    {
      key: 'resetZoom', icon: Refresh, onClick: resetZoom, label: '重置',
    },
    {
      key: 'fullscreen',
      get icon() {
        return isFullscreen.value ? Focus : FullScreen
      },
      onClick: toggleFullscreen,
      label: '全屏',
    },
    {
      key: 'expandAll', icon: TreeDiagram, onClick: expandAll, label: '展开全部',
    },
  ]

  function expandAll() {
    jm?.expand_all()
  }

  watch(() => props.data, () => {
    nextTick(() => renderJsMind())
  }, { deep: true })

  onMounted(() => {
    nextTick(() => renderJsMind())
    document.addEventListener('fullscreenchange', onFullscreenChange)
  })
  onBeforeUnmount(() => {
    document.removeEventListener('fullscreenchange', onFullscreenChange)
    jm = null
  })
</script>

<style lang="stylus" scoped>
.jsmind-viewer-wrapper {
  position: relative;
  padding: 0 24px;
}
.jsmind-container {
  width: 100%;
  min-height: 400px;
  height: 700px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px #f0f1f2;
  margin-top: 0;
  overflow: auto;
  border: solid 1px #ccc;
  transition: height 0.2s;
  position: relative;
  display: flex;
  flex-direction: column;
}
.jsmind-toolbar {
  display: flex;
  gap: 8px;
  padding: 8px 12px 8px 12px;
  background: transparent;
  z-index: 2;
}
.jsmind-mindmap {
  flex: 1 1 0;
  min-height: 350px;
}
:fullscreen .jsmind-container {
  height: 100vh !important;
}
:deep(.jm-node-flag-multiline) {
  white-space: normal;
  word-break: break-all;
  overflow: visible;
  max-width: 320px;
}

:deep(jmnodes.theme-xhs_theme jmnode) {
  background-color: #fff;
  color: #333 !important;
  border: 1px solid #e0e0e0 !important;
  box-shadow: 0 2px 8px #f0f1f2;
}
:deep(jmnodes.theme-xhs_theme jmnode:hover) {
  border-color: #3c66ff !important;
  color: #222 !important;
  cursor: pointer;
}
:deep(jmnodes.theme-xhs_theme jmnode.selected) {
  border-color: #3c66ff !important;
  color: #222;
  z-index: 2;
}
:deep(jmexpander){
  line-height: 1;
}

:deep(.jm-node-title) {
  font-size: 13px;
  font-weight: bold;
  color: #222;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 4px;
  padding-left: 4px;
  line-height: 1.2;
}
:deep(.jm-node-info) {
  display: flex;
  flex-direction: column;
  border-radius: 6px;
  padding: 4px;
  gap: 4px;
  font-size: 12px;
  color: #555;
  box-sizing: border-box;
  background: #f7f9fa;
}
:deep(.jm-node-label) {
  display: inline-block;
  background: #e0e7ff;
  color: #555;
  border-radius: 4px;
  font-weight: 500;
  font-size: 12px;
  padding: 0 6px;
  margin-right: 4px;
  margin-bottom: 2px;
  line-height: 18px;
}
:deep(.jm-node-flag-multiline) {
  background: #f4f6fa;
  color: #1a1a1a;
  font-family: 'JetBrains Mono', 'Menlo', 'Consolas', monospace;
  font-size: 12px;
  border-radius: 4px;
  padding: 4px 6px;
  margin-top: 2px;
  max-width: 320px;
  overflow-x: auto;
  white-space: pre-line;
  line-height: 1.5;
  word-break: break-all;
}
:deep(.jmnode.root) {
  background: linear-gradient(90deg, #e0e7ff 60%, #b3c6ff 100%);
  color: #222 !important;
  font-size: 18px;
  font-weight: bold;
  box-shadow: 0 4px 16px #e0e7ff;
  border: none !important;
}
:deep(svg.jm-branch) path {
  stroke: #b3c6ff !important;
  stroke-width: 2.2;
  transition: stroke 0.2s;
}
.jsmind-toolbar .dl-icon {
  color: #555;
  font-size: 18px;
}
</style>
