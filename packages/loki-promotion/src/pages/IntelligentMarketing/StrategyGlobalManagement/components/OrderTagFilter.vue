<template>
  <div class="order-tag-filter">
    <Tooltip :content="label">
      <span
        class="order-tag-label"
      >{{ label }}</span>
    </Tooltip>
    <Select
      v-model="orderDate"
      :options="orderDateOptions"
      :placeholder="placeholder1"
      clearable
      class="order-tag-select"
      style="width: 120px;"
    />
    <Select
      v-model="orderCount"
      :options="orderCountOptions"
      :placeholder="placeholder2"
      clearable
      class="order-tag-select"
      style="width: 160px;"
    />
  </div>
</template>

<script setup lang="ts">
  import {
    ref, watch, defineProps, toRefs,
  } from 'vue'
  import { Select, Tooltip } from '@xhs/delight'

  const props = defineProps({
    modelValue: {
      type: Object,
      default: () => ({}),
    },
    orderDateOptions: {
      type: Array,
      default: () => [],
    },
    orderCountOptions: {
      type: Array,
      default: () => [],
    },
    placeholder1: {
      type: String,
      default: '请选择周期',
    },
    placeholder2: {
      type: String,
      default: '请选择订单数量',
    },
    label: {
      type: String,
      default: '',
    },
  })

  const emit = defineEmits(['update:modelValue'])
  const { modelValue } = toRefs(props)
  const orderDate = ref(modelValue.value?.orderDate)
  const orderCount = ref(modelValue.value?.orderCount)

  watch([orderDate, orderCount], ([date, count]) => {
    emit('update:modelValue', {
      orderDate: date,
      orderCount: count,
    })
  })

  watch(modelValue, val => {
    orderDate.value = val?.orderDate
    orderCount.value = val?.orderCount
  })
</script>

<style lang="stylus" scoped>
.order-tag-filter {
  display: flex;
  align-items: center;
  gap: 12px;
  min-height: 32px;
  border-radius: 4px;
  background-color: #f7f7f7;
}
.order-tag-label {
  min-width: 90px;
  max-width: 120px;
  margin-left: 8px;
  color: rgba(0,0,0,0.45);
  font-size: 14px;
  font-weight: 500;
  line-height: 32px;
  display: inline-block;
  text-align: right;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.order-tag-select {
  height: 32px;
  font-size: 14px;
}
</style>
