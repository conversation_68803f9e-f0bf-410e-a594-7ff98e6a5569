import {
  ref,
  computed,
  watch,
  markRaw,
} from 'vue'
import { useStore } from 'vuex'
import { Input, Select } from '@xhs/delight'
import { STRATEGY_TAG_OPTIONS } from '../LaunchPlan/config'
import {
  USER_LIFECYCLE_OPTIONS_V2,
  ORDER_DATE_OPTIONS,
  VALID_ORDER_OPTIONS,
} from '../../Crowd/constants/index'
import OrderTagFilter from './components/OrderTagFilter.vue'
import { strategyGlobalManagementPreview } from '~/services/intelligentMarketing'

export function useStrategyGlobalFilter(jsMindDataRef?: any) {
  const store = useStore()
  const sceneOptions = computed(() => store.state.intelligentMarketing.sceneOptions)
  const userName = computed(() => store.state?.user?.userInfo?.userName)
  const loading = ref(false)
  const present = ref('')
  const filterParam = ref({
    userName: userName.value,
    label: undefined,
    scene: undefined,
    strategyId: undefined,
    launchId: undefined,
    templateId: undefined,
    userPkgId: undefined,
    userLifeCycle: undefined,
    orderTag: undefined,
    effectPeriod: undefined,
  })

  // filterConfig 先用空数组，后续 watch 动态赋值
  const filterConfig = ref({
    expandedCount: 4,
    handleFilter: fetchList,
    filterItems: [
      {
        label: '标签',
        name: 'label',
        component: {
          is: markRaw(Select),
          props: {
            options: STRATEGY_TAG_OPTIONS,
            clearable: true,
          },
        },
      },
      {
        label: '场景',
        name: 'scene',
        component: {
          is: markRaw(Select),
          props: {
            options: [], // 初始为空
            placeholder: '请选择场景',
            clearable: true,
          },
        },
      },
      {
        label: '策略id',
        name: 'strategyId',
        component: {
          is: markRaw(Input),
          props: {
            placeholder: '请输入策略id',
            clearable: true,
          },
        },
      },
      {
        label: '人群包id',
        name: 'userPkgId',
        component: {
          is: markRaw(Input),
          props: {
            placeholder: '请输入人群包id',
            clearable: true,
          },
        },
      },
      {
        label: '投放计划id',
        name: 'launchId',
        component: {
          is: markRaw(Input),
          props: {
            placeholder: '请输入投放计划id',
            clearable: true,
          },
        },
      },
      {
        label: '券模版id',
        name: 'templateId',
        component: {
          is: markRaw(Input),
          props: {
            placeholder: '请输入券模版id',
            clearable: true,
          },
        },
      },
      {
        label: '人群实时标签-生命周期',
        name: 'userLifeCycle',
        component: {
          is: markRaw(Select),
          props: {
            options: USER_LIFECYCLE_OPTIONS_V2.map(opt => ({
              ...opt,
              value: opt.name,
              label: opt.name,
            })),
            multiple: false, // 单选
            clearable: true,
            filterable: true,
            placeholder: '请选择生命周期',
            optionContent: (option: any) => (
              option.tooltip
                ? `${option.name}（${option.tooltip}）`
                : option.name
            ),
          },
        },
      },
      {
        label: '人群实时标签-近期订单',
        name: 'orderTag',
        component: {
          is: markRaw(OrderTagFilter),
          props: {
            orderDateOptions: ORDER_DATE_OPTIONS.map(opt => ({
              ...opt,
              value: opt.name,
              label: opt.name,
            })),
            orderCountOptions: VALID_ORDER_OPTIONS.map(opt => ({
              ...opt,
              value: opt.name,
              label: opt.name,
            })),
            placeholder1: '请选择周期',
            placeholder2: '请选择订单数量',
            label: '人群实时标签-近期订单',
          },
        },
      },
      {
        label: '效果数据周期',
        name: 'effectPeriod',
        component: {
          is: markRaw(Select),
          props: {
            options: [
              { label: '昨天日均', value: 'yesterday_avg' },
              { label: '近7天日均', value: '7days_avg' },
              { label: '近30天日均', value: '30days_avg' },
            ],
            placeholder: '请选择效果数据周期',
            clearable: true,
          },
        },
      },
    ],
  })

  // 响应式赋值 options
  watch(sceneOptions, val => {
    const sceneItem = filterConfig.value.filterItems.find(item => item.name === 'scene')
    if (sceneItem) {
      sceneItem.component.props.options = val || []
    }
  }, { immediate: true })

  async function fetchList() {
    try {
      loading.value = true
      filterParam.value.userName = userName.value
      const { orderTag } = filterParam.value
      const params = { ...filterParam.value }
      if (orderTag?.orderDate || orderTag?.orderCount) {
        params.orderTag = `${orderTag?.orderDate || ''}${orderTag?.orderCount || ''}`
      } else {
        delete params.orderTag
      }
      const { config, present: presentValue } = await strategyGlobalManagementPreview(params)
      present.value = presentValue || ''
      if (jsMindDataRef) {
        jsMindDataRef.value = {
          meta: { name: '策略可视化', author: 'jin', version: '10' },
          format: 'node_tree',
          data: convertToJsMindNodeTree(config),
        }
      }
    } catch (error) {
      // console.error('获取策略预览失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 通用节点渲染方法
  function renderNodeFields(node: any, fields: { label: string; key: string }[]) {
    const titleLine = (node.name || node.label)
      ? `<div><span class="jm-node-label">${node.nodeType || ''}名称：</span>${node.name || node.label || ''}</div>`
      : ''
    return `
    <div class="jm-node-title">${node.nodeType || ''}</div>
    <div class="jm-node-info">
      ${titleLine}
      ${fields.map(f => `<div><span class="jm-node-label">${f.label}：</span>${node[f.key] ?? ''}</div>`).join('')}
    </div>
  `
  }

  // 动态生成 jsMind 节点 topic 的函数
  function renderNodeTopic(node: any) {
    switch (node.nodeType) {
      // case '根节点':
      //   return `<div style="font-size: 18px;font-weight: 500;">${node.name || ''}</div>
      //       <div style="font-size: 14px;margin-top: 4px;">${node.updateAt || ''}</div>
      //   `
      case '根节点':
        return `
          <div class="jm-node-title">${node.name || ''}</div>
          <div class="jm-node-info">
            <div>${node.updateAt || ''}</div>
          </div>
        `
      case '权益池':
        return renderNodeFields(node, [])
      case '权益组':
        return renderNodeFields(node, [
          { label: '发放模型', key: 'distributionModel' },
          { label: '概率', key: 'rate' },
        ])
      case '权益':
        return renderNodeFields(node, [
          { label: '权益模板ID', key: 'templateId' },
          // { label: '权益名称', key: 'prizeRuleName' },
          { label: '领取量', key: 'claimCnt' },
          { label: '核销量', key: 'usage' },
        ])
      case '人群包':
        return renderNodeFields(node, [
          { label: '人群量级', key: 'crowdCount' },
          { label: '人群规则', key: 'ruleTag' },
        ])
      case '实验': {
        let flagContent = ''
        if (node.flag !== null) {
          let flag
          // 如果 flag 是字符串，尝试解析为 JSON 对象
          if (typeof node.flag === 'string' && node.flag.startsWith('{')) {
            flag = JSON.parse(node.flag)
          }

          const expr = generateExpression(flag)
          flagContent = `<div class="jm-node-flag-multiline" title="${expr.replace(/\n/g, ' ')}">${expr.replace(/\n/g, '<br/>')}</div>`
        } else {
          flagContent = ''
        }
        return `
          <div class="jm-node-title">${node.nodeType || ''}</div>
          <div class="jm-node-info">
          <div><span class="jm-node-label">${node.nodeType || ''}名称：</span>${node.name || node.label || ''}</div>
            <div><span class="jm-node-label">实验平台参数：</span>${flagContent}</div>
          </div>
        `
      }
      case '触点':
        return renderNodeFields(node, [
          { label: '场景', key: 'scene' },
          { label: '容器模版', key: 'containerTemplate' },
        ])
      case '触发规则':
        return renderNodeFields(node, [
          { label: '场景', key: 'scene' },
          { label: '时机', key: 'executionTime' },
        ])
      case '容器':
        return renderNodeFields(node, [
          { label: '容器模版', key: 'containerTemplate' },
        ])
      case '策略':
        return renderNodeFields(node, [
          { label: '策略总结', key: 'summary' },
        ])
      case '场景':
        return `
          <div class="jm-node-title">${node.name || ''}</div>
          <div class="jm-node-info">
            <div>${node.summary || ''}</div>
          </div>
        `
      default:
        return `<div class="jm-node-title">${node.name || ''}</div>`
    }
  }

  // 递归生成实验参数表达式
  function generateExpression(node: any, isRoot = true, parentConnector?: string): string {
    if (!node || typeof node !== 'object') return ''
    if (!Array.isArray(node.subExperimentConfigV2List) || node.subExperimentConfigV2List.length === 0) {
      // 叶子节点
      return (!node.value && !node.flag) ? '' : `${node.flag || ''} = ${node.value || ''}`
    }
    const childrenExprs = node.subExperimentConfigV2List.map((child: any) => generateExpression(child, false, node.subExperimentRelation))
    const connector = node.subExperimentRelation === 'AND' ? ' 且 ' : ' 或 '
    if (isRoot) {
      const rootConnector = `\n${connector.trim()}\n`
      return childrenExprs.join(rootConnector)
    }
    const expr = childrenExprs.join(connector)
    const needsWrap = childrenExprs.length > 1 || (parentConnector && parentConnector !== node.subExperimentRelation)
    return needsWrap ? `(${expr})` : expr
  }

  // 背景色设置
  function getNodeBgColor({
    node, level, branchIdx, mode = 'branch',
  }: {
    node: any
    level: number
    branchIdx: number
    mode?: 'branch' | 'level' | 'type'
  }): string {
    // 分支色板
    const branchColors = [
      '#E6F7FF', '#FFF7E6', '#F6FFED', '#FFF0F6', '#F9F0FF', '#F0F5FF', '#FFF1F0',
    ]
    // 层级色板
    const levelColors = [
      '#F5F7FA', '#E6F7FF', '#FFF7E6', '#F6FFED', '#FFF0F6', '#F9F0FF', '#F0F5FF', '#FFF1F0',
    ]
    // 类型色板
    const typeColors: Record<string, string> = {
      权益组: '#F5F7FA',
      权益: '#E6F7FF',
      人群包: '#FFF7E6',
      实验: '#F6FFED',
      触点: '#FFF0F6',
      触发规则: '#F9F0FF',
      容器: '#F0F5FF',
      策略: '#FFF1F0',
      根节点: '#ffffff',
    }
    if (mode === 'branch') {
      if (level === 1) return branchColors[branchIdx % branchColors.length]
      if (level > 1 && node.parent && node.parent.branchColor) return node.parent.branchColor
      return levelColors[0]
    }
    if (mode === 'level') {
      return levelColors[level % levelColors.length]
    }
    if (mode === 'type') {
      return typeColors[node.nodeType] || '#ffffff'
    }
    return '#ffffff'
  }

  function convertToJsMindNodeTree(node: any, level: number = 0, branchIdx: number = 0, colorMode: 'branch' | 'level' | 'type' = 'level') {
    const bgColor = getNodeBgColor({
      node, level, branchIdx, mode: colorMode,
    })
    // id 必须为字符串且唯一
    const safeId = node?.id != null ? String(node.id) : `auto_${level}_${branchIdx}_${Date.now()}_${Math.random()}`
    const result: any = {
      id: safeId,
      topic: renderNodeTopic(node),
      branchColor: colorMode === 'level' ? bgColor : undefined, // 仅递归用
      'background-color': bgColor,
      expanded: node?.childrenNum <= 1,
    }
    let childrenArr: any[] = []
    if (node.children) {
      if (Array.isArray(node.children)) {
        childrenArr = node.children
      } else if (typeof node.children === 'string') {
        try {
          childrenArr = JSON.parse(node.children)
        } catch (e) {
          childrenArr = []
        }
      }
    }
    if (childrenArr.length > 0) {
      result.children = childrenArr.map((child: any, idx: number) => convertToJsMindNodeTree(child, level + 1, colorMode === 'level' && level === 0 ? idx : branchIdx, colorMode))
    }
    return result
  }

  return {
    present,
    filterParam,
    filterConfig,
    fetchList,
    loading,
  }
}
