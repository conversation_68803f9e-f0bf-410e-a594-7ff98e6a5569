import { toast } from '@xhs/delight'
import debounce from 'lodash/debounce'
import { getPromotionCycleList } from '~/services/intelligentMarketing'
import { formatTime } from './utils'

export const STRATEGY_TAG_OPTIONS = [
  {
    name: '拉新',
    value: 'pull_new',
  },
  {
    name: '复购',
    value: 'repurchase',
  },
  {
    name: '流失',
    value: 'lost',
  },
  {
    name: '活动',
    value: 'activity',
  },
  {
    name: '沉默补贴',
    value: 'inactive_subsidy',
  },
  {
    name: '直播拉新',
    value: 'live_pull_new',
  },
  {
    name: '营销表达',
    value: 'marketing_expression',
  },
  {
    name: '单单返',
    value: 'order_rebate',
  },
  {
    name: '首评返现',
    value: 'review_rebate',
  },
  {
    name: '行业拉新',
    value: 'industry_pull_new',
  },
  {
    name: '行业活动',
    value: 'industry_activity',
  },
  {
    name: '直播间玩法',
    value: 'living_room_play',
  },
  {
    name: '裂变拉新',
    value: 'fission_pull_new',
  },
  {
    name: '老客日常补贴',
    value: 'regular_customer_daily_subsidy',
  },
  {
    name: '购物车商家券',
    value: 'cart_seller_coupon',
  },
  {
    name: '领福利频道',
    value: 'welfare_claim_channel',
  },
  {
    name: '口令红包',
    value: 'passcode_packet',
  },
  {
    name: '测试',
    value: 'test',
  },
]

export const HOLDOUT_OPTIONS = [
  {
    label: '无',
    value: '0',
  },
  {
    label: '季度hold out实验组',
    value: '2',
  },
  {
    label: '季度hold out对照组',
    value: '1',
  },
]

export const STATUS_OPTIONS = [
  {
    label: '未开始',
    value: 1,
  },
  {
    label: '进行中',
    value: 2,
  },
  {
    label: '已下线',
    value: 3,
  },
  {
    label: '已结束',
    value: 4,
  },
  {
    label: '暂停中',
    value: 5,
  },
]

export const COPY_STRATEGY_TYPE = [
  {
    label: '全部',
    value: 'ALL',
  },
  {
    label: '未下线策略',
    value: 'ONLINE',
  },
  {
    label: '指定策略',
    value: 'ASSIGNED',
  },
]

export const ADD_DATE_SUFFIX_OPTIONS = [
  {
    label: '是',
    value: true,
  },
  {
    label: '否',
    value: false,
  },
]

export const frontierTypeOptions = [
  {
    label: '境内',
    value: 'domestic',
  },
  {
    label: '境外',
    value: 'abroad',
  },
]

const xReactionForSelectPeriodPlanId = field => {
  const service = async (filterVal: any) => {
    const res = await getPromotionCycleList({
      pageNum: 1,
      pageSize: 50,
      periodName: filterVal || '',
    })

    field.setComponentProps({
      options: res.periodPlan?.map(act => ({
        label: act.periodName,
        value: act.id,
        roundInfo: act.roundInfo || [],
        time: {
          start: act.startTime,
          end: act.endTime,
        },
      })),
    })
  }

  const customFilter = debounce(async filterVal => {
    try {
      field.setComponentProps({
        loading: true,
      })

      // 不管filterVal是否为空，都执行搜索
      await service(filterVal)
    } catch (error: any) {
      // 使用原始getFilter函数中相同的错误处理逻辑
      toast.danger({
        strong: true,
        description: `获取触发点列表失败: ${error?.data?.result?.message || JSON.stringify(error)}`,
      })
    } finally {
      field.setComponentProps({
        loading: false,
      })
    }
  }, 300)

  field.setComponentProps({
    filterable: true,
    remote: true,
    filter: customFilter,
  })

  // 立即触发服务加载初始选项
  service('')
}

// 抽离出处理轮次选项的函数
export const handlePeriodPlanIdChange = $self => {
  const field = $self
  const form = field.form

  const periodPlanId = field.value
  if (!periodPlanId) {
    form.setFieldState('roundName', state => {
      state.visible = false
      state.componentProps = { ...state.componentProps, options: [] }
    })

    // 当没有选择periodPlanId时，启用时间选择
    form.setFieldState('planTime', state => {
      state.disabled = false
    })

    // 当periodPlanId为空时，将periodPlanName也设置为空
    form.values.periodPlanName = ''

    return
  }

  const options = field.componentProps.options || []
  const selectedOption = options.find(option => option.value === periodPlanId)
  const roundInfo = selectedOption?.roundInfo || []

  // 将选中选项的label值赋给periodPlanName
  console.log(selectedOption?.label)
  form.values.periodPlanName = selectedOption?.label || ''

  form.setFieldState('roundName', state => {
    state.visible = roundInfo.length > 0
    state.componentProps = {
      ...state.componentProps,
      options: roundInfo.map(item => ({
        label: item.roundName,
        value: item.roundName,
        time: {
          start: item.startTime,
          end: item.endTime,
        },
      })),
    }
  })

  // 如果没有轮次信息，则将所选周期的时间赋值给planTime字段
  if (!roundInfo.length && selectedOption?.time) {
    form.setFieldState('planTime', state => {
      state.value = {
        start: formatTime(selectedOption.time.start),
        end: formatTime(selectedOption.time.end),
      }
    })
  }

  // 当选择了periodPlanId时，禁用时间选择
  form.setFieldState('planTime', state => {
    state.disabled = true
  })
}

export const handleRoundNameChange = $self => {
  const field = $self
  const form = field.form

  const roundName = field.value
  if (!roundName) {
    return
  }

  const options = field.componentProps.options || []
  const selectedOption = options.find(option => option.value === roundName)

  if (selectedOption?.time) {
    form.setFieldState('planTime', state => {
      state.value = {
        start: formatTime(selectedOption.time.start),
        end: formatTime(selectedOption.time.end),
      }
    })
  }
}

export const getConfig = (type?: string) => ({
  type: 'void',
  'x-decorator': 'FormLayout',
  'x-disabled': '{{ disabled }}',
  properties: {
    name: {
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '投放计划名称',
        required: true,
      },
      'x-component': 'Input',
      'x-component-props': {
        maxLength: 30,
        placeholder: '请输入',
        clearable: true,
      },
      'x-validator': {
        required: true,
      },
    },
    label: {
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '标签',
        required: true,
      },
      'x-component': 'Select',
      'x-component-props': {
        placeholder: '请选择',
        clearable: true,
        options: STRATEGY_TAG_OPTIONS,
      },
      'x-validator': {
        required: true,
      },
    },
    periodPlanId: {
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '大促周期',
        required: true,
      },
      'x-component': 'Select',
      'x-component-props': {
        placeholder: '请选择',
        clearable: true,
        filterable: true,
        remote: true,
      },
      'x-reactions': [
        {
          dependencies: ['label'],
          fulfill: {
            state: {
              visible: '{{$deps[0] === "activity"}}',
            },
          },
        },
        xReactionForSelectPeriodPlanId,
        {
          // 使用抽离的函数处理轮次选项
          effects: ['onFieldValueChange'],
          fulfill: {
            run: '{{ handlePeriodPlanIdChange($self) }}',
          },
        },
      ],
      'x-validator': {
        required: false,
      },
    },
    roundName: {
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '轮次',
        required: true,
      },
      'x-component': 'Select',
      'x-component-props': {
        placeholder: '请选择',
        clearable: true,
        options: [],
      },
      'x-validator': {
        required: true,
      },
      'x-reactions': [
        {
          dependencies: ['periodPlanId'],
          fulfill: {
            state: {
              visible: false,
            },
          },
        },
        {
          // 使用抽离的函数处理轮次选项
          effects: ['onFieldValueChange'],
          fulfill: {
            run: '{{ handleRoundNameChange($self) }}',
          },
        },
      ],
    },
    planTime: {
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '时间',
        required: true,
      },
      'x-component': 'DatePicker',
      'x-component-props': {
        isRange: true,
        unit: 'second',
        placeholder: {
          start: '请选择开始时间',
          end: '请选择结束时间',
        },
      },
      'x-validator': {
        required: true,
        // validateStrategyTimeRange: true,
      },
    },
    frontierType: {
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '生效范围',
        required: true,
      },
      'x-component': 'Radio.RadioGroup',
      'x-component-props': {
        options: frontierTypeOptions,
      },
      default: 'domestic',
      'x-validator': {
        required: true,
      },
    },
    experimentConfigsV2: {
      type: 'object',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '实验平台参数',
        wrapperWidth: 1000,
        help: '条件项过多时可以左右滑动',
      },
      'x-component': 'TestParamsInputV2',
      'x-validator': {
        validateExperimentMap: true,
      },
      'x-component-props': {},
      default: {
        subExperimentRelation: 'AND',
        subExperimentConfigs: [
          // {
          //   conditionType: '',
          //   operator: '',
          //   value: '',
          // },
        ],
      },
    },
    copyStrategyType: type !== 'copy' ? undefined : {
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '复制策略范围',
        required: true,
      },
      'x-component': 'Select',
      'x-component-props': {
        placeholder: '请选择',
        clearable: true,
        options: COPY_STRATEGY_TYPE,
      },
      'x-validator': {
        required: true,
      },
      'x-reactions': {
        target: 'copyStrategyIds',
        fulfill: {
          state: {
            visible: '{{$self.value == "ASSIGNED"}}',
          },
        },
      },
    },
    copyStrategyIds: type !== 'copy' ? undefined : {
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '复制策略选择',
        required: true,
      },
      'x-component': 'Select',
      'x-component-props': {
        placeholder: '请选择',
        clearable: true,
        multiple: true,
      },
      'x-validator': {
        required: true,
      },
    },
    copyStrategyNameSuffixType: type !== 'copy' ? undefined : {
      type: 'boolean',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        label: '复制后策略名称添加日期后缀',
        required: true,
      },
      'x-component': 'Select',
      'x-component-props': {
        placeholder: '请选择',
        clearable: true,
        options: ADD_DATE_SUFFIX_OPTIONS,
      },
      default: true,
      'x-validator': {
        required: true,
      },
    },
  },
})

export const config = getConfig()
