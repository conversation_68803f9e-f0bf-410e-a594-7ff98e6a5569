<template>
  <DelightForm
    ref="formRef"
    name="layout"
    :config="config"
    :components="{
      DelightForm,
      FormLayout,
      FormItem,
      Input,
      Select,
      DatePicker,
      Radio,
      TestParamsInputV2
    }"
    :registry-rules="{
      validateExperimentMap,
    }"
    :scope="{
      disabled,
      handleRoundNameChange,
      handlePeriodPlanIdChange,
    }"
  />
  <div
    v-if="!disabled"
    class="footer"
  >
    <Button
      class="btn"
      type="default"
      @click="()=>{$router.back()}"
    >
      取消
    </Button>
    <Button
      class="btn"
      type="primary"
      :loading="taskLoading"
      @click="create"
    >
      {{ buttonText }}
    </Button>
  </div>
  <StategyGroupV2
    v-if="type==='view'"
    :plan-id="planId"
    :plan-name="planName"
    :is-launch-plan="true"
    :launch-plan-status="launchPlanStatus"
  />
</template>
  <script setup lang="tsx">
  import {
    Button,
    Modal,
    Text,
    toast,
  } from '@xhs/delight'
  import {
    DatePicker,
    DelightForm,
    Input,
    Layout,
    Radio,
    Select,
  } from '@xhs/delight-formily'
  import { cloneDeep } from 'lodash'
  import {
    computed, h,
    onMounted,
    ref,
  } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { useBroadcastChannel } from '~/hooks/useCrossPageCommunication'
  import { STRATEGY_STATE_MAP } from '~/pages/IntelligentMarketing/const'
  import TestParamsInputV2 from '~/pages/IntelligentMarketing/Group/Nodes/Experiment/TestParamsInputV2/Index.vue'
  import {
    validateExperimentMap,
  } from '~/pages/IntelligentMarketing/Group/Nodes/Experiment/TestParamsInputV2/utils'
  import { getFrontState } from '~/pages/IntelligentMarketing/utils'
  import {
    copyLaunchPlan,
    createLaunchPlan,
    editLaunchPlan,
    getLaunchPlanDetail,
    getPromotionCycleList,
    getStrategyList,
  } from '../../../services/intelligentMarketing'
  import StategyGroupV2 from '../Group/StrategyGroupV2.vue'
  import { isExperimentValid } from '../utils/experiment'
  import { getConfig, handlePeriodPlanIdChange, handleRoundNameChange } from './config'
  import {
    formatTime,
    getUnixTime,
  } from './utils'

  const FormLayout = Layout.FormLayout
  const FormItem = Layout.FormItem

  const route = useRoute()
  const router = useRouter()
  const type = route.query.type
  const disabled = type === 'view'
  const planId = Number(route.query?.id)
  const planName = ref('投放计划')
  const launchPlanStatus = ref(null)
  const taskLoading = ref(false)
  const config = ref(getConfig(type as string))

  const formRef = ref()

  const buttonText = computed(() => {
    if (type === 'copy') {
      return '确认复制'
    } if (type === 'edit') {
      return '保存编辑'
    } if (type === 'create') {
      return '保存创建'
    }
    return ''
  })

  // 添加加载周期计划选项的方法
  const loadPeriodPlanOptions = async (keyword = '') => {
    try {
      // 假设这里调用获取周期计划列表的API
      // 需要根据你的实际API替换
      const res = await getPromotionCycleList({
        pageNum: 1,
        pageSize: 50,
        periodName: keyword || '',
      })
      const options = (res?.list || []).map(item => ({
        label: item.name,
        value: item.id,
      }))

      // 更新选项到表单
      formRef.value.form.setFieldState('periodPlanId', state => {
        state.dataSource = options
      })

      return options
    } catch (error) {
      console.error('加载周期计划选项失败', error)
      return []
    }
  }

  const getDetail = () => {
    const params = {
      launchPlanId: planId,
    }

    getLaunchPlanDetail(params).then(res => {
      if (Array.isArray(res.launchPlanList) && res.launchPlanList.length) {
        const data = res.launchPlanList[0]
        const oldExperimentConfigs = data.experimentConfigs
        let newExperimentConfigs = data.experimentConfigsV2
        if (oldExperimentConfigs?.length && type === 'copy') {
          // 删除掉老版本实验参数的holdout实验
          // 复制操作下筛选掉newExperimentConfigs.subExperimentConfigV2List中flag === egrowth_month_holdout的数据
          const subExperimentConfigV2List = newExperimentConfigs.subExperimentConfigV2List.filter(item => item.flag !== 'egrowth_month_holdout')
          newExperimentConfigs = {
            ...newExperimentConfigs,
            subExperimentConfigV2List,
          }
        }
        const experimentConfigs = convertExperimentConfig(newExperimentConfigs, false)

        // 提前设置periodPlanName，确保搜索参数存在
        formRef.value.form.values.periodPlanName = data?.periodPlanName || ''

        // 如果存在periodPlanId，先加载对应的选项
        if (data?.periodPlanId) {
          // 假设此处有一个loadPeriodPlanOptions方法，用于加载周期计划选项
          loadPeriodPlanOptions(data?.periodPlanName).then(() => {
            // 等选项加载完成后再设置表单值
            const setValues:any = {
              name: data.name,
              label: data.label,
              planTime: {
                start: formatTime(data.startAt),
                end: formatTime(data.endAt),
              },
              experimentConfigsV2: experimentConfigs,
              frontierType: data?.frontierType || 'domestic',
              periodPlanId: data?.periodPlanId,
              roundName: data?.roundName,
              periodPlanName: data?.periodPlanName,
            }
            if (type === 'copy') {
              setValues.copyStrategyType = 'ALL'
            }
            formRef.value.form.setValues(setValues)

            // 手动触发一次periodPlanId变更，确保关联字段正确更新
            handlePeriodPlanIdChange(formRef.value.form.query('periodPlanId').take())
          })
        } else {
          // 无periodPlanId时直接设置值
          const setValues:any = {
            name: data.name,
            label: data.label,
            planTime: {
              start: formatTime(data.startAt),
              end: formatTime(data.endAt),
            },
            experimentConfigsV2: experimentConfigs,
            frontierType: data?.frontierType || 'domestic',
            periodPlanId: data?.periodPlanId,
            roundName: data?.roundName,
            periodPlanName: data?.periodPlanName,
          }
          if (type === 'copy') {
            setValues.copyStrategyType = 'ALL'
          }
          formRef.value.form.setValues(setValues)
        }

        launchPlanStatus.value = data.status
        planName.value = data.name
      }
    })

    if (type === 'copy') {
      const params: any = {
        page: 1,
        pageSize: 9999,
        launchPlanId: planId,
        version: 1,
      }
      getStrategyList(params).then(res => {
        const options = (res.strategies || []).map(item => ({
          label: `【${STRATEGY_STATE_MAP[getFrontState(item)]}】${item.name}`,
          value: item.id,
        }))
        formRef.value.form.setFieldState('copyStrategyIds', state => {
          state.componentProps.options = options
        })
      })
    }
  }

  function convertExperimentConfig(config, toV2 = true) {
    // 如果不是对象或是 null，直接返回
    if (typeof config !== 'object' || config === null) {
      return config
    }

    // 创建一个新对象来存储转换后的结果
    const newConfig = Array.isArray(config) ? [] : {}

    const configsMapping = {
      from: toV2 ? 'subExperimentConfigs' : 'subExperimentConfigV2List',
      to: toV2 ? 'subExperimentConfigV2List' : 'subExperimentConfigs',
    }

    if (Array.isArray(config)) {
      return config.map(item => convertExperimentConfig(item, toV2))
    }

    Object.keys(config).forEach(key => {
      if (key === configsMapping.from) {
        newConfig[configsMapping.to] = convertExperimentConfig(config[key], toV2)
      } else {
        newConfig[key] = convertExperimentConfig(config[key], toV2)
      }
    })

    return newConfig
  }
  const { checkAndSendInstanceNode } = useBroadcastChannel({
    name: 'SOP-channel',
  })
  const create = async () => {
    try {
      // 第一步：表单验证
      try {
        await formRef.value.form.validate()
      } catch (error) {
        toast.danger({
          description: '表单校验失败',
          closeable: true,
        })
        return
      }

      // 准备参数
      const expirements = convertExperimentConfig(cloneDeep(formRef.value.form.values.experimentConfigsV2))
      const params = {
        launchPlan: {
          id: Number(planId),
          name: formRef.value.form.values.name,
          label: formRef.value.form.values.label,
          experimentConfigsV2: expirements,
          experimentConfigs: [],
          startAt: getUnixTime(formRef.value.form.values.planTime.start),
          endAt: getUnixTime(formRef.value.form.values.planTime.end),
          version: 1,
          frontierType: formRef.value.form.values?.frontierType,
          periodPlanId: formRef.value.form.values?.periodPlanId,
          roundName: formRef.value.form.values?.roundName,
          periodPlanName: formRef.value.form.values?.periodPlanName,
        },
        copyStrategyType: formRef.value.form.values.copyStrategyType,
        copyStrategyNameSuffixType: formRef.value.form.values.copyStrategyNameSuffixType,
        strategyIds: formRef.value.form.values.copyStrategyIds,
      }

      // 针对复制功能, 作一个特别判断
      if (type === 'copy' && params.launchPlan.name === planName.value) {
        const isContinue = await new Promise(resolve => {
          Modal.warning({
            title: '提示',
            content: h(Text, {}, {
              default: () => '名称与原投放计划名称相同, 是否确认提交？',
            }),
            onConfirm: close => {
              close?.()
              resolve(true)
            },
            onCancel: close => {
              close?.()
              resolve(false)
            },
          })
        })
        if (!isContinue) {
          return
        }
      }

      if (type === 'create') {
        delete params.launchPlan.id
      }
      if (type !== 'copy') {
        delete params.copyStrategyType
        delete params.strategyIds
        delete params.copyStrategyNameSuffixType
      }

      // 第二步：预检查接口调用
      const checkRes = await isExperimentValid({
        experimentConfigsV2: expirements,
      })

      if (!checkRes) {
        return
      }

      // 第三步：根据类型选择请求方法
      const requestMap = {
        create: createLaunchPlan,
        edit: editLaunchPlan,
        copy: copyLaunchPlan,
      }

      const request = requestMap[type]
      if (!request) {
        throw new Error('未知的操作类型')
      }

      // 第四步：发起创建请求
      taskLoading.value = true

      const res = await request(params)

      toast.success({ description: '创建成功', closeable: true })

      if (res?.launchPlan?.id) {
        checkAndSendInstanceNode([res?.launchPlan?.id])
        router.push({
          name: 'LaunchPlanDetailV2',
          query: {
            type: 'view',
            id: res.launchPlan.id,
          },
        })
        setTimeout(() => {
          window.location.reload()
        }, 500)
      } else {
        router.push({
          name: 'LaunchPlan',
        })
      }
    } catch (error) {
      toast.danger({
        description: error.message || error.msg || '操作失败',
        closeable: true,
      })
    } finally {
      taskLoading.value = false
    }
  }

  onMounted(() => {
    if (type !== 'create') {
      getDetail()
    }
    // formRef.value.form.setValues(cloneDeep(store.state.strategyTree.activeNode?.bizData))
  })

</script>

<style lang="stylus" scoped>
  .footer {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 20px;
    .btn {
      margin: 0 10px;
    }
  }
</style>
