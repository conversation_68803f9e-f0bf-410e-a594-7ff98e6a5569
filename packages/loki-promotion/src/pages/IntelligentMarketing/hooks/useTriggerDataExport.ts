import { toast } from '@xhs/delight'
import { TASK_STATUS } from '@xhs/delight-material-ark-task-modal'
import download from 'loki-shared/utils/download'
import { ref, Ref } from 'vue'
import useLongTask from '~/hooks/useLongTask'
import { submitLongTask, getLongTaskDetail } from '~/services/longTask'
import { IILongTaskDetail } from '~/types/longTask'

interface TriggerDataExportOptions {
  modalVisible?: Ref<boolean>
  onSuccess?: (result: IILongTaskDetail['result']) => void
}

export function useTriggerDataExport(options?: TriggerDataExportOptions) {
  const taskPercent = ref(0)
  const longTaskLoading = ref(false)
  const taskResult = ref<IILongTaskDetail['result']>()
  const exportModalVisible = ref(false)

  const handleExport = () => {
    if (taskResult.value?.fileUrl) {
      download(taskResult.value.fileUrl, '策略触发数据.xlsx')
    }
  }

  const handleLongTaskFinish = (detail: IILongTaskDetail) => {
    const { status, result } = detail
    longTaskLoading.value = false
    if (status === TASK_STATUS.TIMEOUT) {
      toast.danger({ description: result.msg || '任务超时，请稍后重试', closeable: true })
    } else if (status === TASK_STATUS.EXCEPTION) {
      toast.danger({ description: result.msg || '任务失败，请稍后重试', closeable: true })
    } else {
      taskResult.value = result
      // 如果提供了onSuccess回调，则调用它
      if (options?.onSuccess) {
        options.onSuccess(result)
      }
    }
  }

  const { submit, polling } = useLongTask({
    submitFn: submitLongTask,
    detailFn: getLongTaskDetail,
    isFinish: ({ status }) => TASK_STATUS.FINISHED === status,
    onProgress: ({ finishedCount, totalCount }) => {
      const p = Math.floor((finishedCount / totalCount) * 100)
      taskPercent.value = Number.isInteger(p) ? p : 0
    },
    onFinish: handleLongTaskFinish,
    onError: () => {
      longTaskLoading.value = false
      taskResult.value = null
      toast.danger({ description: '任务失败，请稍后重试', closeable: true })
    },
  })

  function handleLongTask(extra: Record<string, any>) {
    longTaskLoading.value = true
    const payload = {
      taskName: 'download_strategy_trigger_data',
      moduleName: 'marketing',
      input: {
        fileUrl: 'https://fe-video-qc.xhscdn.com/fe-platform-file/104101b831i9tiheu7806c5q544t00000000003anbkejs.xlsx',
        extra,
      },
    }
    submit(payload)
  }

  return {
    exportPolling: polling,
    exportModalVisible,
    taskResult,
    taskPercent,
    handleExport,
    handleLongTask,
  }
}
