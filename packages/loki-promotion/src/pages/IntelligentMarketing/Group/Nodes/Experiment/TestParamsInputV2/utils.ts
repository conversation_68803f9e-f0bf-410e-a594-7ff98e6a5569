import { ExperimentConfigs, IConditionConfig } from '~/types/intelligentMarketing'

export const checkExperimentIsValid = (configs: ExperimentConfigs[]) => {
  for (let i = 0; i < configs.length; i++) {
    const item = configs[i]
    // 如果不是一个条件组的话
    if (!item.subExperimentConfigs) {
      // 条件项 运算符 值 有一项为空就报错
      if (item.flag === '' || item.value === '') {
        return false
      }
    } else if (!checkExperimentIsValid(item.subExperimentConfigs)) {
      return false
    }
  }
  return true
}

export const convertExperimentConfig = (config, toV2 = true) => {
  // 如果不是对象或是 null，直接返回
  if (typeof config !== 'object' || config === null) {
    return config
  }

  // 创建一个新对象来存储转换后的结果
  const newConfig = Array.isArray(config) ? [] : {}

  const configsMapping = {
    from: toV2 ? 'subExperimentConfigV2List' : 'subExperimentConfigs',
    to: toV2 ? 'subExperimentConfigs' : 'subExperimentConfigV2List',
  }

  if (Array.isArray(config)) {
    return config.map(item => convertExperimentConfig(item, toV2))
  }

  Object.keys(config).forEach(key => {
    if (key === configsMapping.from) {
      newConfig[configsMapping.to] = convertExperimentConfig(config[key], toV2)
    } else {
      newConfig[key] = convertExperimentConfig(config[key], toV2)
    }
  })

  return newConfig
}

// 校验条件的完整性
export const validateExperimentMap = (val: ExperimentConfigs) => {
  const newVal = convertExperimentConfig(val)
  if (newVal.subExperimentConfigs?.length === 0) return ''
  if (!checkExperimentIsValid(newVal.subExperimentConfigs)) {
    return {
      type: 'error',
      message: '任何一项条件项不可为空',
    }
  }
  return ''
}

export const validateExperimentMapForm = (rule, value, callback) => {
  const newVal = convertExperimentConfig(value)
  if (newVal.subExperimentConfigs.length === 0) callback()
  if (!checkExperimentIsValid(newVal.subExperimentConfigs)) {
    callback(new Error('任何一项条件项不可为空'))
  }
  callback()
}

const checkConditionIsValid = (configs: IConditionConfig[]) => {
  for (let i = 0; i < configs.length; i++) {
    const item = configs[i]
    // 如果不是一个条件组的话
    if (!item.subConditionConfigs) {
      // 条件项 运算符 值 有一项为空就报错
      if (item.operator === '' || item.value === '' || item.conditionType === '') {
        return false
      }
    } else if (!checkConditionIsValid(item.subConditionConfigs)) {
      return false
    }
  }
  return true
}

// 校验条件的完整性
export const validateConditionMap = (rule, value, callback) => {
  if (value.subConditionConfigs.length === 0) return callback()
  if (!checkConditionIsValid(value.subConditionConfigs)) {
    callback(new Error('任何一项条件项不可为空'))
  }
  return callback()
}
