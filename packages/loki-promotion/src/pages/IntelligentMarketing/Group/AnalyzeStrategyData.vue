<template>
  <Spinner :spinning="loading">
    <Text
      link
      class="back-link"
      @click="handleBack"
    >{{ backText }}</Text>
    <Panel
      title=""
      class="strategy-panel"
    >
      <Space
        direction="vertical"
        align="start"
        style="width: 100%"
      >
        <div class="strategy-info">
          <Text>策略id：{{ strategyId }}</Text>
          <Text>触发策略时间：{{ timestampToString(formattedTime) }}</Text>
          <div class="input-range">
            <Text>请输入日均预估触发人数</Text>
            <Input
              v-model="formValue.estimateTriggerStartNum"
              clearable
              type="number"
              placeholder="请输入"
              class="number-input"
            />
            <Text>到</Text>
            <Input
              v-model="formValue.estimateTriggerEndNum"
              clearable
              type="number"
              placeholder="请输入"
              class="number-input"
            />
          </div>
          <Button
            type="secondary"
            :loading="submitLoading"
            @click="confirm"
          >确认
          </Button>
        </div>
      </Space>
    </Panel>
    <Panel
      title="今日策略触发情况"
      class="strategy-panel"
    >
      <div class="number-card">
        <div class="item">
          <Text
            type="h5"
            color="warning"
          >{{ todayTriggerNum || '暂无' }}</Text>
          <Text
            type="h4"
            color="primary"
          >今日实际触发人数</Text>
        </div>
        <div class="item">
          <Text
            type="h5"
            color="warning"
          >{{ `${formValue.estimateTriggerStartNum || '暂无'} - ${formValue.estimateTriggerEndNum || '暂无'}` }}</Text>
          <Text
            type="h4"
            color="primary"
          >日均预估触发人数</Text>
        </div>
      </div>
    </Panel>
    <Panel
      title="近7天策略触发情况"
      class="strategy-panel"
    >
      <div class="weekly-data">
        <Text
          v-if="!tableData.day.length && !tableData.total.length"
          type="description"
        >暂无数据</Text>
        <div
          v-else
          class="weekly-stats"
        >
          <!-- 这里可以添加图表或数据展示组件 -->
          <div class="header-container">
            <div style="flex: 1;">
              <Tabs v-model="activeTab">
                <TabPane
                  id="day"
                  label="每日"
                />
                <TabPane
                  id="total"
                  label="累计"
                />
              </Tabs>
            </div>
            <Button
              type="primary"
              size="small"
              style="margin-bottom: 10px;"
              @click="exportDataModal"
            >导出数据</Button>
          </div>
          <div class="main-content">
            <Table
              :columns="tableColumns"
              :data-source="tableDataForType"
              :pagination="false"
              bordered
            />
          </div>
        </div>
      </div>
    </Panel>
    <Modal
      v-model:visible="exportModalVisible"
      title="导出策略触发数据"
      :loading="exportPolling"
      @cancel="exportModalVisible = false"
      @confirm="exportData"
    >
      <Form
        ref="exportDataFormRef"
        label-position="top"
        :model="exportDataForm"
        :rules="exportDataFormRules"
        :style="{ width: '100%' }"
      >
        <FormItem
          required
          label="导出时间范围"
          name="time"
        >
          <DateRangePicker
            v-model="exportDataForm.time"
            clearable
            unit="second"
            style="width: 400px;"
          />
        </FormItem>
        <FormItem
          required
          label="数据维度"
          name="dataDimension"
        >
          <Select
            v-model="exportDataForm.dataDimension"
            :options="[
              { value: 'day', label: '每日' },
              { value: 'total', label: '累计' },
            ]"
          />
        </FormItem>
      </Form>
    </Modal>
  </Spinner>
</template>

<script setup lang="ts">
  import {
    Button,
    DateRangePicker, Form2 as Form, FormItem2 as FormItem,
    Input,
    Modal,
    Select,
    Space,
    Spinner,
    TabPane, Table,
    Tabs,
    Text,
    toast,
  } from '@xhs/delight'
  import download from 'loki-shared/utils/download'
  import {
    computed,
    onMounted,
    ref,
    watch,
  } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import Panel from '~/components/Panel/Index.vue'
  import { triggerDataEdit, triggerDataQuery } from '~/services/intelligentMarketing'
  import { stringToTimestamp, timestampToString } from '~/utils/time'
  import { useTriggerDataExport } from '../hooks/useTriggerDataExport'

  interface FormData {
    estimateTriggerStartNum: number | null
    estimateTriggerEndNum: number | null
  }

  const route = useRoute()
  const router = useRouter()
  const loading = ref(false)
  const submitLoading = ref(false)
  const backText = ref('< 返回')
  const strategyId = ref(route.params.strategyId as string)
  const todayTriggerNum = ref(0)
  const activeTab = ref('day')
  const exportModalVisible = ref(false)
  const exportDataFormRef = ref()

  const formValue = ref<FormData>({
    estimateTriggerStartNum: null,
    estimateTriggerEndNum: null,
  })
  const exportDataForm = ref({
    time: {
      start: null,
      end: null,
    },
    dataDimension: 'day',
  })

  const tableData = ref({
    day: [],
    total: [],
  })

  const dayDates = ref([])
  const totalDates = ref([])
  const tableDataForType = computed(() => (activeTab.value === 'day' ? tableData.value.day : tableData.value.total))

  const exportDataFormRules = {
    time: [{ required: true, message: '请选择导出时间范围' }],
    dataDimension: [{ required: true, message: '请选择数据维度' }],
  }

  const formattedTime = ref(0)

  // 任务成功回调函数
  const handleExportSuccess = (result: any) => {
    // 下载文件
    if (result?.fileUrl) {
      download(result.fileUrl, '策略触发数据.xlsx')
    }
    // 关闭弹窗
    exportModalVisible.value = false
    // 显示成功提示
    toast.success('数据导出成功')
  }

  const {
    exportPolling,
    handleLongTask,
  } = useTriggerDataExport({
    modalVisible: exportModalVisible,
    onSuccess: handleExportSuccess,
  })

  const handleBack = () => {
    router.back()
  }

  // 表格的列定义
  const tableColumns = ref([
    {
      title: '策略触发过滤节点/日期',
      dataIndex: 'filterNode',
      fixed: 'left' as const,
      width: 180,
    },
  // 动态生成日期列，根据后端返回的日期数据
  ])

  // 更新表格列定义
  const updateTableColumns = (dates: string[]) => {
    // 保留第一列
    tableColumns.value = [tableColumns.value[0]]

    // 根据后端返回的日期动态添加列
    dates.forEach(date => {
      tableColumns.value.push({
        title: date,
        children: [
          {
            title: '人数',
            dataIndex: `${date}Count`,
            width: 100,
            align: 'center' as const,
          },
          {
            title: '过滤比',
            dataIndex: `${date}Rate`,
            width: 100,
            align: 'center' as const,
          },
        ],
      })
    })
  }

  const handleTableData = (data: any) => {
    // 将数据按照statType分组
    const dayData = data.filter((item: any) => item.statType === 'day')
    const totalData = data.filter((item: any) => item.statType === 'total')

    // 获取所有日期
    dayDates.value = dayData.map(item => item.date)
    totalDates.value = totalData.map(item => item.date)

    // 处理表格数据的通用函数
    const processData = (sourceData: any[]) => {
      if (!sourceData.length) {
        return []
      }

      return [
        {
          filterNode: '实际触发人数',
          ...sourceData.reduce((acc: any, curr: any) => {
            acc[`${curr.date}Count`] = curr.triggerCount || 0
            acc[`${curr.date}Rate`] = '-'
            return acc
          }, {}),
        },
        {
          filterNode: '入口人数',
          ...sourceData.reduce((acc: any, curr: any) => {
            acc[`${curr.date}Count`] = curr.entryCount || 0
            acc[`${curr.date}Rate`] = '-'
            return acc
          }, {}),
        },
        {
          filterNode: '实验命中人数',
          ...sourceData.reduce((acc: any, curr: any) => {
            acc[`${curr.date}Count`] = curr.expCount || 0
            // 使用后端返回的过滤比
            acc[`${curr.date}Rate`] = curr.expFilterRatio || '0.00%'
            return acc
          }, {}),
        },
        {
          filterNode: '人群包命中人数',
          ...sourceData.reduce((acc: any, curr: any) => {
            acc[`${curr.date}Count`] = curr.crowdCount || 0
            // 使用后端返回的过滤比
            acc[`${curr.date}Rate`] = curr.crowdFilterRatio || '0.00%'
            return acc
          }, {}),
        },
        {
          filterNode: '频控符合人数',
          ...sourceData.reduce((acc: any, curr: any) => {
            acc[`${curr.date}Count`] = curr.frequencyCount || 0
            // 使用后端返回的过滤比
            acc[`${curr.date}Rate`] = curr.frequencyFilterRatio || '0.00%'
            return acc
          }, {}),
        },
        {
          filterNode: '条件符合人数',
          ...sourceData.reduce((acc: any, curr: any) => {
            acc[`${curr.date}Count`] = curr.conditionCount || 0
            // 使用后端返回的过滤比
            acc[`${curr.date}Rate`] = curr.conditionFilterRatio || '0.00%'
            return acc
          }, {}),
        },
      ]
    }

    // 根据 activeTab 返回对应的数据
    tableData.value = {
      day: activeTab.value === 'day' ? processData(dayData) : processData(totalData),
      total: activeTab.value === 'day' ? processData(totalData) : processData(dayData),
    }

    // 更新表格列
    updateTableColumns(activeTab.value === 'day' ? dayDates.value : totalDates.value)
  }

  const fetchStrategyData = async () => {
    try {
      loading.value = true
      // 这里添加获取策略数据的API调用
      triggerDataQuery({
        strategyId: strategyId.value,
      }).then((res: any) => {
        formattedTime.value = res.startTime
        formValue.value.estimateTriggerStartNum = res.estimateTriggerStartNum
        formValue.value.estimateTriggerEndNum = res.estimateTriggerEndNum
        todayTriggerNum.value = res.todayTriggerNum
        handleTableData(res.statisticsList)
        loading.value = false
      })
    } catch (error) {
      toast.danger('获取策略数据失败')
      loading.value = false
    } finally {
      loading.value = false
    }
  }

  const confirm = async () => {
    if (!formValue.value.estimateTriggerStartNum || !formValue.value.estimateTriggerEndNum) {
      toast.warning('请输入完整的预估触发人数范围')
      return
    }

    const startNum = parseInt(formValue.value.estimateTriggerStartNum, 10)
    const endNum = parseInt(formValue.value.estimateTriggerEndNum, 10)

    if (startNum >= endNum) {
      toast.warning('结束人数必须大于起始人数')
      return
    }

    try {
      submitLoading.value = true
      triggerDataEdit({
        strategyId: strategyId.value,
        estimateTriggerStartNum: startNum,
        estimateTriggerEndNum: endNum,
      }).then(() => {
        toast.success('提交成功')
        submitLoading.value = false
        fetchStrategyData()
      }).catch((err: any) => {
        toast.danger(err.message)
        submitLoading.value = false
      })
    } catch (error) {
      toast.danger('提交失败')
      submitLoading.value = false
    }
  }

  const exportData = () => {
    exportDataFormRef.value.validate().then(() => {
      const extra = {
        strategyId: strategyId.value,
        startDate: stringToTimestamp(exportDataForm.value.time.start),
        endDate: stringToTimestamp(exportDataForm.value.time.end),
        dataDimension: exportDataForm.value.dataDimension,
      }
      handleLongTask(extra)
    })
  }

  const exportDataModal = () => {
    exportModalVisible.value = true
  }

  watch(activeTab, () => {
    updateTableColumns(activeTab.value === 'day' ? dayDates.value : totalDates.value)
  }, { immediate: true })

  onMounted(() => {
    fetchStrategyData()
  })
</script>

<style scoped lang="stylus">
.back-link {
  margin-bottom 20px
}

.strategy-panel {
  flex-direction column
  gap 10px
  padding 20px 40px 40px 40px
}

.strategy-info {
  width 100%
  display flex
  flex-direction row
  flex-wrap wrap
  align-items center
  justify-content space-between
}

.input-range {
  display flex
  align-items center
  gap 10px
}

.number-input {
  width 100px
}

.number-card {
  width 100%
  display flex
  flex-direction row
  flex-wrap wrap
  align-items center
  justify-content space-around

  .item {
    display flex
    flex-direction column
    align-items center
    justify-content center
    gap 10px
  }
}

.weekly-data {
  width 100%
  min-height 100px
  display flex
  justify-content center
  align-items center
}

.weekly-stats {
  width 100%
  display flex
  flex-direction column

  .main-content {
    margin-top 15px
    width 100%
    overflow-x auto
  }
}

.header-container {
  display flex
  flex-direction row
  justify-content space-between
}
</style>
