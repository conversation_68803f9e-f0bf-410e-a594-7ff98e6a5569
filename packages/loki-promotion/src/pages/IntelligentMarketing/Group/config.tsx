import {
  h, ref,
} from 'vue'
import { ThContent } from '@xhs/delight/components/Table/interface'
import {
  Space, Text, Tag, Modal, toast,
} from '@xhs/delight'
import _ from 'lodash'
import { useRouter } from 'vue-router'
import { getFrontState } from '~/pages/IntelligentMarketing/utils'
import {
  STRATEGY_STATE_COLOR_MAP, STRATEGY_STATE_MAP,
} from '~/pages/IntelligentMarketing/const'
import { updateStrategyStatus } from '~/services/intelligentMarketing'
import { ExperimentNode } from '~/types/intelligentMarketing'

export const statusOptions = ref([
  { label: '进行中', value: '1' },
  { label: '已下线', value: '2' },
  { label: '已结束', value: '3' },
  { label: '未开始', value: '4' },
])

const offLineStrategy = (id, name, getData) => {
  Modal.warning({
    title: '确认下线该策略吗?',
    centered: true,
    content: h(Text, {}, {
      default: () => `请确认是否要失效策略: ${name}(ID: ${id})!`,
    }),
    async onConfirm(close) {
      // 更新状态
      const res = await updateStrategyStatus({
        strategyId: id,
      }, {
        status: 'OFFLINE',
      })
      if (res.success) {
        toast.success({ description: '更新成功', duration: 1500 })
        getData()
        close?.()
      } else {
        toast.danger({ description: res.message, duration: 1500 })
      }
    },
  })
}

// 删除策略
const deleteStrategy = (id, name, getData) => {
  Modal.warning({
    title: '确认删除该策略吗?',
    centered: true,
    content: h(Text, {}, {
      default: () => `请确认是否要删除策略: ${name}(ID: ${id})!`,
    }),
    async onConfirm(close) {
      // 更新状态
      const res = await updateStrategyStatus({
        strategyId: id,
      }, {
        status: 'DELETED',
      })
      if (res.success) {
        toast.success({ description: '更新成功', duration: 1500 })
        getData(true)
        close?.()
      } else {
        toast.danger({ description: res.msg, duration: 1500 })
      }
    },
  })
}

// 编辑  修改 查看 策略
const handleStrategy = (data: any, editOrCopyTactics, type) => {
  editOrCopyTactics(data, type)
}

const experimentConfigs = experimentConfigs => (experimentConfigs?.length
  ? <Space direction="vertical" align="start" size="2px">
    {
      experimentConfigs.map(item => (
        <Tag size="small" style={{ cursor: 'pointer' }}>
          <Text>{item.flag}</Text>
          <Text>=</Text>
          <Text style={{ fontWeight: 'bold' }}>{item.value}</Text>
        </Tag>
      ))
    }
  </Space> : '')

// 试验参数处理
const generateExpression = (config: ExperimentNode): string => {
  const build = (
    node: ExperimentNode,
    isRoot: boolean = true,
    parentConnector?: string,
  ): string => {
    if (node.subExperimentConfigV2List.length === 0) {
      return (!node.value && !node.flag) ? '' : `${node.flag || ''} = ${node.value || ''}`
    }

    const childrenExprs = node.subExperimentConfigV2List.map(child => build(child, false, node.subExperimentRelation))

    const connector = node.subExperimentRelation === 'AND' ? ' 且 ' : ' 或 '

    if (isRoot) {
      const rootConnector = `\n${connector.trim()}\n`
      return childrenExprs.join(rootConnector)
    }

    const expr = childrenExprs.join(connector)
    const needsWrap = childrenExprs.length > 1
      || (parentConnector && parentConnector !== node.subExperimentRelation)

    return needsWrap ? `(${expr})` : expr
  }

  return build(config)
}

function groupByExperimentConfigs(data) {
  const map = new Map()
  for (const item of data) {
    let found = false
    for (const [key, value] of map.entries()) {
      if (_.isEqual(JSON.parse(key), item.experimentConfigs || null)) {
        value.push(item.name)
        found = true
        break
      }
    }
    if (!found) {
      map.set(JSON.stringify(item.experimentConfigs || null), [item.name])
    }
  }
  // 如果你需要返回一个对象，你可以使用Object.fromEntries函数
  return Object.fromEntries(map)
}

const handlePrizeRule = array => {
  const list = groupByExperimentConfigs(array)
  return (
    <Space direction="vertical" align="start">
      {
        Object.keys(list).map(key => {
          const leftRule = JSON.parse(key) || []
          return (
            <Space direction="vertical" style={{ background: 'rgba(136, 136, 136, 0.08)', padding: '5px', borderRadius: '5px' }}>
              {
                experimentConfigs(leftRule)
              }
              {
                <Space direction="vertical" align="start">
                  {
                    list[key].map(item => (
                      <Text style="text-wrap: nowrap">{item}</Text>
                    ))
                  }
                </Space>
              }
            </Space>
          )
        })
      }
    </Space>
  )
}

export function useStrategyTable({
  getData, editOrCopyTactics, showPreviewStrategyModal, showTriggerDataModal,
}) {
  const router = useRouter()

  const jumpToDebug = rowData => {
    router.push({
      name: 'StrategyDebug',
      query: {
        strategyId: rowData.id,
      },
    })
  }
  const tableColumns = ref<ThContent[]>([
    {
      title: '策略id',
      dataIndex: 'id',
      minWidth: 60,
    },
    {
      title: '策略名称',
      dataIndex: 'name',
      minWidth: 175,
      fixed: 'left',
    },
    {
      title: '触点名称',
      dataIndex: 'triggerPointId',
      minWidth: 100,
      render: ({ rowData: { triggerPoint } }) => (
        <Text>{ triggerPoint?.name }</Text>
      ),
    },
    {
      title: '场景',
      dataIndex: 'bizsceneNameList',
      minWidth: 110,
      render: ({ rowData: { triggerPoint } }) => (
        <Space direction="vertical" align="start">
          {
            [...new Set(triggerPoint.bizsceneNameList)]?.map(scene => (
              <Text>{scene}</Text>
            ))
          }
        </Space>

      ),
    },
    {
      title: '触发规则',
      dataIndex: 'sceneID',
      minWidth: 200,
      render: ({ rowData: { triggerPoint } }) => {
        const triggerRuleList = (triggerPoint?.triggerRuleList || []).map(item => ({
          ...item,
          name: item.name,
          experimentConfigs: JSON.parse(JSON.stringify(item.experiments || [])),
        }))
        return (
          <Space direction="vertical" align="start" size="20px">
            {
              triggerRuleList?.length ? handlePrizeRule(triggerRuleList) : ''
              // triggerRuleList.map(rule => (
              //   <Space style={{ background: 'rgba(136, 136, 136, 0.08)', padding: '5px', borderRadius: '5px' }}>
              //     {experimentConfigs(rule.experiments)}
              //     <Text>{ rule.name }</Text>
              //   </Space>
              // ))
            }
          </Space>
        )
      },
    },
    {
      title: '容器',
      dataIndex: 'actionConfig',
      minWidth: 200,
      render: ({ rowData: { triggerPoint } }) => {
        const containers = (triggerPoint?.containerRule?.containerList || []).map(item => ({
          ...item,
          name: item?.containerName,
        }))
        return (
          <Space direction="vertical" align="start" size="20px">
            {
              containers?.length ? handlePrizeRule(containers) : ''
            }
          </Space>
          // <Space direction="vertical" align="start" size="20px">
          //   {
          //     containers.map(container => (
          //       <Space style={{ background: 'rgba(136, 136, 136, 0.08)', padding: '5px', borderRadius: '5px' }}>
          //         {experimentConfigs(container.experimentConfigs)}
          //         <Text>{container.containerName}</Text>
          //       </Space>
          //
          //     ))
          //   }
          // </Space>
        )
      },
    },
    {
      title: '实验平台参数',
      dataIndex: 'experimentConfigV2',
      minWidth: 200,
      render: ({ rowData: { experimentConfigV2 } }) => {
        // 生成表达式并处理省略号
        const expression = generateExpression(experimentConfigV2)
        const lines = expression.split('\n')
        const maxLength = 10
        const isOverflow = lines.length > maxLength

        // 截断并添加省略号
        const displayedLines = isOverflow
          ? [...lines.slice(0, maxLength), '...']
          : lines
        return (
          <div style={{
            lineHeight: '20px', fontSize: '14px', color: 'rgba(0, 0, 0, 0.65)', whiteSpace: 'nowrap', background: 'rgba(136, 136, 136, 0.08)', borderRadius: '5px', padding: '5px',
          }}>
            {displayedLines.map((line, index) => (
              <div key={index} class="expression-line">
                {line}
              </div>
            ))}
          </div>
        )
      },
    },
    {
      title: '权益池',
      dataIndex: 'prizeRuleNameTrue',
      minWidth: 70,
      render: ({ rowData: { actionConfig } }) => (
        <Text>{actionConfig.prizeRuleName}</Text>
      ),
    },
    {
      title: '权益组',
      dataIndex: 'prizeRuleName',
      minWidth: 300,
      render: ({ rowData: { actionConfig } }) => (
        <Space direction="vertical" align="start">
          {
            actionConfig?.benefitGroupConfigs?.length && handlePrizeRule(actionConfig?.benefitGroupConfigs)
          }
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      minWidth: 60,
      render: ({ rowData }) => (
        // @ts-ignore
        rowData.status === 'PAUSE' ? (
          <Tag color="red" size="small">
            暂停
          </Tag>
        ) : (
          <Tag color={STRATEGY_STATE_COLOR_MAP[getFrontState(rowData)]} size="small">
            {STRATEGY_STATE_MAP[getFrontState(rowData)]}
          </Tag>
        )
      ),
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      minWidth: 175,
      render: ({ rowData }) => (
        <Space direction="horizontal" wrap={true}>
          {
            [0, 1].includes(getFrontState(rowData))
              ? <Text link onClick={() => handleStrategy(rowData, editOrCopyTactics, 'edit')}>编辑</Text> : ''
          }
          {
            [0, 1].includes(getFrontState(rowData))
              ? <Text link onClick={() => offLineStrategy(rowData.id, rowData.name, getData)}>下线</Text> : '' // 只有待开始 & 进行中显示下线按钮
          }
          <Text link onClick={() => handleStrategy(rowData, editOrCopyTactics, 'copy')}>复制</Text>
          <Text link onClick={() => handleStrategy(rowData, editOrCopyTactics, 'view')}>查看</Text>
          {
            [2, 3].includes(getFrontState(rowData))
              ? <Text link onClick={() => deleteStrategy(rowData.id, rowData.name, getData)}>删除</Text>
              : '' // 已下线展示删除按钮
          }
          {rowData?.strategyExtraInfo && (
            <Space>
              {[
                {
                  label: '监控',
                  link: rowData.strategyExtraInfo.monitorLink,
                },
              ].filter(item => item.link).map((item, index) => (
                <Text
                  key={`link-${index}`}
                  link
                  onClick={() => window.open(item.link)}
                >
                  {item.label}
                </Text>
              ))}
            </Space>
          )}
          {
            [0, 1].includes(getFrontState(rowData))
              ? <Text link onClick={() => showPreviewStrategyModal(rowData.id, rowData.actionConfig.prizeRuleId)}>预览</Text> : ''
          }
          {
            [0, 1].includes(getFrontState(rowData))
              ? <Text link onClick={() => showTriggerDataModal(rowData)}>触发数据</Text> : ''
          }
          {
            <Text link onClick={() => jumpToDebug(rowData)}>调试</Text>
          }
        </Space>
      ),
    },
  ])
  return { tableColumns }
}
