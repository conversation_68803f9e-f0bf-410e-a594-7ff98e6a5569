<template>
  <Modal
    v-model:visible="visible"
    title="策略预览"
    size="900px"
    :destroy-on-close="true"
  >
    <Form
      ref="formRef"
      :model="formValue"
      :disabled="disabled"
    >
      <FormItem
        label="预估日均触发人数"
        name="name"
        :rules="[{ required: true, message: '请输入活动名称！' }]"
      >
        <Space>
          <Input
            v-model="formValue.estimateTriggerStartNum"
            placeholder="请输入"
            type="number"
          />
          <Span>到</Span>
          <Input
            v-model="formValue.estimateTriggerEndNum"
            placeholder="请输入"
            type="number"
          />
        </Space>
      </FormItem>
    </Form>
    <template #footer>
      <Space
        block
        justify="center"
      >
        <Button
          size="small"
          @click="handleClose"
        >
          取消
        </Button>
        <Button
          v-if="disabled"
          size="small"
          @click="disabled = !disabled"
        >
          修改
        </Button>
        <Button
          type="primary"
          size="small"
          @click="handleConfirm"
        >
          确定
        </Button>
      </Space>
    </template>
  </Modal>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import {
    Modal, Form2 as Form, FormItem2 as FormItem, Input, Space, Button, toast,
  } from '@xhs/delight'
  import { triggerDataEdit, triggerDataQuery } from '~/services/intelligentMarketing'

  const formRef = ref()
  const strategyId = ref(null)
  const disabled = ref(false)
  const formValue = ref({
    estimateTriggerStartNum: null,
    estimateTriggerEndNum: null,
  })

  interface IParamsType {
    id: number
    userId: string
    estimateTriggerStartNum: number
    estimateTriggerEndNum: number
  }

  const emit = defineEmits(['finish'])

  // 通用
  const visible = ref(false)

  const handleClose = () => {
    visible.value = false
  }

  const getTriggerDataQuery = () => {
    try {
      triggerDataQuery({
        strategyId: strategyId.value,
      }).then((res: any) => {
        console.log('res', res)
        formValue.value.estimateTriggerStartNum = res?.estimateTriggerStartNum || null
        formValue.value.estimateTriggerEndNum = res?.estimateTriggerEndNum || null
        disabled.value = !!(res.estimateTriggerEndNum && res.estimateTriggerStartNum)
      })
    } catch (err: any) {
      toast.danger(err.message)
    }
  }

  const showModal = (params: IParamsType) => {
    strategyId.value = params.id
    formValue.value = params
    getTriggerDataQuery()
    visible.value = true
  }

  const handleConfirm = () => {
    if (!formValue.value.estimateTriggerStartNum || !formValue.value.estimateTriggerEndNum) {
      toast.warning('请输入完整的预估触发人数范围')
      return
    }

    if (formValue.value.estimateTriggerStartNum >= formValue.value.estimateTriggerEndNum) {
      toast.warning('结束人数必须大于起始人数')
      return
    }
    try {
      triggerDataEdit({
        strategyId: strategyId.value,
        estimateTriggerStartNum: Number(formValue.value.estimateTriggerStartNum),
        estimateTriggerEndNum: Number(formValue.value.estimateTriggerEndNum),
      }).then(() => {
        emit('finish')
        handleClose()
      }).catch((err: any) => {
        toast.danger(err.message)
      })
    } catch (err: any) {
      // toast.danger(err.message)
    }
  }

  defineExpose({
    showModal,
  })

</script>
