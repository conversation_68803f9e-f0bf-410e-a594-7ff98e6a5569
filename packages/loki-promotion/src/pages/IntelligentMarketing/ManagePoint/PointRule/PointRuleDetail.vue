<template>
  <Spinner
    :spinning="sceneOptions === null"
    tip="加载中"
    size="large"
  >
    <Text
      v-if="!isSelect"
      link
      style="margin-bottom: 20px;"
      @click="()=>{$router.back()}"
    >{{ backText }}</Text>
    <Text
      type="h4"
      bold
    >基本信息</Text><br>
    <DelightForm
      v-if="(type === 'create' || (type !== 'create' && hasInit)) && totalSceneOptions.length"
      ref="formRef"
      name="layout"
      :config="config"
      :components="{
        DelightForm,
        FormLayout,
        FormItem,
        Input,
        Select,
        DatePicker,
        Radio,
        TestParamsInput,
        Checkbox,
        FrequencyInput,
        RelationTree,
        Switch,
        MultiLevelCheckbox,
      }"
      :scope="{
        disabled,
        updateFieldVisible,
        updateSceneOptions,
        updateTriggerOptions,
        updateConditionConfig,
        sceneOptions,
        formType: type,
      }"
      :registry-rules="{
        validateTestParams,
      }"
    />
    <div
      v-if="!disabled"
      class="footer"
    >
      <Button
        class="btn"
        type="default"
        @click="()=>{$router.back()}"
      >
        取消
      </Button>
      <Button
        class="btn"
        type="primary"
        @click="create"
      >
        {{ buttonText }}
      </Button>
    </div>
    <template v-if="type !=='create'">
      <Text
        type="h6"
        bold
      >关联的触点</Text><br>
      <Point :trigger-rule-id="triggerRuleId" />
      <Text
        type="h6"
        bold
      >关联的投放计划</Text><br>
      <LaunchPlan :trigger-rule-id="triggerRuleId" />
    </template>
  </Spinner>
</template>
  <script setup lang="tsx">
  import {
    Button,
    Modal,
    Spinner,
    Text,
    toast,
  } from '@xhs/delight'
  import {
    Checkbox,
    DatePicker,
    DelightForm,
    Input,
    Layout,
    Radio,
    Select,
    Switch,
  } from '@xhs/delight-formily'
  import {
    computed, h,
    onMounted,
    ref,
  } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import MultiLevelCheckbox from '~/components/MultiLevelCheckbox'
  import { useBroadcastChannel } from '~/hooks/useCrossPageCommunication'
  import FrequencyInput from '~/pages/IntelligentMarketing/Group/Nodes/Frequency/FrequencyInput.vue'
  import {
    createTriggerRule,
    getTriggerRule,
    updateTriggerRule,
  } from '~/services/trigger'
  import {
    getBizSceneListV2,
    getTriggerInfo,
  } from '../../../../services/intelligentMarketing'
  import TestParamsInput from '../../Group/Nodes/Experiment/TestParamsInput.vue'
  import LaunchPlan from '../../LaunchPlan/Index.vue'
  import { isExperimentValid, validateTestParams } from '../../utils/experiment'
  import RelationTree from '../components/RelationTree.vue'
  import Point from '../Point/Point.vue'
  import { config } from './config'

  const FormLayout = Layout.FormLayout
  const FormItem = Layout.FormItem

  const backText = '< 返回'
  const route = useRoute()
  const router = useRouter()
  const isSelect = route.query.isSelect
  const type = route.query.type
  const disabled = type === 'view'
  const triggerRuleId = route.query?.id
  const bizsceneList = ref({})
  const originName = ref('')
  const hasInit = ref(false)
  const isCreating = ref(false)
  const sceneOptions = computed(() => {
    const { bizscenes } = bizsceneList.value
    if (bizscenes) {
      return bizscenes.map(item => ({
        name: item.name,
        value: item.id,
        ...item,
      }))
    }
    return []
  })

  const totalSceneOptions = ref([])

  // 移除全局的 newDiVersion，改为在 bizsceneConfig 中管理

  const formRef = ref()

  // 根据 bizsceneId 和模式设置 newDiVersion 的值
  const setNewDiVersion = (bizsceneId: number, isCreateMode: boolean, backendNewDiVersion?: boolean) => {
    // 新建模式逻辑
    if (isCreateMode) {
      return !!((bizsceneId === 24 || bizsceneId === 23))
    }

    if (bizsceneId !== 24 && bizsceneId !== 23) {
      return true
    }

    // 编辑模式逻辑
    if (backendNewDiVersion === true && (bizsceneId === 24 || bizsceneId === 23)) {
      return true
    }
    if (backendNewDiVersion === false) {
      return false
    }
    // 默认情况
    return false
  }

  const { checkAndSendInstanceNode } = useBroadcastChannel({
    name: 'SOP-channel',
  })

  const buttonText = computed(() => {
    if (type === 'copy') {
      return '确认复制'
    } if (type === 'edit') {
      return '保存编辑'
    } if (type === 'create') {
      return '保存创建'
    }
    return ''
  })

  // 更新可选择的场景
  const updateSceneOptions = async (field: any, form: any) => {
    if (!form.values?.frontierType) return
    const res = await getBizSceneListV2(form.values?.frontierType)
    bizsceneList.value = res
    field.componentProps.options = res?.bizscenes.map(item => ({
      name: item.name,
      value: item.id,
      status: item.status || '',
    })) || []
    // 清空原来的场景选择
    if (!field.componentProps.options.map(item => item.value).includes(field.value)) {
      field.value = undefined
    }
  }

  // newDiVersion 逻辑现在直接在 config.ts 的 bizsceneId 字段的 x-reactions 中处理

  // 更新可选择的触发点
  const updateTriggerOptions = async (field: any, form: any) => {
    if (!form.values?.bizsceneId) return

    const res = await getTriggerInfo({
      bizsceneId: form.values.bizsceneId,
      bizsceneConfig: form.values?.bizsceneConfig || {},
    })
    field.componentProps.options = res?.triggers.map(item => ({
      name: item.name,
      value: item.id,
      status: item.status || '',
    })) || []
    // field.componentProps.options 剔除掉 status 为 OFFLINE 但保留 form.values?.triggerId， 如果是该项status 是 OFFLINE 中 添加属性 disabled
    // 移除 status 为 OFFLINE 的选项，但保留与 triggerId 相同的选项
    field.componentProps.options = field.componentProps.options.filter(option => option.status !== 'OFFLINE' || option.value === form.values?.triggerId).map(option => {
      // 如果是 triggerId 对应的选项，并且 `status` 是 OFFLINE，则加上 `disabled: true`
      if (option.value === form.values?.triggerId && option.status === 'OFFLINE') {
        return {
          ...option,
          disabled: true, // 禁用当前项
        }
      }
      return option // 其他保持不变
    })

    // 清空原来的触发点选择
    if (!field.componentProps.options.map(item => item.value).includes(field.value)) {
      field.value = undefined
    }
  }

  const updateFieldVisible = (field, form) => {
    const { bizsceneId, bizsceneConfig } = form.values
    if (!totalSceneOptions.value || !totalSceneOptions.value.length) {
      field.setState(state => {
        state.display = 'none'
      })
    }
    const configItems = totalSceneOptions.value?.find(item => item.value === bizsceneId)?.configItems || []
    if (field.props.name === 'noteContentType') {
      field.setState(state => {
        state.display = configItems.includes('note_content_type') ? 'visible' : 'none'
      })
    } else if (field.props.name === 'noteSource') {
      field.setState(state => {
        state.display = configItems.includes('note_source') ? 'visible' : 'none'
      })
    } else if (field.props.name === 'videoNoteFeedSource') {
      const isVideo = bizsceneConfig && Array.isArray(bizsceneConfig.noteContentType) && bizsceneConfig.noteContentType.includes('VIDEO')
      field.setState(state => {
        state.display = configItems.includes('video_note_feed_source') && isVideo ? 'visible' : 'none'
      })
    } else if (field.props.name === 'detailSourceType') {
      // 原有逻辑：基于 configItems 判断
      const configItemsVisible = configItems.includes('detail_source_type')
      // 新增逻辑：版本控制，特定场景下根据 newDiVersion 决定是否显示旧版本字段
      const versionVisible = (bizsceneId === 24 || bizsceneId === 23)
        ? !bizsceneConfig?.newDiVersion : true

      field.setState(state => {
        state.display = (configItemsVisible && versionVisible) ? 'visible' : 'none'
      })
    }
  }

  const updateConditionConfig = (field, form) => {
    const { triggerId, bizsceneId, frontierType } = form.values
    let conditionConfig = form.values.conditionConfig
    // 判断triggerId bizsceneId有任何一个没有值的话，conditionConfig变成默认值
    if (!triggerId || !bizsceneId || !frontierType) {
      conditionConfig = {
        subConditionRelation: 'AND',
        subConditionConfigs: [],
      }
    }

    const setValues: any = {
      ...form.values,
      conditionConfig: {
        ...conditionConfig,
        triggerId,
        bizsceneId,
        frontierType,
      },
    }
    formRef.value?.form.setValues(setValues)
  }

  const getDetail = () => {
    const params = {
      triggerRuleId,
    }
    getTriggerRule(params).then(res => {
      if (res.triggerRule) {
        const setValues: any = {
          ...res.triggerRule,
          conditionConfig: {
            ...res.triggerRule.conditionConfig,
            triggerId: res.triggerRule.triggerId,
            bizsceneId: res.triggerRule.bizsceneId,
          },
        }

        // 编辑场景：根据新逻辑设置 newDiVersion
        const backendNewDiVersion = setValues?.bizsceneConfig?.newDiVersion || false
        const calculatedNewDiVersion = setNewDiVersion(
          setValues.bizsceneId,
          false, // 编辑模式
          backendNewDiVersion,
        )

        // 确保 bizsceneConfig 存在
        if (!setValues.bizsceneConfig) {
          setValues.bizsceneConfig = {}
        }
        setValues.bizsceneConfig.newDiVersion = calculatedNewDiVersion

        hasInit.value = true
        originName.value = res.triggerRule.name
        setTimeout(() => {
          formRef.value?.form.setValues(setValues)
        })
      }
    })
  }

  const checkConditionIsValid = configs => {
    if (!configs) {
      return true
    }
    for (let i = 0; i < configs.length; i++) {
      const item = configs[i]
      // 如果不是一个条件组的话
      if (!item.subConditionConfigs) {
        // 条件项 运算符 值 有一项为空就报错
        if (item.operator === '' || item.value === '' || item.conditionType === '') {
          return false
        }
      } else if (!checkConditionIsValid(item.subConditionConfigs)) {
        return false
      }
    }
    return true
  }

  const create = async () => {
    try {
      if (isCreating.value) {
        return
      }

      // 校验条件配置
      if (!checkConditionIsValid(formRef.value.form.values.conditionConfig?.subConditionConfigs)) {
        toast.danger({ description: '任何一项条件项不可为空', duration: 1500 })
        return
      }

      // 第一步：表单验证
      try {
        await formRef.value.form.validate()
      } catch (error) {
        toast.danger({
          description: '表单校验失败',
          closeable: true,
        })
        return
      }

      // 准备参数
      const formValues = formRef.value.form.values
      const params = {
        triggerRule: {
          ...formValues,
        },
      }
      if (type === 'copy' && params.triggerRule.name === originName.value) {
        const isContinue = await new Promise(resolve => {
          Modal.warning({
            title: '提示',
            content: h(Text, {}, {
              default: () => '名称与原触发规则名称相同, 是否确认提交？',
            }),
            onConfirm: close => {
              close?.()
              resolve(true)
            },
            onCancel: close => {
              close?.()
              resolve(false)
            },
          })
        })
        if (!isContinue) {
          return
        }
      }
      if (type !== 'create') {
        delete params.triggerPoint?.id
      }

      // 第二步：预检查实验配置
      const checkRes = await isExperimentValid({
        experimentConfigs: params.triggerRule.experiments || [],
      })
      if (!checkRes) {
        return
      }

      // 第三步：根据类型选择请求方法
      const requestMap = {
        create: createTriggerRule,
        edit: updateTriggerRule,
        copy: createTriggerRule,
      }

      const request = requestMap[type]
      if (!request) {
        throw new Error('未知的操作类型')
      }

      // 第四步：发起创建请求
      isCreating.value = true
      const res = await request(params)

      if (res.success) {
        toast.success({ description: '创建成功', closeable: true })
        checkAndSendInstanceNode([res?.data?.id])
        setTimeout(() => {
          router.push({
            name: 'ManagePoint',
          })
        }, 500)
      } else {
        throw new Error(res?.msg)
      }
    } catch (error) {
      toast.danger({
        description: error?.message || '操作失败',
        duration: 1500,
      })
    } finally {
      isCreating.value = false
    }
  }

  const initBizSceneList = async () => {
    const res = await getBizSceneListV2()
    const { bizscenes } = res
    totalSceneOptions.value = bizscenes.map(item => ({
      name: item.name,
      value: item.id,
      ...item,
    }))
    // bizsceneList.value = res
    if (type !== 'create') {
      getDetail()
    } else {
      // 新建场景处理：需要监听 bizsceneId 变化来设置 newDiVersion
      hasInit.value = true

      // 在新建模式下，初始化 bizsceneConfig.newDiVersion 为 false
      setTimeout(() => {
        if (formRef.value?.form) {
          const currentValues = formRef.value.form.values
          const newValues = {
            ...currentValues,
            bizsceneConfig: {
              ...currentValues.bizsceneConfig,
              newDiVersion: false,
            },
          }
          formRef.value.form.setValues(newValues)
        }
      }, 100)
    }
  }
  onMounted(() => {
    initBizSceneList()
  })

</script>

<style lang="stylus" scoped>
  .footer {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 20px;
    .btn {
      margin: 0 10px;
    }
  }
</style>
