<template>
  <Spinner
    :spinning="false"
    tip="加载中"
    size="large"
  >
    <Text
      link
      style="margin-bottom: 20px;"
      @click="()=>{$router.back()}"
    >&lt; 返回</Text>
    <Text
      type="h4"
      bold
      style="margin-bottom: 20px; display: block;"
    >创建白名单</Text>
    <DelightForm
      ref="formRef"
      :config="formConfig"
      :components="components"
      :scope="{}"
    />
    <div class="footer">
      <Button
        class="btn"
        type="default"
        @click="handleReset"
      >重置</Button>
      <Button
        class="btn"
        type="primary"
        @click="handleSave"
      >保存</Button>
    </div>
  </Spinner>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { useRouter } from 'vue-router'
  import {
    Button, toast, Text, Spinner,
  } from '@xhs/delight'
  import {
    Input,
    FormItem,
    FormLayout,
    DelightForm,
  } from '@xhs/delight-formily'
  import { addDebugWhitelist } from '~/services/intelligentMarketing'

  const router = useRouter()
  const formRef = ref()

  const components = {
    Input,
    FormItem,
    FormLayout,
  }

  const formConfig = {
    type: 'object',
    'x-component': 'FormLayout',
    properties: {
      uid: {
        type: 'number',
        'x-decorator': 'FormItem',
        'x-decorator-props': {
          label: 'UID',
          required: true,
          style: { maxWidth: '400px' },
        },
        'x-component': 'Input',
        'x-component-props': {
          placeholder: '请输入',
          style: { width: '320px' },
        },
        'x-validator': {
          required: true,
        },
      },
      name: {
        type: 'string',
        'x-decorator': 'FormItem',
        'x-decorator-props': {
          label: '名称',
          style: { maxWidth: '400px' },
        },
        'x-component': 'Input',
        'x-component-props': {
          placeholder: '请输入',
          style: { width: '320px' },
        },
      },
    },
  }

  function handleReset() {
    formRef.value?.form.reset()
  }

  function handleSave() {
    formRef.value?.form.validate().then(async () => {
      const values = formRef.value.form.values
      try {
        await addDebugWhitelist({ whiteUserName: values.name, whiteUserId: values.uid ? Number(values.uid) : undefined })
        toast.success({ description: '保存成功', duration: 1500 })
        setTimeout(() => {
          router.back()
        }, 500)
      } catch (e) {
        toast.danger({ description: e?.msg || '保存失败', duration: 1500 })
      }
    }).catch(() => {
      toast.danger({ description: '请填写必填项', duration: 1500 })
    })
  }
</script>

<style scoped lang="stylus">
.footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 32px;
  .btn {
    margin-left: 16px;
    min-width: 80px;
  }
}
</style>
