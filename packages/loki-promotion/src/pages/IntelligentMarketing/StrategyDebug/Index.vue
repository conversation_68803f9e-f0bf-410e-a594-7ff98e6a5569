<template>
  <Tabs v-model="tabValue">
    <TabPane label="策略Debug查询">
      <template #tab>
        策略Debug查询
        <Tooltip>
          <Icon :icon="Help" />
          <template #content>
            <div style="display: flex; flex-direction: column;">
              <Text style=" color: white;">使用步骤：</Text>
              <Text style=" color: white;">1. 增加白名单</Text>
              <Text style=" color: white;">2. 发起用户请求</Text>
              <Text style=" color: white;">3. debug查询</Text>
            </div>
          </template>
        </Tooltip>
      </template>
      <Space
        direction="vertical"
        block
        size="large"
        align="unset"
      >
        <!-- <Text
            type="h2"
            :blod="true"
            class="textWeight"
          >策略查询工具</Text> -->
        <header class="tool-bar">
          <div class="search-bar">
            <Input
              v-model="searchParams.userId"
              clearable
              placeholder="请输入用户id（必填）"
              style="width: 375px"
            />
            <Input
              v-model="searchParams.strategyId"
              clearable
              placeholder="请输入策略id"
            />
            <Select
              v-model="searchParams.bizScene"
              clearable
              :options="bizSceneOptions"
              placeholder="请选择场景"
            />
            <Button
              type="secondary"
              :loading="loading"
              @click="handleSearch"
            >查询
            </Button>
          </div>
          <div
            class="space"
            style="flex: 1"
          />
        </header>
        <div
          class="info"
          style="padding: 0 24px;"
        >
          <Text v-if="loading">正在查询中....</Text>
          <template v-else>
            <Text v-if="searchParamsHas">查询无效，请检查查询条件是否正确</Text>
            <div>
              <div
                v-if="strategyDebugList.length"
                class="flexRow first"
              >
                <Text
                  type="h3"
                  :blod="true"
                  class="textWeight"
                >24小时内，用户进入的场景有：</Text>
                <Tooltip content="本次查询仅展示24小时内用户进入的最近50个场景">
                  <Icon
                    :icon="Info"
                    size="large"
                    color="grey-8"
                  />
                </Tooltip>
              </div>
              <div
                v-for="item in strategyDebugList"
                :key="item.id"
                class="first"
              >
                <div class="title flexRow">
                  <div class="grayRound" />
                  <Text
                    type="h6"
                    :blod="true"
                    class="textWeight blueText"
                  >{{ timestampToSecondString(item.time) }}</Text>
                  <Text
                    type="h6"
                    :blod="true"
                    class="textWeight blueText"
                  >用户进入</Text>
                  <Text
                    type="h6"
                    :blod="true"
                    class="textWeight originText"
                  >
                    {{ `「${item.triggerName}」，` }}
                  </Text>
                  <Text
                    v-if="item.validStrategyList && item.validStrategyList.length"
                    type="h6"
                    :blod="true"
                    class="textWeight blueText"
                  >{{ `生效策略${item.validStrategyList.length}条，` }}
                  </Text>
                  <Text
                    v-if="item.inValidStrategyList && item.inValidStrategyList.length"
                    type="h6"
                    :blod="true"
                    class="textWeight blueText"
                  >
                    {{ `未生效策略${item.inValidStrategyList.length}条` }}
                  </Text>
                  <arrowIcon
                    :is-open="item.expend"
                    @click="changeExpend(item, 'expend')"
                  />
                </div>

                <div
                  v-if="item.expend"
                  class="second"
                >
                  <div style="margin-bottom: 10px">
                    <div class="flexRow">
                      <div class="hollowCircle" />
                      <Text
                        type="h6"
                        :blod="true"
                        class="textWeight"
                      >
                        {{ `生效策略有${item.validStrategyList.length}条` }}
                      </Text>
                      <arrowIcon
                        :is-open="item.validStrategyExpend"
                        @click="changeExpend(item, 'validStrategyExpend')"
                      />
                    </div>
                    <div
                      v-if="item.validStrategyExpend"
                      class="third"
                    >
                      <div
                        v-for="(validItem, index) in item.validStrategyList"
                        :key="validItem.id"
                        class="flexRow gap10"
                      >
                        <Text>{{ `${index + 1}、` }}</Text>
                        <Text>{{ `策略id：${validItem.id}` }}</Text>
                        <Text>{{ `策略名称：${validItem.name}` }}</Text>
                        <Text>{{ `投放计划id：${validItem.launchPlanId}` }}</Text>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div class="flexRow">
                      <div class="hollowCircle" />
                      <Text
                        type="h6"
                        :blod="true"
                        class="textWeight"
                      >
                        {{ `未生效策略有${item.inValidStrategyList.length}条` }}
                      </Text>
                      <arrowIcon
                        :is-open="item.invalidStrategyExpend"
                        @click="changeExpend(item, 'invalidStrategyExpend')"
                      />
                    </div>
                    <div
                      v-if="item.invalidStrategyExpend"
                      class="third"
                    >
                      <div
                        v-for="(validItem, index) in item.inValidStrategyList"
                        :key="validItem.id"
                      >
                        <div class="flexRow gap10">
                          <Text>{{ `${index + 1}、` }}</Text>
                          <Text>{{ `策略id：${validItem.id}` }}</Text>
                          <Text>{{ `策略名称：${validItem.name}` }}</Text>
                          <Text>{{ `投放计划id：${validItem.launchPlanId}` }}</Text>
                        </div>
                        <div style="margin-left: 30px">
                          <Text style="color: #CC3030">未生效原因：</Text>
                          <Text style="color: #CC3030">{{ validItem.invalidReason }}</Text>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div />
                </div>
              </div>
            </div>
          </template>
        </div>
      </Space>
    </TabPane>
    <TabPane label="白名单管理">
      <WhitelistManage />
    </TabPane>
  </Tabs>
</template>

<script setup lang="ts">
  import {
    ref, computed, onMounted, watch,
  } from 'vue'
  import {
    Tabs, TabPane, Space, Button, Input, Select, toast2 as toast, Text, Icon, Tooltip,
  } from '@xhs/delight'
  import { useRoute } from 'vue-router'
  import { Info, Help } from '@xhs/delight/icons'
  import arrowIcon from './arrowIcon.vue'
  import WhitelistManage from './WhitelistManage.vue'
  import { getBizSceneList, strategyDebugQuery } from '~/services/intelligentMarketing'
  import { timestampToSecondString } from '~/utils/time'

  // 策略基础类型
  interface Strategy {
    id: string
    name: string
    launchPlanId: string
    invalidReason: string
  // 可使用扩展字段 ...
  }

  // 触点数据主体
  interface TriggerData {
    id: string
    /** 时间戳 (毫秒) */
    time: number
    /** 触点名称 */
    triggerName: string
    /** 策略触发描述 */
    desc: string
    /** 生效策略列表 */
    validStrategyList: Strategy[]
    /** 未生效策略列表 */
    inValidStrategyList: Strategy[]
    /** 整体是否展开 */
    expend: boolean
    /** 已生效策略是否展开 */
    validStrategyExpend: boolean
    /** 未生效策略是否展开 */
    invalidStrategyExpend: boolean
  }

  // 完整响应结构
  type ResponseData = TriggerData[]

  const searchParams = ref({
    userId: '',
    strategyId: '',
    bizScene: '',
  })

  const route = useRoute()

  const isSearch = ref(false)

  const bizSceneOptions = ref([])

  const strategyDebugList = ref<ResponseData>([])

  const loading = ref(false)

  const searchParamsHas = computed(() => isSearch.value && !strategyDebugList.value.length && Object.values(searchParams.value).some(value => value !== null && value !== undefined && value !== ''))

  const initBizSceneList = async () => {
    try {
      const { bizscenes } = await getBizSceneList()
      bizSceneOptions.value = bizscenes.map(item => ({
        label: item.name,
        value: item.id,
      }))
    } catch (error) {
      console.error(error)
    }
  }

  // 抽离数据格式化逻辑 (可维护性提升)
  const createStrategyItem = (item: TriggerData) => ({
    ...item,
    isExpend: false, // 默认折叠状态
    validStrategyList: item.validStrategyList || [], // 防御性处理
    inValidStrategyList: item.inValidStrategyList || [],
    validStrategyExpend: false, // 生效策略默认折叠
    invalidStrategyExpend: true, // 失效策略默认展开
  })

  const loadData = async () => {
    // 前置校验强化：空值校验 + 去空格处理
    if (!searchParams.value.userId?.trim()) {
      toast.warning('请输入有效用户ID')
      return
    }

    try {
      loading.value = true
      isSearch.value = true
      const { infoList } = await strategyDebugQuery(searchParams.value)

      // 数据安全处理：应对空数据场景
      strategyDebugList.value = (infoList || []).map(createStrategyItem)
    } catch (error) {
      // 错误处理优化：用户提示 + 日志记录
      toast.warning(error.msg || error.message || '数据加载异常')
      console.error('[策略调试] 数据加载异常:', error)
    } finally {
      loading.value = false // 确保无论成功失败都会清除loading
    }
  }

  // 搜索
  const handleSearch = async () => {
    await loadData()
  }

  const changeExpend = (item, type) => {
    item[type] = !item[type]
  }

  onMounted(() => {
    initBizSceneList()
    const { strategyId } = route.query
    if (strategyId) {
      searchParams.value.strategyId = strategyId as string
    }
  })

  const tabValue = ref(sessionStorage.getItem('strategy_debug_tab') || '策略Debug查询')

  watch(() => tabValue.value, value => {
    sessionStorage.setItem('strategy_debug_tab', value)
  })
</script>

<style scoped lang="stylus">
.tool-bar {
  display: flex;
  padding: 24px;
  .search-bar {
    width: 70%;
    display: flex;
    gap: 14px;
  }
}

.flexRow {
  display flex
  flex-direction row
  flex-wrap wrap
  align-items center
}
.gap10 {
  gap 10px
}

.grayRound {
  width: 8px
  height: 8px
  border-radius: 50%
  background-color: #ccc
  margin-right 12px
}

.title {
  gap 6
}

.textWeight {
  font-weight 600
}

.first {
  margin-bottom 14px
}

.second {
  margin 10px 0 10px 14px
}

.blueText {
  color: #3357d9;
}

.originText {
  color: #F06A1D;
}
.hollowCircle {
  width: 8px
  height: 8px
  border-radius: 50%
  border: 1px solid #ccc
  margin-right 12px
}
.third {
  margin 10px 0 10px 22px
  display flex
  flex-direction column
  gap 10px
}
</style>
