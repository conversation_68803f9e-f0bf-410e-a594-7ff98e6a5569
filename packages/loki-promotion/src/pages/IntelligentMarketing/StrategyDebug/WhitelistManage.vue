<template>
  <Space
    direction="vertical"
    block
    size="large"
    align="unset"
  >
    <OutlineFilter
      v-model="filterParam"
      :config="filterConfigRef"
      style="overflow: auto"
    />
    <Space
      :style="{ background: '#FFFFFF', borderRadius: '8px', padding: '0 24px 24px' }"
      direction="vertical"
      size="small"
      align="unset"
    >
      <Toolbar
        style="margin-top: -24px"
        :config="toolBarConfig"
      />
      <Table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        row-key="uid"
      >
        <template #operation="{ rowData }">
          <Text
            link
            style="color: #2468f2"
            @click="handleDelete(rowData)"
          >删除</Text>
        </template>
      </Table>
    </Space>
  </Space>
</template>

<script setup lang="ts">
  import {
    markRaw, onMounted, ref,
  } from 'vue'
  import {
    Table, Text, Space, Modal, toast, Input,
  } from '@xhs/delight'
  import OutlineFilter from '@xhs/delight-material-ultra-outline-filter'
  import Toolbar from '@xhs/delight-material-ultra-toolbar'
  import type { TooltipConfig } from '@xhs/delight-material-ultra-toolbar'
  import { useRouter } from 'vue-router'
  import { queryDebugWhitelist, deleteDebugWhitelist } from '~/services/intelligentMarketing'

  const router = useRouter()

  // 白名单数据
  const dataSource = ref([])
  const loading = ref(false)

  // OutlineFilter 配置
  const filterParam = ref({
    whiteUserId: '',
  })

  const filterConfigRef = ref({
    filterItems: [
      {
        label: 'UID',
        name: 'whiteUserId',
        component: {
          is: markRaw(Input),
          props: {
            placeholder: '请输入UID',
            clearable: true,
          },
        },
      },
    ],
    handleFilter: fetchList,
  })
  const toolBarConfig: TooltipConfig = {
    actions: [
      {
        text: '新增白名单',
        props: {
          type: 'primary',
        },
        onClick: () => {
          router.push({ name: 'WhitelistDetail', query: { type: 'create' } })
        },
      },
    ],
  }

  const columns = [
    { title: '白名单UID', dataIndex: 'whiteUserId', minWidth: 200 },
    { title: '白名单名称', dataIndex: 'whiteUserName', minWidth: 200 },
    { title: '操作', dataIndex: 'operation', minWidth: 100 },
  ]

  async function fetchList() {
    try {
      loading.value = true
      const data = await queryDebugWhitelist({
        whiteUserId: filterParam.value.whiteUserId,
      })
      dataSource.value = data?.whiteList || []
    } catch (error) {
      toast.danger({ description: error?.msg || '查询失败', duration: 1500 })
    } finally {
      loading.value = false
    }
  }

  function handleDelete(row) {
    Modal.warning({
      title: '确认删除该白名单吗?',
      centered: true,
      content: `白名单UID: ${row.whiteUserId}`,
      async onConfirm(close) {
        loading.value = true
        try {
          await deleteDebugWhitelist({ whiteUserId: row.whiteUserId })
          toast.success({ description: '删除成功', duration: 1500 })
          fetchList()
        } catch (e) {
          toast.danger({ description: e?.msg || '删除失败', duration: 1500 })
        } finally {
          loading.value = false
          close?.()
        }
      },
    })
  }

  // // 监听筛选条件变化自动查询
  // watch(filterParam, fetchList, { deep: true })

  // 页面初始化自动加载
  onMounted(() => {
    fetchList()
  })
</script>

<style scoped lang="stylus">
.tableText {
  color: rgba(0,0,0,0.65);
  font-size: 14px;
}
</style>
