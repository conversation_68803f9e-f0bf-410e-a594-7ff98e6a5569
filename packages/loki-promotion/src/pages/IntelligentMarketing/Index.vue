<template>
  <div class="intelligent-marketing-page">
    <div class="tab-list">
      <div
        v-for="(item, index) in menuList"
        :key="index"
        class="tab-item"
        :class="[getLastSegment(route.path) === item.path ? 'selected-tab' : '']"
        @click="handleChangeTab(index)"
      >
        {{ item.name }}
      </div>
    </div>
    <div class="page-content">
      <router-view />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, ref } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { useStore } from 'vuex'
  import { getBizSceneList, getBizSceneListV2 } from '~/services/intelligentMarketing'
  import { getTriggerList } from '~/services/trigger'
  import { ISceneItem, ITriggerItem, IContainerItem } from '~/types/intelligentMarketing'

  const router = useRouter()
  const route = useRoute()
  const store = useStore()

  const menuList = ref([
    {
      name: '标准流程',
      to: 'StrategySOP',
      path: 'strategy-sop',
    },
    {
      name: '投放计划',
      to: 'LaunchPlan',
      path: 'launch-plan',
    },
    {
      name: '策略全局管理',
      to: 'StrategyGlobalManagement',
      path: 'strategy-global-management',
    },
    {
      name: '触点管理',
      to: 'ManagePoint',
      path: 'manage-point',
    },
    {
      name: '权益池',
      to: 'EquityPool',
      path: 'equity-pool',
    },
    {
      name: '场景列表-历史',
      to: 'Scenes',
      path: 'scenes',
    },
    {
      name: '场景列表',
      to: 'ScenesV2',
      path: 'scenesV2',
    },
    {
      name: '策略组',
      to: 'StrategyGroup',
      path: 'strategy-group',
    },
    {
      name: '优先级管理',
      to: 'PriorityManage',
      path: 'priority-manage',
    },
    {
      name: '全局频控管理',
      to: 'FrequencyManage',
      path: 'frequency-manage',
    },
    // {
    //   name: '算法配置',
    //   to: 'AlgorithmConfig',
    //   path: 'algorithm-config',
    // },
    {
      name: '条件规则配置',
      to: 'ConditionRuleConfig',
      path: 'condition-rule-config',
    },
    {
      name: '筛选规则配置',
      to: 'FilterRuleConfig',
      path: 'filter-rule-config',
    },
    {
      name: '资源管理',
      to: 'ResourceManagement',
      path: 'resource-management',
    },
    {
      name: '策略调试',
      to: 'StrategyDebug',
      path: 'strategy-debug',
    },
  ])

  const handleChangeTab = (index: number) => {
    router.push({ name: menuList.value[index].to })
  }

  function getLastSegment(url:string) {
    if (typeof url !== 'string' || url.trim() === '') {
      return null
    }

    // 正则表达式匹配最后一节地址，不包含结尾的斜杠
    const match = url.match(/\/([^/]+)\/?$/)
    if (match) {
      return match[1]
    }
    return null
  }

  onMounted(async () => {
    const res2: {
      bizscenes: ISceneItem[]
      triggers: ITriggerItem[]
      containerConfigs: IContainerItem
    } = await getBizSceneListV2()

    store.commit('intelligentMarketing/setBizSceneInfoV2', res2)

    const res: {
      bizscenes: ISceneItem[]
      triggers: ITriggerItem[]
      containerConfigs: IContainerItem
    } = await getBizSceneList()

    // 更新到store中
    store.commit('intelligentMarketing/setBizSceneInfo', res)

    const res1 = await getTriggerList({
      pageNum: 1,
      pageSize: 999,
    })
    store.commit('intelligentMarketing/setTriggerPointList', res1.triggerPointList.map(item => ({
      id: item.id,
      name: item.name,
    })) || [])
  })
</script>

<style lang="stylus" scoped>
  .intelligent-marketing-page {
    min-height: 480px;

    .tab-list {
      display: flex;
      color: rgba(0,0,0,0.65);
      gap: 20px;
      height: 30px;

      .tab-item {
        height: 30px;
        position: relative;
        cursor: pointer;
      }

      .selected-tab {
        color: #000000;
        font-weight: 500;

        &::after {
          content: '';
          position absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 100%;
          height: 2px;
          border-radius: 2px;
          background-color: var(--color-primary);
        }
      }
    }

    .page-content {
      min-height: 600px;
      background-color: #fff;
      border-radius: 8px;
      margin-top: 10px;
      padding: 20px;

      .v-enter-active,
      .v-leave-active {
        transition: all 0.5s ease;
      }

      .v-enter-from,
      .v-leave-to {
        opacity: 0;
        transform: translateX(12px);
      }
    }
  }
</style>
