<template>
  <div class="test-params-input">
    <div
      v-for="(item, index) in modelValue"
      :key="index"
      class="params-item"
    >
      <Space style="width: 100%; margin-bottom: 14px;">
        <span class="params-title">阶段{{ index + 1 }}：库存释放开始时间</span>
        <DateRangePicker
          v-model="item.date"
          style="width:280px"
          :placeholder="{start: '选择时间', end: '选择时间'}"
          :disabled="disabled || item.allowEdit === false"
        />
        <Select
          v-model="item.weeks"
          :options="options"
          multiple
          :max-tag-count="2"
          style="max-width: 200px"
          placeholder="选择星期"
          :disabled="disabled || item.allowEdit === false"
        >
          <template #stickTop>
            <Button @click.stop="selectAllDays(index)">一键全选</Button>
          </template>
        </Select>
        <TimePicker
          v-model="item.effectiveHourAndMinutes"
          style="width: 185px"
          select-only
          placeholder="选择时间(24小时制)"
          :disabled="disabled || item.allowEdit === false"
        />
        <Button
          :icon="ReduceOne"
          :disabled="disabled || item.allowEdit === false"
          @click="deleteItemByIndex(index)"
        />
      </Space>
      <Space
        v-if="isShowDurationHours"
        style="width: 100%; margin-bottom: 14px;"
      >
        <span
          class="params-title"
        >&emsp;&emsp;&emsp;&ensp;持续时长（小时）</span>
        <InputNumber
          v-model="item.durationHours"
          placeholder="填写持续时长"
          :precision="0"
          :min="1"
          inner-buttons
          style="width: 150px;"
          :disabled="disabled || item.allowEdit === false"
        />
      </Space>
      <Space>
        <span
          style="margin-right: 1px;"
          class="params-title"
        >&emsp;&emsp;&emsp;&ensp;库存量 &emsp; &emsp;&emsp;&emsp;&ensp;</span>
        <InputNumber
          v-model="item.stock"
          placeholder="填写库存量"
          :precision="0"
          inner-buttons
          style="width: 150px;"
          :disabled="disabled || item.allowEdit === false"
        />
      </Space>
    </div>
    <div class="operator">
      <Button
        v-show="modelValue.length < 10"
        type="secondary"
        :icon="AddOne"
        size="small"
        :disabled="disabled"
        @click="handleAddParams()"
      >添加</Button>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import {
    defineProps, defineEmits, watch, computed, reactive, toRefs, onMounted,
  } from 'vue'
  import {
    Button, DateRangePicker, Select, TimePicker, InputNumber, Space,
  } from '@xhs/delight'
  import { AddOne, ReduceOne } from '@xhs/delight/icons'
  import { cloneDeep, isEqual } from 'lodash'

  const props = defineProps({
    modelValue: {
      type: Array,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    isShowDurationHours: {
      type: Boolean,
      default: false,
    },
  })
  const emits = defineEmits(['update:modelValue'])

  const options = [{
                     label: '周一',
                     value: 1,
                   },
                   {
                     label: '周二',
                     value: 2,
                   },
                   {
                     label: '周三',
                     value: 3,
                   }, {
                     label: '周四',
                     value: 4,
                   },
                   {
                     label: '周五',
                     value: 5,
                   },
                   {
                     label: '周六',
                     value: 6,
                   },
                   {
                     label: '周日',
                     value: 7,
                   },
  ]

  // const hoursOptions = Array.from({ length: 24 }, (_, i) => ({
  //   label: `${i}点`,
  //   value: `${i < 10 ? `0${i}` : i}:00:00`,
  // }))

  const state = reactive({
    modelValue: [],
  })

  onMounted(() => {
    state.modelValue = cloneDeep(props.modelValue || [])
  })

  const handleAddParams = () => {
    state.modelValue.push({
      date: undefined,
      weeks: undefined,
      effectiveHourAndMinutes: undefined,
      stock: undefined,
      durationHours: undefined,
    })
  }

  const deleteItemByIndex = (index: number) => {
    state.modelValue.splice(index, 1)
  }

  const selectAllDays = (index: number) => {
    state.modelValue[index].weeks = options.map(o => o.value)
  }

  const computedModelValue = computed(() => JSON.parse(JSON.stringify(state.modelValue)))

  watch(computedModelValue, (newVal, oldVal) => {
    if (isEqual(newVal, oldVal)) {
      return
    }
    console.log('lxc new========', newVal)
    emits('update:modelValue', newVal)
  }, {
    deep: true,
  })

  const { modelValue } = toRefs(state)
</script>

<style lang="stylus" scoped>
  .test-params-input {
    display: flex;
    flex-direction: column;
    gap: 14px;

    .params-item {
      gap: 14px;
      align-items: center;
    }
    .params-title{
      font-size: 14px;
    }

    .operator {
      display: flex;
    }
  }
</style>
