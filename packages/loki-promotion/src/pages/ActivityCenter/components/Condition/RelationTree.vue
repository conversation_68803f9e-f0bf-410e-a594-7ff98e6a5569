<template>
  <Space
    direction="vertical"
    style="display: flex; align-items: start"
  >
    <div class="relation-tree">
      <RelationGroup
        :condition-map="subConditionRelation"
        :disabled="disabled"
        :condition-info="conditionInfo"
        @add-item="handleAddItem"
        @add-group="handleAddGroup"
        @del-item="handleDeleteItem"
        @switch-relation="handleSwitchRelation"
        @update-item="handleUpdateItem"
      />
    </div>
  </Space>
</template>

<script lang="ts" setup>
  import { Space } from '@xhs/delight'
  import { cloneDeep } from 'lodash'
  import {
    PropType,
    computed,
    onMounted,
    ref, watch,
  } from 'vue'
  import { getConditionComponentList } from '~/services/intelligentMarketing'
  import { IConditionConfig } from '~/types/intelligentMarketing'
  import RelationGroup from './RelationGroup.vue'

  const props = defineProps({
    value: {
      type: Object as PropType<IConditionConfig>,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    platForm: {
      type: String,
      default: 'PLAY_CENTER',
    },
  })

  const emits = defineEmits(['change'])

  const subConditionRelation = ref<IConditionConfig>({
    subConditionRelation: 'AND',
    subConditionConfigs: [],
  })

  const conditionInfo = ref([])
  const computedValue = computed(() => JSON.parse(JSON.stringify(props.value)))

  const unwatch = watch(computedValue, (newVal: IConditionConfig) => {
    if (JSON.stringify(newVal) !== '{}') {
      subConditionRelation.value = {
        ...cloneDeep(newVal),
      }
    }
    unwatch?.()
  }, {
    immediate: true,
    deep: true,
  })

  // 获取目标节点
  const getTargetCondition = (arr: string[]) => {
    let children: IConditionConfig = subConditionRelation.value

    while (arr.length) {
      children = children.subConditionConfigs[Number(arr[0])]
      arr.shift()
    }

    return children
  }

  // 添加单个条件项
  const handleAddItem = (position: string) => {
    if (position === '') {
      subConditionRelation.value.subConditionConfigs.push({
        conditionType: '',
        operator: '',
        value: '',
      })

      return
    }

    const arr = position.split('_')
    const res = getTargetCondition([...arr])
    res.subConditionConfigs.push({
      conditionType: '',
      operator: '',
      value: '',
    })
  }

  // 添加条件组
  const handleAddGroup = (e: { position: string; index: number }) => {
    if (e.position === '') {
      const tempCondition = { ...subConditionRelation.value.subConditionConfigs[e.index] }
      subConditionRelation.value.subConditionConfigs.splice(e.index, 1, {
        subConditionRelation: subConditionRelation.value.subConditionRelation === 'AND' ? 'OR' : 'AND',
        subConditionConfigs: [
          {
            ...tempCondition,
          },
          {
            conditionType: '',
            operator: '',
            value: '',
          },
        ],
      })

      return
    }

    const arr = e.position.split('_')
    const res = getTargetCondition([...arr])
    const tempCondition2 = { ...res.subConditionConfigs[e.index] }
    res.subConditionConfigs.splice(e.index, 1, {
      subConditionRelation: res.subConditionRelation === 'AND' ? 'OR' : 'AND',
      subConditionConfigs: [
        {
          ...tempCondition2,
        },
        {
          conditionType: '',
          operator: '',
          value: '',
        },
      ],
    })
  }

  // 删除单个条件项
  const handleDeleteItem = (e: { position: string; index: number }) => {
    if (e.position === '') {
      const copyData = subConditionRelation.value.subConditionConfigs.slice(0)
      copyData.splice(e.index, 1)
      subConditionRelation.value.subConditionConfigs = copyData.slice(0)
      if (subConditionRelation.value.subConditionConfigs.length === 1 && subConditionRelation.value.subConditionConfigs[0]?.subConditionConfigs) {
        subConditionRelation.value = { ...subConditionRelation.value.subConditionConfigs[0] }
      }
      return
    }

    const arr = e.position.split('_')
    const res = getTargetCondition([...arr])
    const copyArr = [...arr]
    const lastPos = Number(copyArr[copyArr.length - 1])
    // 找到其父节点
    copyArr.pop()
    const parent = getTargetCondition([...copyArr])
    // 先删除当前的
    const copyData = res.subConditionConfigs.slice(0)
    copyData.splice(e.index, 1)
    res.subConditionConfigs = copyData.slice(0)

    // 当条件组只剩一个节点时
    if (res?.subConditionConfigs && res.subConditionConfigs.length === 1) {
      const lastCondition = res.subConditionConfigs[0]
      const copyParentData = parent.subConditionConfigs.slice(0)
      copyParentData.splice(lastPos, 1)
      parent.subConditionConfigs = copyParentData.slice(0)
      // parent.subConditionConfigs.splice(lastPos, 1)
      parent.subConditionConfigs.push(lastCondition)
    }
  }

  // 切换关系
  const handleSwitchRelation = (position: string) => {
    if (position === '') {
      subConditionRelation.value.subConditionRelation = subConditionRelation.value.subConditionRelation === 'AND' ? 'OR' : 'AND'
      return
    }

    const arr = position.split('_')
    const res = getTargetCondition([...arr])

    res.subConditionRelation = res.subConditionRelation === 'AND' ? 'OR' : 'AND'
  }

  const getConditionInfo = async () => {
    // 获取条件项
    const res = await getConditionComponentList({ platForm: props.platForm })
    // 下线的策略条件，保留已经选中的，其他的筛选掉，已选中的 disable
    const list = subConditionRelation.value.subConditionConfigs.map(e => e.conditionType)
    // res.conditionComponentConfigs 筛选出status 不为 OFFLINE 或者 value 在 list 中的， 如果是 OFFLINE 切在 list 中 添加属性 disabled
    conditionInfo.value = res.conditionComponentConfigs.map(e => {
      if (e.status === 'OFFLINE') {
        if (list.includes(e.value)) {
          e.disabled = true
        }
      }
      return e
    }).filter(e => e.status !== 'OFFLINE' || list.includes(e.value))
  }

  // 更新条件项
  const handleUpdateItem = (e: { position: string; index: number }) => {
    if (e.position === '') {
      subConditionRelation.value.subConditionConfigs[e.index].operator = ''
      subConditionRelation.value.subConditionConfigs[e.index].value = ''

      return
    }

    const arr = e.position.split('_')
    const res = getTargetCondition([...arr])

    res.subConditionConfigs[e.index].operator = ''
    res.subConditionConfigs[e.index].value = ''
  }

  watch(subConditionRelation, newVal => {
    emits('change', { ...newVal })
  }, {
    deep: true,
  })

  onMounted(() => {
    // 初始化时，如果没有子条件，添加一个空条件
    if (!subConditionRelation.value.subConditionConfigs.length) {
      subConditionRelation.value.subConditionConfigs.push({
        conditionType: '',
        operator: '',
        value: '',
      })
    }
    getConditionInfo()
  })
</script>

<style lang="stylus" scoped>
  .relation-tree {
    overflow-x: scroll;

    &::-webkit-scrollbar {
      display: none;
    }
  }
</style>
