<template>
  <div class="key-word-input">
    <Button
      :disabled="disabled"
      @click="handleUploadKeyword"
    >
      {{ inputConfig?.btnText || '上传文件' }}
      {{ value === '' ? '(待上传)' : '(已上传)' }}
    </Button>
    <UploadModal
      ref="uploadModalRef"
      :modal-title="inputConfig?.modalTitle || '上传文件'"
      task-name="marketing_config_rule_keyword_upload"
      module-name="marketing"
      :task-params="{ keyWordType: 1 }"
      :template-link="inputConfig?.templateLink"
      :template-name="inputConfig?.templateName"
      @get-result="handleGetUploadResult"
    />
  </div>
</template>

<script lang="ts" setup>
  import {
    ref, defineProps, computed, defineEmits, PropType,
  } from 'vue'
  import { Button } from '@xhs/delight'
  import UploadModal from '../../../Launch/components/UploadModal.vue'

  const props = defineProps({
    value: {
      type: String,
      default: '',
    },
    inputConfig: {
      type: Object as PropType<Record<string, string>>,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  })
  const emits = defineEmits(['update:value'])

  const uploadModalRef = ref()

  const modelValue = computed({
    get: () => props.value,
    set: newVal => {
      emits('update:value', newVal)
    },
  })

  const handleUploadKeyword = () => {
    uploadModalRef.value.showModal()
  }

  const handleGetUploadResult = (e: Record<string, any>) => {
    modelValue.value = JSON.parse(e.extraJson).keywordGroupId || ''
  }
</script>

<style lang="stylus" scoped></style>
