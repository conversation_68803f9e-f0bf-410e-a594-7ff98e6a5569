<template>
  <Drawer
    v-model:visible="visibleState"
    :title="`${props.isEdit ? '编辑' : '新建'}关系条件`"
    size="50%"
    :on-close="handleClose"
  >
    <RelationTree
      :value="relationValue"
      :plat-form="platForm"
      @change="handleChange"
    />
    <div class="drawer-footer">
      <Button @click="handleClose">取消</Button>
      <Button
        type="primary"
        @click="handleConfirm"
      >确定</Button>
    </div>
  </Drawer>
</template>

<script setup lang="ts">
  import { Button, Drawer } from '@xhs/delight'
  import { cloneDeep } from 'lodash'
  import {
    defineEmits,
    defineExpose,
    defineProps,
    ref,
  } from 'vue'
  import { IConditionConfig } from '~/types/intelligentMarketing'
  import RelationTree from './RelationTree.vue'

  const props = defineProps({
    platForm: {
      type: String,
      default: 'PLAY_CENTER',
    },
    isEdit: {
      type: <PERSON><PERSON><PERSON>,
      default: false,
    },
  })

  const emit = defineEmits(['change'])

  const visibleState = ref(false)
  const relationValue = ref<IConditionConfig>()

  const open = (value: IConditionConfig) => {
    visibleState.value = true
    // 打开弹窗时，更新内部数据
    relationValue.value = cloneDeep(value)
  }

  const close = () => {
    visibleState.value = false
  }

  const handleClose = () => {
    close()
  }

  const handleChange = (value: IConditionConfig) => {
    relationValue.value = value
  }

  const handleConfirm = () => {
    emit('change', cloneDeep(relationValue.value))
    // handleClose()
  }

  // 暴露方法供外部调用
  defineExpose({
    open,
    close,
  })
</script>

<style lang="stylus" scoped>
  .drawer-footer
    margin-top 24px
    text-align right

    button
      margin-left 8px
</style>
