<template>
  <Select
    v-model="modelValue"
    placeholder="请输入搜索条件项"
    :disabled="disabled"
    :options="options"
    :loading="isConditionRuleListLoading"
    filterable
    style="width: 240px"
  />
</template>

<script lang="ts" setup>
  import {
    defineProps, computed, defineEmits, ref,
  } from 'vue'
  import { Select, toast } from '@xhs/delight'
  import { get } from 'lodash'
  import { getConditionRuleList } from '~/services/intelligentMarketing'

  const SELECT_VALUE_ENTITY_TYPE_MAP = {
    COUPON_CONDITION_RULE: 'COUPON',
  }

  const props = defineProps({
    value: {
      type: Number,
      default: null,
    },
    entityType: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  })
  const emits = defineEmits(['update:value'])

  const options = ref([])

  const modelValue = computed({
    get: () => Number(props.value),
    set: newVal => {
      emits('update:value', newVal)
    },
  })

  const isConditionRuleListLoading = ref(false)

  // 搜索条件项
  const filterConditionRuleList = async (name: string) => {
    try {
      isConditionRuleListLoading.value = true
      const entityType = get(SELECT_VALUE_ENTITY_TYPE_MAP, get(props, 'entityType', ''), '')
      const res: any = await getConditionRuleList({
        pageNum: 1,
        pageSize: 99999,
        nameLike: name,
        ruleEntityType: entityType,
      })
      if (res?.code === 0) {
        const conditionRuleOptions = get(res, 'data.conditionRules', []).map((item: any) => ({
          value: item.id,
          label: item.name,
        }))
        options.value = conditionRuleOptions
      } else {
        throw new Error(get(res, 'msg', ''))
      }
    } catch (e) {
      toast.danger('条件规则信息加载失败')
    } finally {
      isConditionRuleListLoading.value = false
    }
  }

  // const filterConditionRuleListDebounce = useDebounce(filterConditionRuleList, { delay: 500 })

  // const loadConditionRuleDetail = async (conditionRuleId: number) => {
  //   try {
  //     isConditionRuleListLoading.value = true
  //     const res: any = await getConditionRuleDetail({ conditionRuleId })
  //     if (res?.code === 0) {
  //       const conditionRuleOption = {
  //         value: get(res, 'data.conditionRule.id', null),
  //         label: get(res, 'data.conditionRule.name', ''),
  //       }
  //       options.value = [conditionRuleOption]
  //     } else {
  //       throw new Error(get(res, 'msg', ''))
  //     }
  //   } catch (e) {
  //     toast.danger(`加载条件规则详情失败${e.message}`)
  //   } finally {
  //     isConditionRuleListLoading.value = false
  //   }
  // }

  const init = () => {
    // if (Number(props.value)) {
    //   loadConditionRuleDetail(Number(props.value))
    // } else {
    filterConditionRuleList('')
    // }
  }

  init()
</script>
