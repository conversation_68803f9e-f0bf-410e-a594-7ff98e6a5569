<template>
  <div class="relation-group">
    <div class="relation-group-left">
      <Button
        type="primary"
        :icon="SortTwo"
        icon-position="right"
        :disabled="disabled"
        @click="handleSwitchRelation"
      >
        {{ conditionMap.subConditionRelation === 'AND' ? '且' : '或' }}
      </Button>
    </div>
    <div class="relation-group-right">
      <div class="condition-input">
        <template
          v-for="(item, index) in subConditionConfigs"
          :key="index"
        >
          <RelationGroup
            v-if="item.subConditionConfigs && item.subConditionConfigs.length"
            :disabled="disabled"
            :condition-map="item"
            :condition-info="conditionInfo"
            :position="getNewPosition(props.position, index)"
            @add-item="e => emits('addItem', e)"
            @add-group="e => emits('addGroup', { position: e.position, index: e.index })"
            @del-item="e => emits('delItem', { position: e.position, index: e.index })"
            @switch-relation="e => emits('switchRelation', e)"
            @update-item="e => emits('updateItem', { position: e.position, index: e.index })"
          />
          <div
            v-else
            class="relation-item"
          >
            <!-- 条件项选择 -->
            <Select
              v-model="item.conditionType"
              placeholder="请选择条件"
              style="width: 240px;"
              :options="conditionInfo"
              :disabled="disabled"
              filterable
              @change="handelChangeConditionType(index)"
            />
            <!-- 运算符选择 -->
            <Select
              v-model="item.operator"
              placeholder="运算符"
              style="width: 120px;"
              :options="CONDITION_MAP[item.conditionType]?.operatorConfigs || []"
              :disabled="disabled"
            />
            <div class="input-container">
              <!-- 文件上传 -->
              <KeywordInput
                v-if="CONDITION_MAP[item.conditionType]?.operators[item.operator]?.component === 'KeywordInput'"
                v-model:value="item.value"
                :input-config="CONDITION_MAP[item.conditionType]?.operators[item.operator]?.componentProps || {}"
                :disabled="disabled"
                :condition-type="item.conditionType"
              />
              <!-- 商品类目ID输入 -->
              <Popover
                v-else-if="CONDITION_MAP[item.conditionType]?.operators[item.operator]?.component === 'SkuCategoryInput'"
              >
                <Button>填写商品类目ID</Button>
                <template #content>
                  <div
                    class="category-input"
                    style="padding: 10px; background-color: #fff;"
                  >
                    <!-- Todo 暂时使用该种方式输入 后续迭代掉 -->
                    <CategoryInput
                      v-model:value="item.value"
                    />
                  </div>
                </template>
              </Popover>
              <!-- 数字 -->
              <Tooltip
                v-else-if="CONDITION_MAP[item.conditionType]?.operators[item.operator]?.component === 'InputNumber'"
                :content="CONDITION_MAP[item.conditionType]?.operators[item.operator]?.help || undefined"
              >
                <InputNumber
                  v-model="item.value"
                  style="width: 120px;"
                  :disabled="disabled"
                  placeholder="值(数字)"
                  :min="CONDITION_MAP[item.conditionType]?.operators[item.operator]?.componentProps?.min || -Infinity"
                  :max="CONDITION_MAP[item.conditionType]?.operators[item.operator]?.componentProps?.max || Infinity"
                  validate-timing="immediate"
                  :validate="e => validateNumberInputValue(e, CONDITION_MAP[item.conditionType]?.operators[item.operator]?.componentProps)"
                />
              </Tooltip>
              <!-- 普通单选值 -->
              <Select
                v-else-if="CONDITION_MAP[item.conditionType]?.operators[item.operator]?.component === 'Select'"
                v-model="item.value"
                placeholder="值"
                style="width: 120px;"
                :options="CONDITION_MAP[item.conditionType]?.operators[item.operator]?.componentProps?.options || []"
                :disabled="disabled"
              />
              <!-- 条件规则选值 -->
              <RuleConditionSelect
                v-else-if="CONDITION_MAP[item.conditionType]?.operators[item.operator]?.component === 'RuleConditionSelect'"
                v-model:value="item.value"
                :entity-type="item.conditionType"
                :disabled="disabled"
              />
              <!-- 默认提示 -->
              <Input
                v-else
                disabled
                style="width: 164px;"
                placeholder="请先选择条件和运算符"
              />
            </div>
            <a
              v-if="item.conditionType === 'QIXI_HIGH_QUERY_CATEGORY'"
              href="https://drive.weixin.qq.com/s?k=ANAAyQcbAAgwaJwJmQAfQARQZrAOQ#/preview?fileId=i.1970324973013456.1688857987466740_f.697523482gswU"
              target="_blank"
              class="CategoryList"
            >类目list</a>
            <Button
              :icon="RightBranchOne"
              :disabled="disabled"
              @click="handleAddGroup(position, index)"
            />
            <Button
              style="color: #ff2442"
              :icon="Delete"
              :disabled="disabled"
              @click="handleDeleteItem(position, index)"
            />
          </div>
        </template>
      </div>
      <div class="operator">
        <Button
          :icon="AddOne"
          type="secondary"
          :disabled="disabled"
          @click="handleAddItem(position)"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import {
    computed, defineProps, defineEmits,
  } from 'vue'
  import {
    Select, Button, Popover, InputNumber, Input, Tooltip,
  } from '@xhs/delight'
  import {
    AddOne, Delete, RightBranchOne, SortTwo,
  } from '@xhs/delight/icons'
  import CategoryInput from './CategoryInput.vue'
  import KeywordInput from './KeywordInput.vue'
  import RuleConditionSelect from './RuleConditionSelect.vue'

  const props = defineProps({
    conditionMap: {
      type: Object,
      default: () => ({}),
    },
    conditionInfo: {
      type: Array,
      default: () => ([]),
    },
    position: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  })
  const emits = defineEmits(['addItem', 'delItem', 'addGroup', 'switchRelation', 'updateItem'])

  // Condition Map 重新生成对前端使用比较友好的结构
  const CONDITION_MAP = computed(() => {
    const map = {}

    if (props.conditionInfo && props.conditionInfo.length > 0) {
      props.conditionInfo.forEach((item: any) => {
        const operators = {}
        // 重新生成运算符结构
        item.operatorConfigs.forEach(op => {
          operators[op.value] = {
            name: op.name,
            value: op.value,
            component: op.valueConfig?.component || '',
            componentProps: op.valueConfig?.componentPropertyConfig || {},
            help: op.help,
          }
        })
        map[item.value] = {
          ...item,
          operators,
        }
      })
    }

    return map
  })

  // 校验数字输入
  const validateNumberInputValue = (e: any, componentProps: any) => {
    const { min = -Infinity, max = Infinity } = componentProps
    return e.modelValue >= min && e.modelValue <= max
  }

  // 渲染的数据
  const subConditionConfigs = computed(() => props.conditionMap?.subConditionConfigs || [])

  // 生成节点位置记录
  const getNewPosition = (pos: string, i: number) => (pos ? `${pos}_${i}` : String(i))

  // 添加同级条件
  const handleAddItem = (position: string) => {
    emits('addItem', position)
  }

  // 创建条件组
  const handleAddGroup = (position: string, index: number) => {
    emits('addGroup', { position, index })
  }

  // 删除条件项
  const handleDeleteItem = (position: string, index: number) => {
    emits('delItem', { position, index })
  }

  // 切换关系
  const handleSwitchRelation = () => {
    emits('switchRelation', props.position)
  }

  // 切换条件项
  // 此处需要单独处理的原因是 不同条件项对应的运算符 以及可选的值会存在不同的情况 所以每次切换条件项是我们需要将
  // 运算符和值进行重置
  const handelChangeConditionType = (index: number) => {
    emits('updateItem', { position: props.position, index })
  }
</script>

<style lang="stylus" scoped>
  .relation-group {
    width: 100%;
    min-height: 32px;
    display: flex;

    &-left {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      border-right: 1px solid #c0c0c0;
      margin-right: 14px;
      padding-right: 14px;

      .condition-btn {
        position: relative;
      }
    }

    &-right {
      display: flex;
      flex-direction: column;
      gap: 10px;

      .condition-input {
        display: flex;
        flex-direction: column;
        gap: 10px;

        .relation-item {
          display: flex;
          gap: 10px;
          align-items: flex-start;

          &:deep(.delight-material-cascader-menu__list) {
            &::-webkit-scrollbar {
              display: none;
            }
          }

          .input-container {
            display: flex;
            flex-direction: column;
          }
        }
      }
    }
  }
</style>

<style>
  .category-input {
    border-radius: 4px;
  }
  .CategoryList {
    font-size: 14px;
    align-self: center;
    color: #3c66ff;
  }
</style>
