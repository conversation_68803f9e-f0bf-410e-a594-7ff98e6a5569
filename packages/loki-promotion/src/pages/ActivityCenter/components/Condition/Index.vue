<template>
  <div class="relation-container">
    <div class="relation-layout">
      <Button
        type="primary"
        size="small"
        :loading="loading"
        @click="openModal"
      >编辑</Button>
      <RelationTree
        :key="JSON.stringify(relationValue)"
        :value="relationValue"
        :disabled="true"
        :plat-form="platForm"
        @change="handleChange"
      />
    </div>
    <RelationTreeModal
      ref="relationModalRef"
      :plat-form="platForm"
      :is-edit="!!value"
      @change="handleModalChange"
    />
  </div>
</template>

<script lang="ts" setup>
  import { Button, toast } from '@xhs/delight'
  import { cloneDeep } from 'lodash'
  import {
    onMounted, PropType, ref, watch,
  } from 'vue'
  import { getConditionComponentConfig, upsertConditionComponent } from '~/services/intelligentMarketing'
  import { IConditionConfig } from '~/types/intelligentMarketing'
  import RelationTree from './RelationTree.vue'
  import RelationTreeModal from './RelationTreeModal.vue'

  type ConditionValue = {
    id: string
    conditionConfig: IConditionConfig
  }

  const props = defineProps({
    value: {
      type: Object as PropType<ConditionValue>,
      default: null,
    },
    platForm: {
      type: String,
      default: 'PLAY_CENTER',
    },
  })

  const emit = defineEmits(['update:value', 'change'])

  const relationValue = ref<IConditionConfig>({
    subConditionRelation: 'AND',
    subConditionConfigs: [],
  })

  const relationModalRef = ref()
  const loading = ref(false)

  // 获取数据
  const fetchData = async (id: string | number) => {
    if (!id) return

    try {
      loading.value = true
      const res = await getConditionComponentConfig({ id, platForm: props.platForm })
      if (res?.response?.success) {
        relationValue.value = res?.conditionConfig
        relationModalRef.value?.close()
      }
    } catch (error) {
      console.error('获取关系配置失败:', error)
      toast.danger('获取关系配置失败')
    } finally {
      loading.value = false
    }
  }

  // 当 modelValue 变化时，获取数据
  watch(() => props.value, newVal => {
    console.log('newVal', newVal)
    if (newVal?.id) {
      fetchData(newVal.id)
    } else {
      // 如果没有 ID，初始化空数据
      relationValue.value = {
        subConditionRelation: 'AND',
        subConditionConfigs: [],
      }
    }
  }, { immediate: true })

  // 创建数据
  const createData = async (data: IConditionConfig) => {
    try {
      loading.value = true
      const res = await upsertConditionComponent({
        componentInfo: data,
        platForm: props.platForm,
      })
      if (res?.response?.success) {
        relationValue.value = data
        // 创建成功后，更新 ID
        const emitData: { id: string | number; extraData?: any } = { id: res.id }
        if (res.extraData) {
          emitData.extraData = res.extraData
        }
        emit('update:value', emitData)
        emit('change', emitData)
        toast.success('创建成功')
        relationModalRef.value?.close()
      } else {
        toast.danger(res?.response?.msg)
      }
    } catch (error) {
      console.error('创建关系配置失败:', error?.message || error?.msg || error)
      toast.danger(error?.message || error?.msg || error)
    } finally {
      loading.value = false
    }
  }

  // 更新数据
  const updateData = async (id: string | number, data: IConditionConfig) => {
    try {
      loading.value = true
      const res = await upsertConditionComponent({
        id,
        componentInfo: data,
        platForm: props.platForm,
      })
      if (res?.response?.success) {
        relationValue.value = data
        const emitData: { id: string | number; extraData?: any } = { id: res.id }
        if (res.extraData) {
          emitData.extraData = res.extraData
        }
        emit('update:value', emitData)
        emit('change', emitData)
      }
      toast.success('更新成功')
    } catch (error) {
      console.error('更新关系配置失败:', error)
      toast.danger('更新关系配置失败')
    } finally {
      loading.value = false
    }
  }

  const openModal = () => {
    relationModalRef.value?.open(cloneDeep(relationValue.value))
  }

  // RelationTree 组件中的数据变更（一般不会触发，因为是只读的）
  const handleChange = (value: IConditionConfig) => {
    relationValue.value = value
  }

  // Modal 中的数据变更，根据是否有 ID 进行创建或更新
  const handleModalChange = (value: IConditionConfig) => {
    if (props.value?.id) {
      // 有 ID，更新数据
      updateData(props.value.id, value)
    } else {
      // 没有 ID，创建数据
      createData(value)
    }
  }

  onMounted(() => {
    // 如果有 ID，初始化时获取数据
    console.log('props.value', props.value)
    if (props.value?.id) {
      fetchData(props.value.id)
    }
  })
</script>

<style lang="stylus" scoped>
.relation-container
  margin-bottom 20px

.relation-layout
  display flex
  align-items center
  gap 16px

  // 让 RelationTree 占据主要空间
  :deep(.relation-tree)
    flex 1
</style>
