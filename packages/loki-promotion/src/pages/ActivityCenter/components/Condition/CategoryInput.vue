<template>
  <div class="category-input">
    <TextArea
      v-model="modelValue"
      placeholder="请输入商品类目ID(输入多个时回车换行)"
    />
  </div>
</template>

<script lang="ts" setup>
  import { defineProps, computed, defineEmits } from 'vue'
  import { TextArea } from '@xhs/delight'

  const props = defineProps({
    value: {
      type: String,
      default: '',
    },
  })
  const emits = defineEmits(['update:value'])

  const modelValue = computed({
    get: () => (props.value ? JSON.parse(props.value).join('\n') : ''),
    set: (newVal: string) => {
      emits('update:value', JSON.stringify(newVal.split('\n')))
    },
  })
</script>

<style lang="stylus" scoped>
</style>
