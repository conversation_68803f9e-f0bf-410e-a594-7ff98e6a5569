<template>
  <Select
    v-model="selectedValue"
    :options="playOptions"
    :loading="loading"
    :placeholder="'请选择玩法组件实例'"
    filterable
    :disabled="disabled || !props.activityComponentType"
    @update:value="handleChange"
  />
</template>

<script setup lang="ts">
  import { Select, toast } from '@xhs/delight'
  import {
    ref, onMounted, computed, watch,
  } from 'vue'
  import { getActivityComponentList } from '~/services/activityCenter'

  interface PlayOption {
    label: string
    value: string | number
  }

  const props = defineProps({
    value: {
      type: [String, Number],
      default: undefined,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    activityComponentType: {
      type: Number,
      default: undefined, // 不设置默认值
    },
  })

  const emit = defineEmits(['update:value', 'change'])

  const loading = ref(false)
  const playOptions = ref<PlayOption[]>([])

  const selectedValue = computed({
    get: () => props.value,
    set: value => {
      emit('update:value', value)
      emit('change', value)
    },
  })

  // 获取玩法列表
  const fetchPlayList = async () => {
    // 如果没有指定组件类型，不发起请求
    if (!props.activityComponentType) {
      playOptions.value = []
      return
    }

    try {
      loading.value = true
      const res = await getActivityComponentList({
        pageNum: 1,
        pageSize: 99999,
        activityComponentType: props.activityComponentType,
      })

      const { activityComponents } = res
      playOptions.value = activityComponents.map((component: any) => ({
        value: component.id,
        label: component.name,
      }))
    } catch (error) {
      // 获取玩法列表失败，显示错误提示
      toast.danger('获取玩法列表失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  const handleChange = (value: string | number) => {
    selectedValue.value = value
  }

  // 监听 activityComponentType 变化，重新获取数据
  watch(() => props.activityComponentType, () => {
    fetchPlayList()
  }, { immediate: false })

  onMounted(() => {
    fetchPlayList()
  })
</script>
