// 实例状态枚举（基于真实API接口：1在线，2下线）
export enum ComponentInstanceStatusEnum {
  Online = 1, // 在线
  Offline = 2, // 下线
}

// 实例状态对应的标签颜色
export const INSTANCE_STATUS_TAG_TYPE: Record<ComponentInstanceStatusEnum, 'yellow' | 'green' | 'red' | 'grey'> = {
  [ComponentInstanceStatusEnum.Online]: 'green', // 在线 - 绿色
  [ComponentInstanceStatusEnum.Offline]: 'grey', // 下线 - 灰色
}

// 实例状态显示文本
export const INSTANCE_STATUS_TEXT: Record<ComponentInstanceStatusEnum, string> = {
  [ComponentInstanceStatusEnum.Online]: '在线',
  [ComponentInstanceStatusEnum.Offline]: '下线',
}

// 实例状态选项
export const INSTANCE_STATUS_OPTIONS = Object.entries(INSTANCE_STATUS_TEXT).map(([value, label]) => ({
  value: Number(value),
  label,
}))
