<template>
  <HeaderBreadcrumb :items="breadcrumbList" />
  <Spinner :spinning="loading">
    <Panel
      title="基础信息"
      style="flex-direction: column; gap: 10px; padding: 20px 40px 40px 40px"
    >
      <Form
        ref="formRef"
        :model="formData"
        :disabled="isViewMode"
        :rules="formRules"
      >
        <FormItem
          label="实例名称"
          name="name"
        >
          <Input
            v-model="formData.name"
            placeholder="请输入组件实例名称"
            :max-length="50"
            style="width: 400px;"
          />
        </FormItem>
        <FormItem
          label="实例类型"
          name="activityComponentType"
        >
          <Select
            v-model="formData.activityComponentType"
            placeholder="请选择组件实例类型"
            :options="componentTypeOptions"
            style="width: 400px;"
            @change="handleComponentTypeChange"
          />
        </FormItem>
        <FormItem
          label="实例状态"
          name="status"
        >
          <Select
            v-model="formData.status"
            placeholder="请选择状态"
            :options="statusOptions"
            style="width: 400px;"
          />
        </FormItem>
      </Form>
    </Panel>

    <!-- 组件配置表单 -->
    <Panel
      v-if="formData.activityComponentType && componentFormSchema"
      :title="`${getComponentTypeName(formData.activityComponentType)} - 配置信息`"
      style="flex-direction: column; gap: 10px; padding: 20px 40px 40px 40px"
    >
      <DelightForm
        ref="componentFormRef"
        :config="componentFormSchema"
        :components="componentFormComponents"
      />
    </Panel>

    <ButtonGroup :buttons="buttons" />
  </Spinner>
</template>

<script setup lang="ts">
  import {
    ref, computed, onMounted, watch, nextTick,
  } from 'vue'
  import { useRoute } from 'vue-router'
  import {
    toast,
    Spinner,
    Form2 as Form,
    FormItem2 as FormItem,
    Input,
    Select,
  } from '@xhs/delight'
  import { DelightForm } from '@xhs/use-formily-delight'
  import Panel from '~/components/Panel/Index.vue'
  import HeaderBreadcrumb from '~/components/HeaderBreadcrumb/Index.vue'
  import ButtonGroup from '~/components/ButtonGroup/Index.vue'
  import { useComponentInstanceForm } from './handler'
  import { INSTANCE_STATUS_OPTIONS } from './common'
  // 从 Activity/resolver 导入公共函数替代本地的 resolver.ts
  import { usePlayFormComponents, resolveSchemaFromConfig } from '../Activity/resolver'
  import { FORM_TYPE } from '~/hooks/useFormilyType'

  // 定义 props
  const props = withDefaults(defineProps<{
    formType?: FORM_TYPE
  }>(), {
    formType: FORM_TYPE.CREATE,
  })

  const route = useRoute()
  const {
    loading,
    formData,
    formRules,
    isViewMode,
    isEditMode,
    isCopyMode,
    pageTitle,
    loadInstanceDetail,
    saveInstance,
    goBack,
    activityTypeList,
    fetchPlayData,
  } = useComponentInstanceForm(props.formType)

  const componentTypeOptions = computed(() => activityTypeList.value.map((item: any) => ({ value: item.activityType, label: item.name })))
  const statusOptions = INSTANCE_STATUS_OPTIONS
  const componentFormComponents = usePlayFormComponents()

  // 面包屑导航
  const breadcrumbList = ref([
    { title: '玩法实例管理', to: { name: 'ComponentInstanceList' } },
    { title: pageTitle.value },
  ])

  // 组件配置表单相关
  const componentFormRef = ref()
  const componentFormSchema = ref(null)

  // 获取组件类型名称
  const getComponentTypeName = (componentType: number) => {
    const option = componentTypeOptions.value.find(opt => opt.value === componentType)
    return option?.label || '未知组件'
  }

  // 处理组件类型变化
  const handleComponentTypeChange = async (componentType: number) => {
    if (!componentType) {
      componentFormSchema.value = null
      return
    }

    try {
      // 需要先从列表中获取对应componentType的配置
      const componentConfig = activityTypeList.value.find(item => item.activityType === componentType)
      if (!componentConfig) {
        toast.danger({
          description: '获取组件配置失败，请重试',
          closeable: true,
        })
        return
      }
      const configJson = componentConfig.template.value || {}
      const config = JSON.parse(configJson)
      // 获取对应组件类型的表单 schema
      const schema = await resolveSchemaFromConfig(config)

      componentFormSchema.value = schema

      // 等待 DelightForm 渲染完成后，如果是编辑模式，需要重新设置表单值
      await nextTick()
      if (componentFormRef.value && formData.value.componentConfig) {
        try {
          const configData = JSON.parse(formData.value.componentConfig)
          componentFormRef.value.form.setValues(configData)
        } catch (error) {
          // 解析组件配置数据失败，忽略
        }
      }
    } catch (error) {
      toast.danger({
        description: '获取组件配置失败，请重试',
        closeable: true,
      })
    }
  }

  // 表单引用
  const formRef = ref()

  // 点击取消
  const clickCancel = () => {
    goBack()
  }

  // 提交表单
  const submit = async () => {
    try {
      loading.value = true

      // 收集组件配置数据
      let componentConfigData = {}
      if (componentFormRef.value?.form) {
        componentConfigData = componentFormRef.value.form.values || {}
      }

      // 将配置数据序列化为 JSON 字符串
      formData.value.componentConfig = JSON.stringify(componentConfigData)

      await saveInstance()
    } catch (err: any) {
      toast.danger({
        description: err?.message || '保存失败，请重试',
        closeable: true,
      })
    } finally {
      loading.value = false
    }
  }

  // 点击保存，校验表单
  const handleSubmit = async () => {
    if (loading.value || isViewMode.value) {
      return
    }

    try {
      // 基础表单验证
      await formRef.value.validate()

      // 组件配置表单验证
      if (componentFormRef.value?.form) {
        await componentFormRef.value.form.validate()
      }

      // 提交表单
      await submit()
    } catch (err: any) {
      toast.danger({
        description: '表单校验失败，请检查必填项',
        closeable: true,
      })
    }
  }

  // 按钮配置
  const buttons = computed(() => {
    const buttonList = [
      {
        label: isViewMode.value ? '返回' : '取消',
        onClick: clickCancel,
      },
    ]

    if (!isViewMode.value) {
      buttonList.push({
        label: '保存',
        onClick: handleSubmit,
      })
    }

    return buttonList
  })

  // 监听 formData.activityComponentType 变化
  watch(() => formData.value.activityComponentType, newType => {
    if (newType) {
      // 预览或编辑情况下，接口请求还没完成时，调用这个方法会出错
      if (loading.value) return
      handleComponentTypeChange(newType)
    }
  }, { immediate: true })

  // 页面初始化
  onMounted(async () => {
    const instanceId = route.params.instanceId as string
    await fetchPlayData()
    if (instanceId && (isEditMode.value || isViewMode.value || isCopyMode.value)) {
      await loadInstanceDetail(instanceId)
      // 如果有组件类型，加载对应的表单配置
      if (formData.value.activityComponentType) {
        await handleComponentTypeChange(formData.value.activityComponentType)
      }
      if (isViewMode.value) {
        // 如果是查看模式，禁用表单
        nextTick(() => {
          if (componentFormRef.value) {
            componentFormRef.value.form.disabled = true
          }
        })
      }
    }
  })
</script>

<style scoped>
  .component-instance-create {
    height: 100%;
  }
</style>
