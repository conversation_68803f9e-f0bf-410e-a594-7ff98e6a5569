<template>
  <Search
    :list="searchList"
    :columns="3"
    @search="onSearch"
  />
  <DataList
    use-panel
    :buttons="buttons"
    :columns="columns"
    :data-source="dataSource"
    :loading="loading"
    :pagination="pagination"
    @page-change="onPageChange"
    @size-change="onSizeChange"
  />
</template>

<script setup lang="tsx">
  import {
    ref, computed, h, onMounted,
  } from 'vue'
  import {
    Text, Space, Tag, Modal,
  } from '@xhs/delight'
  import dayjs from 'dayjs'
  import Search from '~/components/Search/Index.vue'
  import DataList from '~/components/DataList/Index.vue'
  import CopyText from '~/components/CopyText/Index.vue'
  import {
    ComponentInstanceStatusEnum,
  } from './common'
  import { useComponentInstanceList } from './handler'
  import { handleError } from '../utils/handleError'
  import useSearch from '~/hooks/useSearch'
  import { getActivityComponentList } from '~/services/activityCenter'

  const {
    loading,
    dataSource,
    activityTypeList,
    goToCreate,
    goToEdit,
    goToView,
    copyInstance,
    offlineInstance,
    getStatusTag,
    fetchPlayData,
  } = useComponentInstanceList()

  const {
    query,
    pagination,
    onSearch,
    onPageChange,
    onSizeChange,
  } = useSearch(searchFn)

  // 搜索函数
  async function searchFn() {
    try {
      loading.value = true
      const { time = {} } = query.value || {}
      const startTime = time?.start ? dayjs(time.start).unix() : null
      const endTime = time?.end ? dayjs(time.end).unix() : null

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { time: _, ...restQuery } = query.value || {}
      const apiQuery = {
        ...restQuery,
        pageNum: pagination.pageNo,
        pageSize: pagination.pageSize,
        ...(startTime && { startTime }),
        ...(endTime && { endTime }),
      }

      const result = await getActivityComponentList(apiQuery)
      dataSource.value = result.activityComponents || []
      pagination.total = result.total || 0
    } catch (error) {
      handleError(error, {
        title: '查询失败',
        message: '获取玩法实例列表失败，请稍后重试',
      })
    } finally {
      loading.value = false
    }
  }

  const buttons = computed(() => ([
    {
      label: '创建组件实例',
      type: 'primary' as const,
      onClick: goToCreate,
    },
  ]))

  const searchList = ref([
    {
      label: '实例名称',
      name: 'nameLike',
      type: 'text' as const,
      placeholder: '请输入',
    },
    {
      label: '组件类型',
      name: 'activityComponentType',
      type: 'select' as const,
      placeholder: '请选择',
      options: activityTypeList,
    },
    {
      label: '状态',
      name: 'status',
      type: 'select' as const,
      placeholder: '请选择',
      options: [
        {
          label: '在线',
          value: ComponentInstanceStatusEnum.Online,
        },
        {
          label: '下线',
          value: ComponentInstanceStatusEnum.Offline,
        },
      ],
    },
    {
      label: '起止时间',
      name: 'time',
      type: 'dateRange',
      placeholder: '请选择',
    },
    {
      label: '创建人',
      name: 'creatorEmail',
      type: 'creatorSelect' as const,
      placeholder: '请输入',
    },
  ] as any)

  // 查看
  const viewRow = (instanceId: number) => {
    goToView(instanceId)
  }

  // 编辑
  const editRow = (instanceId: number) => {
    goToEdit(instanceId)
  }

  // 复制
  const copyRow = (instanceId: number) => {
    copyInstance(instanceId)
  }

  // 下线
  const offlineRow = (instanceId: number) => {
    Modal.warning({
      title: '下线实例',
      content: h(Text, {}, {
        default: () => '下线后将停止服务，确认下线吗？',
      }),
      async onConfirm(close) {
        try {
          await offlineInstance(instanceId)
        } catch (err) {
          handleError(err, {
            title: '操作失败',
            message: '下线实例失败，请稍后重试',
          })
          return
        } finally {
          close?.()
        }
        searchFn()
      },
      onCancel(close) {
        close?.()
      },
      cancelText: '再想想',
      confirmText: '确认下线',
    })
  }

  const columns = [
    {
      title: '实例名称',
      dataIndex: 'name',
      minWidth: 300,
      render: ({
        rowData: { name, id },
      }) => <div class="name-id-info">
      <h3>{name}</h3>
      <CopyText text={id}>
        <Text>ID：{id}</Text>
      </CopyText>
    </div>,
    },
    {
      title: '组件类型',
      dataIndex: 'activityComponentType',
      width: 120,
      render: ({ rowData: { activityComponentType } }) => {
        const type = activityTypeList.value.find(item => item.value === activityComponentType)
        return <Text>{type?.label || activityComponentType}</Text>
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 120,
      render: ({ rowData: { status } }) => {
        const statusInfo = getStatusTag(status)
        return <Tag
          size='small'
          color={statusInfo.color as any}
        >
          {statusInfo.text}
        </Tag>
      },
    },
    {
      title: '创建人',
      dataIndex: 'createBy',
      width: 120,
      render: ({ rowData: { createBy } }) => <Text>{createBy || '-'}</Text>,
    },
    {
      title: '更新人/更新时间',
      dataIndex: 'updateBy',
      minWidth: 160,
      render: ({ rowData: { updateBy = '', updateAt } }) => <div class="lines">
      <Text>{ updateBy }</Text>
      <Text>{ dayjs(updateAt).format('YYYY-MM-DD HH:mm:ss') }</Text>
    </div>,
    },
    {
      title: '操作',
      dataIndex: 'actions',
      width: 220,
      fixed: 'right',
      render: ({ rowData: { id, status } }) => <Space>
        {
          [
            { label: '查看', onClick: () => viewRow(id), show: true },
            { label: '编辑', onClick: () => editRow(id), show: status !== ComponentInstanceStatusEnum.Offline },
            { label: '复制', onClick: () => copyRow(id), show: true },
            { label: '下线', onClick: () => offlineRow(id), show: status === ComponentInstanceStatusEnum.Online },
          ].map(({ label, show, ...rest }) => (
            typeof show !== 'boolean' || show ? <Text
              {...rest}
              link={true}
            >{label}</Text> : null
          ))
        }
      </Space>,
    },
  ]

  onMounted(() => {
    searchFn()
    fetchPlayData()
  })
</script>

<style lang="stylus" scoped>
  .name-id-info h3 {
    margin: 0 0 4px 0;
    font-size: 14px;
    font-weight: 500;
    color: #1f2329;
  }
:deep(.lines) {
  display: flex
  flex-direction: column
  align-items: flex-start
  color: #2D2D2D
  .d-text {
    margin: 0
    line-height: 20px
    font-size: 14px
  }
}
</style>
