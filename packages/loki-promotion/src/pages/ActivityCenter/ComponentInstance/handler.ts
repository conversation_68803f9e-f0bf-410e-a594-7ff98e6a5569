import { ref, computed } from 'vue'
import { toast } from '@xhs/delight'
import { useRoute, useRouter } from 'vue-router'
import { FORM_TYPE } from '~/hooks/useFormilyType'
import { ActivityComponent, ComponentInstanceFormValue } from './types'
import { ComponentInstanceStatusEnum, INSTANCE_STATUS_TEXT, INSTANCE_STATUS_TAG_TYPE } from './common'
import {
  getActivityComponentDetail,
  createActivityComponent,
  updateActivityComponent,
  offlineActivityComponent,
  getPlayList,
} from '~/services/activityCenter'
import { handleError } from '../utils/handleError'

// 列表页面处理函数
export function useComponentInstanceList() {
  const router = useRouter()
  const loading = ref(false)
  const dataSource = ref<ActivityComponent[]>([])
  const activityTypeList = ref([])

  // 跳转到创建页面
  const goToCreate = () => {
    router.push({ name: 'CreateComponentInstance' })
  }

  // 跳转到编辑页面
  const goToEdit = (instanceId: number) => {
    router.push({ name: 'EditComponentInstance', params: { instanceId: String(instanceId) } })
  }

  // 跳转到查看页面
  const goToView = (instanceId: number) => {
    router.push({ name: 'ViewComponentInstance', params: { instanceId: String(instanceId) } })
  }

  // 复制实例
  const copyInstance = (instanceId: number) => {
    router.push({ name: 'CopyComponentInstance', params: { instanceId: String(instanceId) } })
  }

  // 下线实例
  const offlineInstance = async (instanceId: number) => {
    try {
      await offlineActivityComponent({ id: instanceId })
      toast.success('下线成功')
    } catch (error) {
      handleError(error, {
        title: '操作失败',
        message: '下线实例失败，请稍后重试',
      })
    }
  }

  // 获取状态标签
  const getStatusTag = (status: ComponentInstanceStatusEnum) => ({
    text: INSTANCE_STATUS_TEXT[status],
    color: INSTANCE_STATUS_TAG_TYPE[status],
  })

  // 获取玩法列表
  const fetchPlayData = async () => {
    loading.value = true
    try {
      const res = await getPlayList({
        pageNum: 1,
        pageSize: 99999,
        allowScenes: 'component',
      })
      const { activityTypes } = res
      activityTypeList.value = activityTypes.map((play: any) => ({ value: play.activityType, label: play.name }))
    } catch (error) {
      handleError(error, {
        title: '获取玩法列表失败',
        message: '请稍后重试',
      })
    } finally {
      loading.value = false
    }
  }

  return {
    loading,
    dataSource,
    activityTypeList,
    goToCreate,
    goToEdit,
    goToView,
    copyInstance,
    offlineInstance,
    getStatusTag,
    fetchPlayData,
  }
}

// 创建/编辑页面处理函数
export function useComponentInstanceForm(formType?: FORM_TYPE) {
  const route = useRoute()
  const router = useRouter()
  const loading = ref(false)
  const activityTypeList = ref([])
  const formRules = {
    name: [{ required: true, message: '请输入实例名称！' }],
    activityComponentType: [{ required: true, message: '请选择组件类型！' }],
    status: [{ required: true, message: '请选择状态！' }],
  }
  const formData = ref<ComponentInstanceFormValue>({
    name: '',
    activityComponentType: undefined,
    componentConfig: undefined,
    status: ComponentInstanceStatusEnum.Online,
  })

  // 表单类型 - 优先使用传入的 formType，否则从路由 meta 中获取
  const currentFormType = computed(() => formType || route.meta?.formType || FORM_TYPE.CREATE)

  // 是否为查看模式
  const isViewMode = computed(() => currentFormType.value === FORM_TYPE.VIEW)

  // 是否为编辑模式
  const isEditMode = computed(() => currentFormType.value === FORM_TYPE.EDIT)

  // 是否为复制模式
  const isCopyMode = computed(() => currentFormType.value === FORM_TYPE.COPY)

  // 页面标题
  const pageTitle = computed(() => {
    switch (currentFormType.value) {
      case FORM_TYPE.CREATE:
        return '创建组件实例'
      case FORM_TYPE.EDIT:
        return '编辑组件实例'
      case FORM_TYPE.VIEW:
        return '查看组件实例'
      case FORM_TYPE.COPY:
        return '复制组件实例'
      default:
        return '组件实例'
    }
  })

  // 获取玩法列表
  const fetchPlayData = async () => {
    loading.value = true
    try {
      const res = await getPlayList({
        pageNum: 1,
        pageSize: 99999,
        allowScenes: 'component',
      })
      const { activityTypes } = res
      activityTypeList.value = activityTypes
    } catch (error) {
      handleError(error, {
        title: '获取玩法列表失败',
        message: '请稍后重试',
      })
    } finally {
      loading.value = false
    }
  }

  // 加载实例详情
  const loadInstanceDetail = async (instanceId: string) => {
    loading.value = true
    try {
      const result = await getActivityComponentDetail({ id: Number(instanceId) })
      const component = result.activityComponent
      if (component) {
        formData.value = {
          name: component.name || '',
          activityComponentType: component.activityComponentType || 1,
          componentConfig: component.componentConfig || '{}',
          status: component.status || ComponentInstanceStatusEnum.Online,
        }
      }
    } catch (error) {
      handleError(error, {
        title: '查询失败',
        message: '加载实例详情失败，请稍后重试',
      })
    } finally {
      loading.value = false
    }
  }

  // 保存实例
  const saveInstance = async () => {
    loading.value = true
    try {
      const instanceId = route.params.instanceId as string

      if (currentFormType.value === FORM_TYPE.CREATE || currentFormType.value === FORM_TYPE.COPY) {
        await createActivityComponent({
          activityComponent: formData.value,
        })
        toast.success('创建成功')
      } else if (currentFormType.value === FORM_TYPE.EDIT) {
        await updateActivityComponent({
          activityComponent: {
            id: Number(instanceId),
            ...formData.value,
          },
        })
        toast.success('保存成功')
      }

      router.push({ name: 'ComponentInstanceList' })
    } catch (error) {
      handleError(error, {
        title: currentFormType.value === FORM_TYPE.EDIT ? '保存失败' : '创建失败',
        message: `${currentFormType.value === FORM_TYPE.EDIT ? '保存' : '创建'}实例失败，请稍后重试`,
      })
    } finally {
      loading.value = false
    }
  }

  // 返回列表
  const goBack = () => {
    router.push({ name: 'ComponentInstanceList' })
  }

  return {
    loading,
    formData,
    formRules,
    formType: currentFormType,
    isViewMode,
    isEditMode,
    isCopyMode,
    pageTitle,
    activityTypeList,
    loadInstanceDetail,
    saveInstance,
    goBack,
    fetchPlayData,
  }
}
