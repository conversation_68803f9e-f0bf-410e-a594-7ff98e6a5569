// 玩法组件实例配置
export interface ActivityComponent {
  /** 主键ID */
  id?: number
  /** 活动名称 */
  name?: string
  /** 活动组件类型 */
  activityComponentType?: number
  /** 状态：1在线，2下线 */
  status?: number
  /** 组件自定义配置 */
  componentConfig?: string
  /** 创建人 */
  createBy?: string
  /** 创建人email */
  creatorEmail?: string
  /** 更新人 */
  updateBy?: string
  /** 更新人email */
  updatorEmail?: string
  /** 创建时间 */
  createAt?: number
  /** 更新时间 */
  updateAt?: number
}

// 表单值类型
export interface ComponentInstanceFormValue {
  name: string
  activityComponentType: number
  componentConfig: string
  status?: number
}

// 列表查询参数
export interface ComponentInstanceListQuery {
  /** 分页查询-页数 */
  pageNum?: number
  /** 分页查询-数量 */
  pageSize?: number
  /** 按照任务活动名称模糊查询 */
  nameLike?: string
  /** 根据任务状态进行查询 */
  status?: number
  /** 按照创建人邮箱搜索 */
  creatorEmail?: string
  time?: {
    /** 开始时间 */
    start?: string | null
    /** 结束时间 */
    end?: string | null
  }
  /** 开始时间 */
  startTime?: number
  /** 结束时间 */
  endTime?: number
  /** 活动组件类型 */
  activityComponentType?: number
}
