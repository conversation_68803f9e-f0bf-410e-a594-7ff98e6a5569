import { defineComponent } from 'vue'
import InputForNoteOrTopic from '~/pages/ActivityCenter/components/InputForNoteOrTopic.vue'

export const typeList = [
  { label: '文本', value: 'string' },
  { label: '整数', value: 'int' },
  { label: '小数', value: 'float' },
  { label: '金额', value: 'finance' },
  { label: '是否', value: 'bool' },
  { label: '单选', value: 'radio' },
  { label: '多选', value: 'checkbox' },
  { label: '日期时间', value: 'dateTime' },
  { label: '时间选择', value: 'timePicker' },
  { label: '时间范围', value: 'list<timeRange>' },
  { label: '链接', value: 'link' },
  { label: '图片链接', value: 'imageLink' },
  { label: '图片上传', value: 'imageUpload' },
  { label: '文件上传', value: 'fileUpload' },
  { label: '协议条款', value: 'agreement' },
  { label: '结构', value: 'struct' },
  { label: '结构列表', value: 'list<struct>' },
  { label: '文本列表', value: 'list<string>' },
  { label: 'IF 判断', value: 'bool<struct>' },
  {
    label: '组件',
    children: [
      { label: '权益策略', value: 'component<PrizeRuleSelect>' },
      { label: '券列表', value: 'list<coupon>' },
      { label: '实验配置', value: 'component<Expirement>' },
      { label: '已有策略', value: 'component<StrategySelect>' },
      { label: '人群选择', value: 'component<CrowdSelect>' },
      { label: '条件规则', value: 'component<ConditionRuleSelect>' },
      { label: '标签输入框', value: 'component<TagInput>' },
      { label: '笔记/话题列表Id输入框', value: 'list<InputForNoteOrTopic>' },
      { label: '条件组件', value: 'component<Condition>' },
      { label: '玩法组件', value: 'component<PlayComponentSelect>' },
      // { label: '规格搜索', value: 'component<skuSearch>' },
      // { label: '笔记搜索', value: 'component<noteSearch>' },
      // { label: '店铺搜索', value: 'component<sellerSearch>' },
      // { label: '用户搜索', value: 'component<userSearch>' },
      // { label: '薯券搜索', value: 'component<couponSearch>' },
      // { label: '直播预告', value: 'component<liveTrailer>' },
      // { label: '资源位', value: 'component<resource>' },
      { label: '其余组件开发中…', value: 'other', disabled: true },
    ],
  },
  // { label: '只读型业务组件', value: 'readonlyBizComponent', disabled: true },
]

export const booleanList = [
  { label: '是', value: 1 },
  { label: '否', value: 0 },
]

export const jsonTreeConfigArray = [
  {
    title: '字段标识',
    type: 'Input',
    keyName: 'key',
    required: true,
    placeholder: '例：noteId',
    // hidden: true, // 是否隐藏该列（原则上以下所有字段都适用，但是某些重要字段不应该隐藏，需根据业务需要自行判断）
  },
  {
    title: '字段名称',
    type: 'Input',
    keyName: 'name',
    required: true,
    placeholder: '例：笔记ID',
  },
  {
    title: '字段类型',
    type: 'Cascader',
    keyName: 'type',
    options: typeList,
    required: true,
    placeholder: '例：文本',
  },
  {
    title: '字段配置',
    type: 'Button',
    keyName: 'config',
    required: false,
    flexibleLength: true,
  },
  {
    title: '备注说明',
    type: 'Input',
    keyName: 'remark',
    required: false,
  },
  {
    title: '必填',
    type: 'Switch',
    keyName: 'isRequired',
    options: booleanList,
    required: false,
    flexibleLength: true,
  },
]

export const jsonTreeQuickFields = [
  {
    title: '基础字段',
    list: [
      {
        key: 'baseFieldString',
        name: '文本',
        type: 'string',
        remark: '',
      },
      {
        key: 'baseFieldInt',
        name: '整数',
        type: 'int',
        remark: '',
      },
      {
        key: 'baseFieldFloat',
        name: '小数',
        type: 'float',
        remark: '',
      },
      {
        key: 'baseFieldFinance',
        name: '金额',
        type: 'finance',
        remark: '',
      },
      {
        key: 'baseFieldBool',
        name: '是否',
        type: 'bool',
        remark: '',
      },
      {
        key: 'baseFieldRadio',
        name: '单选',
        type: 'radio',
        remark: '',
      },
      {
        key: 'baseFieldCheckbox',
        name: '多选',
        type: 'checkbox',
        remark: '',
      },
      {
        key: 'baseFieldDate',
        name: '日期时间',
        type: 'dateTime',
        remark: '',
      },
      {
        key: 'baseFieldTimeRange',
        name: '时间范围',
        type: 'list<timeRange>',
        remark: '',
      },
      {
        key: 'baseFieldTimePicker',
        name: '时间选择',
        type: 'timePicker',
        remark: '',
      },
      {
        key: 'baseFieldLink',
        name: '链接',
        type: 'link',
        remark: '',
      },
      {
        key: 'baseFieldImageLink',
        name: '图片链接',
        type: 'imageLink',
        remark: '',
      },
      {
        key: 'baseFieldImageUpload',
        name: '图片上传',
        type: 'imageUpload',
        remark: '',
      },
      {
        key: 'baseFieldFileUpload',
        name: '文件上传',
        type: 'fileUpload',
        remark: '',
      },
      {
        key: 'baseFieldAgreement',
        name: '协议条款',
        type: 'agreement',
        remark: '',
      },
      {
        key: 'baseFieldStruct',
        name: '结构',
        type: 'struct',
        remark: '',
      },
      {
        key: 'baseFieldListStruct',
        name: '结构列表',
        type: 'list<struct>',
        remark: '',
      },
      {
        key: 'baseFieldListString',
        name: '文本列表',
        type: 'list<string>',
        remark: '',
      },
      {
        key: 'baseIfStruct',
        name: 'IF 判断',
        type: 'bool<struct>',
        remark: '',
      },
    ],
  },
  {
    title: '业务字段',
    list: [
      {
        key: 'prizeRuleId',
        name: '权益策略',
        type: 'component<PrizeRuleSelect>',
        remark: '',
        description: '权益策略ID',
        // forbidCustom: true, // 是否禁止自定义（针对"商品ID"这种高度定制的业务类型，设为true后, JsonTree中的字段类型会置灰、字段配置里的配置表单会隐藏，详见JsonTreeUnit.vue的isComponent方法内部）
        // defaultPrimaryKey: true, // 是否默认打开主键
        // defaultRequired: true, // 是否默认打开必填
        // disabled: true, // 是否禁止添加（招商侧在选中主体后，不允许再添加同类型的字段）
        // forbidDelete: true, // 是否禁止删除（招商侧在选中主体后，不允许删除该主体字段）
      },
      {
        key: 'couponId',
        name: '券列表',
        type: 'list<coupon>',
        remark: '',
        description: '券ID',
      },
      {
        key: 'expirement',
        name: '实验配置',
        type: 'component<Expirement>',
        remark: '',
        description: '实验配置',
      },
      {
        key: 'strategyIds',
        name: '已有策略',
        type: 'component<StrategySelect>',
        remark: '',
        description: '展示策略平台已勾选的策略同步到玩法',
      },
      {
        key: 'crowd',
        name: '人群选择',
        type: 'component<CrowdSelect>',
        remark: '',
        description: '选择人群',
      },
      {
        key: 'condictionRuleId',
        name: '条件规则',
        type: 'component<ConditionRuleSelect>',
        remark: '',
        description: '选择条件规则',
      },
      {
        key: 'tagInput',
        name: '标签输入框',
        type: 'component<TagInput>',
        remark: '',
        description: '标签输入框',
      },
      {
        key: 'topicId',
        name: '话题笔记Id列表输入框',
        type: 'list<InputForNoteOrTopic>',
        remark: '',
        description: '话题笔记Id输入框',
      },
      {
        key: 'condition',
        name: '条件组件',
        type: 'component<Condition>',
        remark: '',
        description: '条件组件',
      },
      {
        key: 'playComponentSelect',
        name: '玩法组件',
        type: 'component<PlayComponentSelect>',
        remark: '',
        description: '选择玩法组件类型',
      },
      // {
      //   key: 'skuId',
      //   name: '规格ID',
      //   type: 'component<skuSearch>',
      //   remark: '',
      //   description: 'SKU维度',
      // },
      // {
      //   key: 'noteId',
      //   name: '笔记ID',
      //   type: 'component<noteSearch>',
      //   remark: '',
      // },
      // {
      //   key: 'sellerId',
      //   name: '店铺ID',
      //   type: 'component<sellerSearch>',
      //   remark: '',
      //   description: '同商家ID',
      // },
      // {
      //   key: 'userId',
      //   name: '用户ID',
      //   type: 'component<userSearch>',
      //   remark: '',
      // },
      // {
      //   key: 'couponId',
      //   name: '薯券ID',
      //   type: 'component<couponSearch>',
      //   remark: '',
      // },
      // {
      //   key: 'liveTrailerId',
      //   name: '直播预告ID',
      //   type: 'component<liveTrailer>',
      //   remark: '',
      // },
      // {
      //   key: 'resourceId',
      //   name: '资源位ID',
      //   type: 'component<resource>',
      //   remark: '',
      // },
    ],
  },
  // {
  //   title: '自定义字段',
  //   list: [
  //     {
  //       key: 'customField',
  //       name: '自定义',
  //       type: 'string',
  //       remark: '',
  //     },
  //   ],
  // },
]

export const initConfig = () => ([{
  key: '',
  name: '',
  type: '',
  config: {},
  remark: '',
  isPrimaryKey: false,
  isRequired: false,
}])

// 默认的子级字段内容（设置字段类型为结构或列表结构时，需要自动给子级字段赋该默认值）
export const childrenField = {
  key: 'customField',
  name: '自定义',
  type: 'string',
  config: {
    isMultiLine: false, // 是否多行，默认false
    maxLength: undefined, // 最大输入长度，默认不限制
    prefix: undefined, // 前缀内容，默认无
    suffix: undefined, // 后缀内容，默认无
    placeholder: undefined, // 提示文本，默认“请输入”
  },
  remark: '',
  isPrimaryKey: false,
  isRequired: false,
}

// 初始选项（招商侧需要隐藏value，即hideValue为真，同时招商侧需要value是number型的）
export const initOptions = (hideValue: boolean = false) => ([
  { value: hideValue ? 1 : '1', label: '选项1' },
  { value: hideValue ? 2 : '2', label: '选项2' },
])

// 初始协议条款（招商侧需要隐藏value，即hideValue为真，同时招商侧需要value是number型的）
export const initAgreements = (hideValue: boolean = false) => ([
  {
    value: hideValue ? 1 : '1',
    label: '假一罚四',
    linkText: '“假一罚四”服务条款',
    link: 'https://agree.xiaohongshu.com/h5/terms/ZXXY20230201001/-1',
  },
])

export default defineComponent({
  components: { InputForNoteOrTopic },
})
