import dayjs from 'dayjs'
import { get, omit, pick } from 'lodash-es'

// 处理数据for后端
export const handleValueForEnd = (params: Record<string, any>) => {
  const basicConfigKeys = ['userPkgId', 'experimentConfig', 'sellerSkuConfig', 'enableGrayStart']
  const basicConfig = pick(params, basicConfigKeys)
  const baseInfo = omit(params, [...basicConfigKeys, 'time', 'playConfig', 'merchantLabel'])
  return {
    ...baseInfo,
    basicConfig,
    startTime: dayjs(get(params, 'time.start', '')).unix(),
    endTime: dayjs(get(params, 'time.end', '')).unix(),
    playConfig: JSON.stringify(get(params, 'playConfig', {})),
  }
}

// 处理数据for前端
export const handleValueForFront = (params: Record<string, any>) => {
  const basicData = {
    ...omit(params, ['id', 'status', 'updateAt', 'updateBy', 'startTime', 'endTime', 'basicConfig', 'playConfig', 'enableGrayStart']),
    ...get(params, 'basicConfig', {}),
    time: {
      start: dayjs.unix(get(params, 'startTime')).format('YYYY-MM-DD HH:mm:ss'),
      end: dayjs.unix(get(params, 'endTime')).format('YYYY-MM-DD HH:mm:ss'),
    },
  }
  const playData = JSON.parse(get(params, 'playConfig', '{}'))
  return { basicData, playData }
}

const hasCatetory = (categories, id) => {
  if (!categories || !id) {
    return false
  }
  for (let i = 0; i < categories.length; i++) {
    if (categories[i].id === id) {
      return categories[i].children
    }
  }
  return false
}

const findCategoryParent = (array, id) => array.find(item => item.id === id)

const pushCategoryObject = (root, value, level = 0) => {
  const id = value.shift()
  const parent = hasCatetory(root, id)
  if (parent) {
    pushCategoryObject(parent, value, level + 1)
  } else {
    root.push({
      id,
      level,
      children: [],
    })
    if (value && value.length > 0) {
      const newRoot = findCategoryParent(root, id)
      pushCategoryObject(newRoot.children, value, level + 1)
    }
  }
}

export const convertCategoryFromString = (categoryStringArray: string[]) => {
  const res = []
  categoryStringArray.forEach(categoryString => {
    if (!categoryString) {
      return
    }
    pushCategoryObject(res, categoryString.split('-'))
  })
  return res
}

export const convertCategoryToString = (tree, prefix = '') => {
  let result = []
  for (const node of tree) {
    const newPrefix = prefix + node.id
    // Cascader 需要每一级的信息，所以全部推入
    result.push(newPrefix)
    if (node.children) {
      result = result.concat(convertCategoryToString(node.children, `${newPrefix}-`))
    }
  }
  return result
}
