<template>
  <HeaderBreadcrumb :items="breadcrumbList" />
  <Spinner :spinning="loading">
    <Panel
      title="活动基础信息"
      style="flex-direction: column; gap: 10px; padding: 20px 40px 40px 40px"
    >
      <Form
        ref="formRef"
        :model="formValue"
        :disabled="formIsView"
      >
        <FormItem
          label="活动名称"
          name="name"
          :rules="[{ required: true, message: '请输入活动名称！' }]"
        >
          <Input
            v-model="formValue.name"
            placeholder="请输入活动名称"
            :max-length="30"
          />
        </FormItem>
        <FormItem
          label="玩法类型"
          name="activityType"
          :rules="[{ required: true, message: '请选择玩法类型！' }]"
        >
          <Select
            v-model="formValue.activityType"
            placeholder="请选择玩法类型"
            :options="playOptions"
            filterable
          />
        </FormItem>
        <FormItem
          label="活动标签"
          name="label"
          :rules="[{ required: true, message: '请选择玩法类型！' }]"
        >
          <Select
            v-model="formValue.label"
            placeholder="请选择活动标签"
            :options="ACTIVITY_LABEL_OPTIONS"
            filterable
            :disabled="formIsEdit"
          />
        </FormItem>
        <FormItem
          label="起止时间"
          name="time"
          :rules="[
            { required: true, message: '请选择起止时间！' },
            { validator: validateDateRanger },
          ]"
        >
          <DateRangePicker
            v-model="formValue.time"
            unit="second"
            clearable
          />
        </FormItem>
        <FormItem
          label="人群配置"
          name="userPkgId"
        >
          <CrowdSelect
            :value="formValue.userPkgId"
            :style="{ marginLeft: '11px' }"
            @change="crowdChange"
          />
        </FormItem>
        <FormItem
          label="实验配置"
          name="experimentConfig"
          help="条件项过多时可以左右滑动"
          :rules="[{ validator: validateExperimentMapForm, message: '任何一项条件项不可为空' }]"
        >
          <div style="max-width: 1000px">
            <TestParamsInputV2
              :value="formValue.experimentConfig"
              :disabled="formIsView"
              @change="
                (e) => {
                  formValue.experimentConfig = e
                }
              "
            />
          </div>
        </FormItem>
        <FormItem
          label="行业配置"
          name="category"
        >
          <CategorySelect
            :style="{ marginLeft: '5px' }"
            :model-value="formValue.categories"
            @update:model-value="changeCategory"
          />
        </FormItem>
        <FormItem
          label="商家标签"
          name="merchant"
        >
          <CheckboxGroup
            v-model="formValue.merchantLabel"
            :style="{ marginLeft: '11px' }"
            @change="changeMerchatLabel"
          >
            <Checkbox
              value="RedLabel"
              label="RedLabel"
            />
            <Checkbox
              value="REDeco"
              label="REDeco"
            />
            <Checkbox
              value="RedSport"
              label="运动心智商家"
            />
          </CheckboxGroup>
        </FormItem>
        <FormItem
          label="创建/ 编辑活动时开启灰度发布"
          name="gray"
        >
          <Switch v-model="formValue.enableGrayStart" />
        </FormItem>
      </Form>
    </Panel>
    <Panel
      v-if="playFormSchema"
      :title="playTitle"
      style="flex-direction: column; gap: 10px; padding: 20px 40px 16px 40px"
    >
      <DelightForm
        ref="playFormRef"
        :config="playFormSchema"
        :components="playFormComponents"
      />
    </Panel>
    <Panel
      v-if="grayScaleRecordList.length > 0"
      title="灰度发布记录"
      style="flex-direction: column; gap: 10px; padding: 20px 40px 16px 40px"
    >
      <div
        v-for="item in grayScaleRecordList"
        :key="item.id"
        style="margin-bottom: 10px"
      >
        <Text
          color="text-paragraph"
          style="margin-right: 40px"
        >{{ timestampToString(item.grayStartTimeInSecond) }}
        </Text>
        <Text color="text-paragraph">{{
          `${item.stage?.ratio}%灰度放量${EXECUTION_STATUS_NAME[item.stage?.grayStatus]}`
        }}</Text>
      </div>
    </Panel>
    <ButtonGroup :buttons="buttons" />
  </Spinner>
</template>

<script setup lang="tsx">
  import {
    Checkbox,
    CheckboxGroup,
    DateRangePicker,
    Form2 as Form,
    FormItem2 as FormItem,
    Input,
    Select,
    Spinner,
    Switch,
    Text,
    toast,
  } from '@xhs/delight'
  import { DelightForm } from '@xhs/use-formily-delight'
  import {
    computed, nextTick, onMounted, ref, watch, withDefaults,
  } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import ButtonGroup from '~/components/ButtonGroup/Index.vue'
  import HeaderBreadcrumb from '~/components/HeaderBreadcrumb/Index.vue'
  import Panel from '~/components/Panel/Index.vue'
  import { FORM_TYPE, FORM_TYPE_TEXT } from '~/hooks/useFormilyType'
  import { isExperimentValid } from '~/pages/IntelligentMarketing/utils/experiment'
  import {
    createPlayActivity,
    getGrayScaleRecord,
    getPlayActivityDetail,
    getPlayList,
    updatePLayActivity,
  } from '~/services/activityCenter'
  import { timestampToString } from '~/utils/time'
  import TestParamsInputV2 from '../../IntelligentMarketing/Group/Nodes/Experiment/TestParamsInputV2/Index.vue'
  import {
    validateExperimentMapForm,
  } from '../../IntelligentMarketing/Group/Nodes/Experiment/TestParamsInputV2/utils'
  import CrowdSelect from '../components/CrowdSelect/index.vue'
  import { ACTIVITY_LABEL_OPTIONS } from '../constants/acitivity'
  import { handleError } from '../utils/handleError'
  import CategorySelect from './CategorySelect.vue'
  import { EXECUTION_STATUS_NAME } from './common'
  import {
    convertCategoryFromString,
    convertCategoryToString,
    handleValueForEnd,
    handleValueForFront,
  } from './handler'
  import { resolveSchemaFromConfig, usePlayFormComponents } from './resolver'

  const playFormComponents = usePlayFormComponents()

  const props = withDefaults(
    defineProps<{
      formType: FORM_TYPE
    }>(),
    {},
  )

  const formRef = ref()
  const formValue = ref({
    name: '',
    label: '',
    activityType: null,
    time: {
      start: '',
      end: '',
    },
    userPkgId: null,
    experimentConfig: {
      subExperimentRelation: 'AND',
      subExperimentConfigs: [],
    },
    sellerSkuConfig: {},
    merchantLabel: [],
    categories: [],
    enableGrayStart: true,
  })

  const playTitle = ref('')
  const playFormRef = ref()
  const playFormSchema = ref(null)
  const loading = ref(false)
  const playOptions = ref([])
  const playList = ref(null)
  const router = useRouter()
  const route = useRoute()
  const formTypeText = computed(() => FORM_TYPE_TEXT[props.formType])
  const formIsView = computed(() => props.formType === FORM_TYPE.VIEW)
  const formIsEdit = computed(() => props.formType === FORM_TYPE.EDIT)
  const formIsCopy = computed(() => props.formType === FORM_TYPE.COPY)

  const { activityId } = route.params

  const grayScaleRecordList = ref([])

  const breadcrumbList = ref([
    { title: '活动列表', to: { name: 'PlayActivityList' } },
    { title: `${formTypeText.value}活动` },
  ])

  const crowdChange = (val: number) => {
    formValue.value.userPkgId = val
  }

  const validateDateRanger = (rule, value, callback) => {
    const { start = '', end = '' } = value
    if (!start && !end) {
      callback(new Error('请选择起止时间！'))
    }
    if (!start) {
      callback(new Error('请选择开始时间！'))
    }
    if (!end) {
      callback(new Error('请选择终止时间！'))
    }
    callback()
  }

  const changeMerchatLabel = value => {
    (formValue.value.sellerSkuConfig as { label: string[] }).label = value
  }

  const changeCategory = value => {
    const categories = convertCategoryFromString(value)
    formValue.value.sellerSkuConfig = {
      ...formValue.value.sellerSkuConfig,
      categories,
    }
  }
  // 点击取消
  const clickCancel = () => {
    router.go(-1)
  }

  // 提交
  const submit = async () => {
    try {
      loading.value = true
      const params = handleValueForEnd({
        ...formValue.value,
        playConfig: playFormRef.value.form.values,
      })
      if (formIsEdit.value) {
        await updatePLayActivity({ activity: params }, { activityId })
      } else {
        await createPlayActivity({ activity: params })
      }
      toast.success({ description: '保存成功', closeable: true })
      router.go(-1)
    } catch (err: any) {
      handleError(err, { title: '保存失败' })
    } finally {
      loading.value = false
    }
  }

  // 点击保存，校验表单
  const handleSubmit = async () => {
    if (loading.value || formIsView.value) {
      return
    }
    try {
      // Form validation
      try {
        await formRef.value.validate()
        await playFormRef.value.form.validate()
      } catch (err) {
        toast.danger({
          description: '表单校验失败',
          closeable: true,
        })
        return
      }

      // 实验配置校验
      const validRes = await isExperimentValid({
        experimentConfigsV2: {
          subExperimentRelation: formValue.value.experimentConfig.subExperimentRelation,
          subExperimentConfigV2List: formValue.value.experimentConfig.subExperimentConfigs,
        },
      })
      if (!validRes) {
        return
      }

      // Submit if validation passes or user confirms
      await submit()
    } catch (err) {
      toast.danger({
        description: err.message || err.msg || '操作失败',
        closeable: true,
      })
    }
  }

  interface IButton {
    label: string
    type?: string
    onClick: () => void
  }

  const buttons = ref<IButton[]>([
    {
      label: formIsView.value ? '返回' : '取消',
      onClick: () => clickCancel(),
    },
  ])

  if (!formIsView.value) {
    buttons.value.push({
      label: `保存${formTypeText.value}`,
      type: 'primary',
      onClick: () => handleSubmit(),
    })
  }

  // 获取活动数据
  const fetchActivityData = async () => {
    loading.value = true
    const res = await getPlayActivityDetail({ activityId })
    const { activity = {} } = res || {}
    const { basicData, playData } = handleValueForFront(activity)
    const { sellerSkuConfig = {} } = basicData
    const { categories = [], label = [] } = sellerSkuConfig
    formValue.value.categories = convertCategoryToString(categories)
    formValue.value.merchantLabel = label
    if (!basicData.enableGrayStart) {
      formValue.value.enableGrayStart = false
    }
    Object.assign(formValue.value, basicData)
    nextTick(() => {
      playFormRef.value.form.setValues(playData)
    })
  }

  // 获取玩法列表
  const fetchPlayData = async () => {
    const res = await getPlayList({
      pageNum: 1,
      pageSize: 99999,
      allowScenes: 'null',
    })
    const { activityTypes } = res
    playOptions.value = activityTypes.map((play: any) => ({
      value: play.activityType,
      label: play.name,
    }))
    playList.value = Object.fromEntries(activityTypes.map((play: any) => [play.activityType, play]))
  }

  // 获取表shcema
  const getFormSchema = (activityType: number) => {
    playTitle.value = ''
    playFormSchema.value = {}
    const { name = '', template = {} } = playList.value[activityType]
    const configJson = template.value || {}
    const config = JSON.parse(configJson)
    const schema = resolveSchemaFromConfig(config)
    playTitle.value = `${name} - 玩法配置`
    playFormSchema.value = schema
  }

  const init = async () => {
    try {
      await fetchPlayData()
      // 拉取玩法信息后才拉取数据，否则form setValue会出错
      if (formIsView.value || formIsEdit.value || formIsCopy.value) {
        await fetchActivityData()
      }
      if (formIsView.value) {
        playFormRef.value.form.disabled = true
      }
    } catch (err) {
      toast.danger({ description: `表单信息初始化失败 ${err?.message || ''}`, closeable: true })
    } finally {
      loading.value = false
    }
  }

  const getGrayScaleRecordList = async () => {
    const res = await getGrayScaleRecord({ activityId })
    grayScaleRecordList.value = res?.grayRecord?.grayStageRecords || []
  }

  onMounted(() => {
    init()
    if (formIsEdit.value || formIsView.value) {
      getGrayScaleRecordList()
    }
  })

  watch(() => formValue.value.activityType, getFormSchema)
</script>

<style>
.d-datepicker-dates {
  .d-datepicker-row {
    display: flex;
  }
}
</style>
