import { ArrayItems, DelightForm, TimePicker } from '@xhs/delight-formily'
import type { DSL } from '@xhs/use-formily-core'
import { hasOwn, resolveFormilySchemaFromDSL } from '@xhs/use-formily-core'
import { useDelightFormilyComponents } from '@xhs/use-formily-delight'
import { get, set } from 'lodash'
import FDateRangePicker from '~/components/DatePicker/FDateRangePicker'
import CouponSelect from '../../IntelligentMarketing/EquityPool/components/CouponSelect.vue'
import TestParamsInputV2 from '../../IntelligentMarketing/Group/Nodes/Experiment/TestParamsInputV2/Index.vue'
import Condition from '../components/Condition/Index.vue'
import ConditionRuleSelect from '../components/ConditionRuleSelect.vue'
import CrowdSelect from '../components/CrowdSelect/index.vue'
import InputForNoteOrTopic from '../components/InputForNoteOrTopic.vue'
import PlayComponentSelect from '../components/PlayComponentSelect/index.vue'
import PrizeRuleSelect from '../components/PrizeRuleSelect/index.vue'
import StrategySelect from '../components/StrategySelect.vue'
import TagInput from '../components/TagInput/Index.vue'

export const usePlayFormComponents = () => ({
  ...useDelightFormilyComponents(),
  DelightForm,
  PrizeRuleSelect,
  ArrayItems,
  CouponSelect,
  TestParamsInputV2,
  CrowdSelect,
  StrategySelect,
  ConditionRuleSelect,
  FDateRangePicker,
  TimePicker,
  TagInput,
  InputForNoteOrTopic,
  Condition,
  PlayComponentSelect,
})

const resolveReactions = (dsl): Record<string, any> | undefined => {
  const { useFieldLinkage, xReaction } = dsl.config || {}
  if (!useFieldLinkage || !xReaction) return undefined

  if (typeof xReaction === 'object') {
    const { dependencies: deps, ...rest } = xReaction
    if (Array.isArray(deps)) {
      return {
        dependencies: deps.map(dep => (typeof dep === 'string' ? `.${dep}` : dep)),
        ...rest,
      }
    }
  }

  return undefined
}

const SchemaMap = {
  radio: (dsl, { formLayout }) => {
    const map = {
      form: {
        'x-decorator': 'FormItem',
        'x-decorator-props': {
          label: dsl.name,
          tooltip: dsl.remark || undefined,
          asterisk: !!(dsl.name && dsl.isRequired),
        },
        'x-component': 'Select',
        'x-component-props': {
          options: get(dsl, 'config.options', []),
        },
        'x-validator': {
          requried: !!(dsl.isRequired),
        },
        'x-reactions': resolveReactions(dsl),
      },
    }
    return map[formLayout]
  },
  'list<coupon>': (dsl, { formLayout }) => {
    const map = {
      form: {
        type: 'array',
        'x-decorator': 'FormItem',
        'x-component': 'ArrayItems',
        'x-decorator-props': {
          label: dsl.name,
          required: true,
        },
        'x-validator': {
          requried: true,
        },
        items: {
          type: 'void',
          'x-decorator': 'FormItem',
          'x-component': 'Space',
          'x-component-props': {
            style: {
              marginBottom: '-30px',
            },
          },
          properties: {
            void: {
              type: 'void',
              properties: {
                sort: {
                  type: 'void',
                  'x-decorator': 'FormItem',
                  'x-decorator-props': {
                    style: {
                      width: 'auto',
                    },
                  },
                  'x-component': 'ArrayItems.SortHandle',
                },
                couponId: {
                  type: 'string',
                  required: true,
                  'x-decorator': 'FormItem',
                  'x-component': 'CouponSelect',
                  'x-decorator-props': {
                    style: {
                      width: '500px',
                    },
                    required: true,
                  },
                  'x-validator': {
                    requried: true,
                  },
                  'x-component-props': {
                    name: dsl.name,
                  },
                },
                remove: {
                  type: 'void',
                  'x-decorator': 'FormItem',
                  'x-component': 'ArrayItems.Remove',
                  'x-decorator-props': {
                    style: {
                      width: 'auto',
                    },
                  },
                },
              },
            },
          },
        },
        properties: {
          add: {
            type: 'void',
            title: `添加${dsl.name}`,
            'x-component': 'ArrayItems.Addition',
          },
        },
        'x-reactions': resolveReactions(dsl),
      },
    }
    return map[formLayout]
  },
  'list<timeRange>': (dsl, { formLayout }) => {
    const map = {
      form: {
        type: 'array',
        'x-decorator': 'FormItem',
        'x-component': 'ArrayItems',
        'x-decorator-props': {
          label: dsl.name,
          required: true,
        },
        'x-validator': {
          requried: true,
        },
        items: {
          type: 'void',
          'x-decorator': 'FormItem',
          'x-component': 'Space',
          'x-component-props': {
            style: {
              marginBottom: '-30px',
            },
          },
          properties: {
            items: {
              type: 'object',
              properties: {
                sort: {
                  type: 'void',
                  'x-decorator': 'FormItem',
                  'x-decorator-props': {
                    style: {
                      width: 'auto',
                    },
                  },
                  'x-component': 'ArrayItems.SortHandle',
                },
                timeRange: {
                  type: 'object',
                  required: true,
                  'x-decorator': 'FormItem',
                  'x-component': 'FDateRangePicker',
                  'x-decorator-props': {
                    style: {
                      width: '450px',
                    },
                    required: true,
                  },
                  'x-validator': {
                    requried: true,
                  },
                  'x-component-props': {
                    name: dsl.name,
                    isRange: true,
                    unit: 'second',
                    placeholder: {
                      start: '请选择开始时间',
                      end: '请选择结束时间',
                    },
                    transform: true,
                    style: {
                      width: '400px',
                    },
                  },
                },
                remove: {
                  type: 'void',
                  'x-decorator': 'FormItem',
                  'x-component': 'ArrayItems.Remove',
                  'x-decorator-props': {
                    style: {
                      width: 'auto',
                    },
                  },
                },
              },
            },
          },
        },
        properties: {
          add: {
            type: 'void',
            title: `添加${dsl.name}`,
            'x-component': 'ArrayItems.Addition',
          },
        },
        'x-reactions': resolveReactions(dsl),
      },
    }
    return map[formLayout]
  },
  'list<struct>': (dsl, { formLayout }, resolver) => {
    const childrenConfig = dsl.children || []
    const childrenSchema = resolver(childrenConfig)
    const map = {
      form: {
        type: 'array',
        'x-component': 'ArrayItems',
        'x-decorator-props': {
          label: dsl.name,
        },
        items: {
          type: 'object',
          properties: {
            void: {
              type: 'void',
              'x-component': 'Space',
              properties: {
                sort: {
                  type: 'void',
                  'x-decorator': 'FormItem',
                  'x-decorator-props': {
                    style: {
                      width: 'auto',
                    },
                  },
                  'x-component': 'ArrayItems.SortHandle',
                },
                index: {
                  type: 'void',
                  'x-decorator': 'FormItem',
                  'x-decorator-props': {
                    label: dsl.name,
                    labelStyle: {
                      paddingRight: '5px',
                    },
                    style: {
                      display: 'flex',
                      alignItems: 'center',
                    },
                  },
                  'x-component': 'ArrayItems.Index',
                  'x-component-props': {
                    dot: false,
                    color: 'var(--color-text-title)',
                  },
                },
                remove: {
                  type: 'void',
                  'x-decorator': 'FormItem',
                  'x-component': 'ArrayItems.Remove',
                  'x-decorator-props': {
                    style: {
                      width: 'auto',
                    },
                  },
                },
              },
            },
            object: {
              type: 'void',
              properties: get(childrenSchema, 'properties.object.properties'),
            },
          },
        },
        properties: {
          add: {
            type: 'void',
            title: `添加${dsl.name}`,
            'x-component': 'ArrayItems.Addition',
          },
        },
        'x-reactions': resolveReactions(dsl),
      },
    }
    return map[formLayout]
  },
  'bool<struct>': (dsl, { formLayout }, resolver) => {
    const childrenConfig = dsl.children || []
    const childrenSchema = resolver(childrenConfig)
    const map = {
      form: {
        type: 'object',
        'x-decorator': 'FormItem',
        'x-decorator-props': {
          label: dsl.name,
        },
        properties: {
          checkbox: {
            type: 'boolean',
            'x-decorator': 'FormItem',
            'x-component': 'Checkbox',
            'x-component-props': {
              ignore: true,
            },
            default: false,
          },
          object: {
            type: 'void',
            properties: get(childrenSchema, 'properties.object.properties'),
            'x-reactions': {
              dependencies: ['.checkbox'],
              fulfill: {
                state: {
                  visible: '{{$deps[0] === true}}',
                },
              },
            },
          },
        },
        'x-reactions': resolveReactions(dsl),
      },
    }
    return map[formLayout]
  },
  struct: (dsl, { formLayout }, resolver) => {
    const childrenConfig = dsl.children || []
    const childrenSchema = resolver(childrenConfig)
    const map = {
      form: {
        type: 'object',
        'x-decorator': 'FormItem',
        'x-decorator-props': {
          label: dsl.name,
        },
        properties: get(childrenSchema, 'properties.object.properties'),
        'x-reactions': resolveReactions(dsl),
      },
    }
    return map[formLayout]
  },
  'list<string>': (dsl, { formLayout }, resolver) => {
    const childDsl = { ...dsl, type: 'string' }
    const childShema = resolver([childDsl])
    const xDecoratorProps = get(childShema, `properties.object.properties.${dsl.key}.x-decorator-props`, {})
    const xComponentProps = get(childShema, `properties.object.properties.${dsl.key}.x-component-props`, {})
    const xValidator = get(childShema, `properties.object.properties.${dsl.key}.x-validator`, {})
    const map = {
      form: {
        type: 'array',
        'x-component': 'ArrayItems',
        'x-decorator': 'FormItem',
        'x-decorator-props': xDecoratorProps,
        items: {
          type: 'void',
          'x-component': 'Space',
          properties: {
            sort: {
              type: 'void',
              'x-decorator': 'FormItem',
              'x-decorator-props': {
                style: {
                  width: 'auto',
                },
              },
              'x-component': 'ArrayItems.SortHandle',
            },
            input: {
              type: 'string',
              'x-decorator': 'FormItem',
              'x-component': 'Input',
              'x-component-props': xComponentProps,
              'x-validator': xValidator,
            },
            remove: {
              type: 'void',
              'x-decorator': 'FormItem',
              'x-component': 'ArrayItems.Remove',
              'x-decorator-props': {
                style: {
                  width: 'auto',
                },
              },
            },
          },
        },
        properties: {
          add: {
            type: 'void',
            title: `添加${dsl.name}`,
            'x-component': 'ArrayItems.Addition',
          },
        },
        'x-reactions': resolveReactions(dsl),
      },
    }
    return map[formLayout]
  },
  'component<PrizeRuleSelect>': (dsl, { formLayout }) => {
    const map = {
      form: {
        type: 'number',
        'x-decorator': 'FormItem',
        'x-decorator-props': {
          label: dsl.name,
          tooltip: dsl.remark || undefined,
          asterisk: !!(dsl.name && dsl.isRequired),
        },
        'x-component': 'PrizeRuleSelect',
        'x-component-props': {
          name: dsl.name,
        },
        'x-validator': {
          requried: !!(dsl.isRequired),
        },
        'x-reactions': resolveReactions(dsl),
      },
    }
    return map[formLayout]
  },
  'component<Expirement>': (dsl, { formLayout }) => {
    const map = {
      form: {
        type: 'object',
        'x-decorator': 'FormItem',
        'x-decorator-props': {
          label: dsl.name,
          tooltip: dsl.remark || undefined,
          asterisk: !!(dsl.name && dsl.isRequired),
        },
        'x-component': 'TestParamsInputV2',
        'x-validator': {
          validateExperimentMap: true,
        },
        'x-component-props': {},
        default: {
          subExperimentRelation: 'AND',
          subExperimentConfigs: [
          ],
        },
        'x-reactions': resolveReactions(dsl),
      },
    }
    return map[formLayout]
  },
  'component<StrategySelect>': (dsl, { formLayout }) => {
    const map = {
      form: {
        type: 'array',
        'x-decorator': 'FormItem',
        'x-decorator-props': {
          label: dsl.name,
          tooltip: dsl.remark || undefined,
          asterisk: !!(dsl.name && dsl.isRequired),
          style: {
            width: '500px',
          },
        },
        'x-component': 'StrategySelect',
        'x-component-props': {
          source: get(dsl, 'config.source'),
        },
        'x-reactions': resolveReactions(dsl),
      },
    }
    return map[formLayout]
  },
  'component<ConditionRuleSelect>': (dsl, { formLayout }) => {
    const map = {
      form: {
        type: 'string',
        'x-decorator': 'FormItem',
        'x-decorator-props': {
          label: dsl.name,
          tooltip: dsl.remark || undefined,
          asterisk: !!(dsl.name && dsl.isRequired),
          style: {
            width: '500px',
          },
        },
        'x-component': 'ConditionRuleSelect',
        'x-component-props': {
          entityType: get(dsl, 'config.entityType'),
        },
        'x-reactions': resolveReactions(dsl),
      },
    }
    return map[formLayout]
  },
  'component<CrowdSelect>': (dsl, { formLayout }) => {
    const map = {
      form: {
        type: 'string',
        'x-decorator': 'FormItem',
        'x-decorator-props': {
          label: dsl.name,
          tooltip: dsl.remark || undefined,
          asterisk: !!(dsl.name && dsl.isRequired),
          style: {
            width: '500px',
          },
        },
        'x-component': 'CrowdSelect',
        'x-reactions': resolveReactions(dsl),
      },
    }
    return map[formLayout]
  },
  'component<Condition>': (dsl, { formLayout }) => {
    const map = {
      form: {
        type: 'object',
        'x-decorator': 'FormItem',
        'x-decorator-props': {
          label: dsl.name,
          tooltip: dsl.remark || undefined,
          asterisk: !!(dsl.name && dsl.isRequired),
        },
        'x-component': 'Condition',
        'x-component-props': {
          platForm: 'PLAY_CENTER',
        },
        'x-reactions': resolveReactions(dsl),
      },
    }
    return map[formLayout]
  },
  'component<PlayComponentSelect>': (dsl, { formLayout }) => {
    const map = {
      form: {
        type: 'string',
        'x-decorator': 'FormItem',
        'x-decorator-props': {
          label: dsl.name,
          tooltip: dsl.remark || undefined,
          asterisk: !!(dsl.name && dsl.isRequired),
          style: {
            width: '500px',
          },
        },
        'x-component': 'PlayComponentSelect',
        'x-component-props': {
          activityComponentType: get(dsl, 'config.activityComponentType'),
        },
        'x-validator': {
          requried: !!(dsl.isRequired),
        },
        'x-reactions': resolveReactions(dsl),
      },
    }
    return map[formLayout]
  },
  timePicker: (dsl, { formLayout }) => {
    const map = {
      form: {
        type: 'string',
        'x-decorator': 'FormItem',
        'x-decorator-props': {
          label: dsl.name,
          tooltip: dsl.remark || undefined,
          asterisk: !!(dsl.name && dsl.isRequired),
        },
        'x-component': 'TimePicker',
      },
    }
    return map[formLayout]
  },
  'component<TagInput>': (dsl, { formLayout }) => {
    const map = {
      form: {
        type: 'string',
        'x-decorator': 'FormItem',
        'x-decorator-props': {
          label: dsl.name,
          tooltip: dsl.remark || undefined,
          asterisk: !!(dsl.name && dsl.isRequired),
          extra: '输入@ 加上 空格 后开启标签选择',
        },
        'x-component': 'TagInput',
        'x-component-props': {
          placeholder: get(dsl, 'config.placeholder', '请输入标签'),
          maxLength: get(dsl, 'config.maxLength', undefined),
          maxCount: get(dsl, 'config.maxCount', undefined),
        },
        'x-reactions': resolveReactions(dsl),
      },
    }
    return map[formLayout]
  },
  'list<InputForNoteOrTopic>': (dsl, { formLayout }) => {
    const map = {
      form: {
        type: 'array',
        'x-component': 'ArrayItems',
        'x-decorator': 'FormItem',
        'x-decorator-props': {
          label: dsl.name,
          tooltip: dsl.remark || undefined,
          asterisk: !!(dsl.name && dsl.isRequired),
        },
        'x-validator': {
          requried: !!(dsl.isRequired),
        },
        items: {
          type: 'void',
          'x-component': 'Space',
          properties: {
            sort: {
              type: 'void',
              'x-decorator': 'FormItem',
              'x-decorator-props': {
                style: {
                  width: 'auto',
                },
              },
              'x-component': 'ArrayItems.SortHandle',
            },
            input: {
              type: 'string',
              'x-decorator': 'FormItem',
              'x-component': 'InputForNoteOrTopic',
              'x-decorator-props': {
              },
              'x-component-props': {
                placeholder: get(dsl, 'remark', '请输入'),
                type: get(dsl, 'config.contentType', 'note'),
                showTypeSelector: true,
              },
              'x-validator': {
                requried: !!(dsl.isRequired),
              },
            },
            remove: {
              type: 'void',
              'x-decorator': 'FormItem',
              'x-component': 'ArrayItems.Remove',
              'x-decorator-props': {
                style: {
                  width: 'auto',
                },
              },
            },
          },
        },
        properties: {
          add: {
            type: 'void',
            title: `添加${dsl.name}`,
            'x-component': 'ArrayItems.Addition',
          },
        },
        'x-reactions': resolveReactions(dsl),
      },
    }
    return map[formLayout]
  },
}

export const Resolver = (resolver: any) => (dsl, ctx) => {
  if (hasOwn(SchemaMap, dsl.type)) {
    return {
      key: dsl.key,
      schema: SchemaMap[dsl.type](dsl, ctx, resolver),
    }
  }
  return null
}

const formLayout = {
  labelAlign: 'left',
}

export const resolveSchemaFromConfig = (config: DSL[]) => {
  const { schema = {} } = resolveFormilySchemaFromDSL(config, {
    formLayout: 'form',
    resolvers: [Resolver(resolveSchemaFromConfig)],
  })

  if (get(schema, 'properties.object.x-decorator', '') === 'FormLayout') {
    const oldProps = get(schema, 'properties.object.x-decorator-props', {})
    set(schema, 'properties.object.x-decorator-props', {
      ...oldProps,
      ...formLayout,
    })
  }
  return schema
}
