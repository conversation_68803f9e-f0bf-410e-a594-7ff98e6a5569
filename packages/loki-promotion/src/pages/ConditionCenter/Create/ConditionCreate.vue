<template>
  <HeaderBreadcrumb :items="breadcrumbList" />
  <Spinner :spinning="loading">
    <Panel
      title="人群基础信息"
      style="flex-direction: column; gap: 10px; padding: 20px 40px 40px 40px"
    >
      <Form
        ref="formRef"
        :model="formValue"
        :disabled="formIsView"
        label-position="Left"
        label-width="200px"
      >
        <FormItem
          label="条件名称"
          name="name"
          :rules="[{ required: true, message: '请输入条件名称！' }]"
        >
          <Input
            v-model="formValue.name"
            placeholder="请输入条件名称"
            :max-length="30"
          />
        </FormItem>
        <FormItem
          label="可调用平台"
          name="platForms"
          :rules="[{ required: true, message: '请选择可调用平台' }]"
        >
          <Select
            v-model="formValue.platForms"
            placeholder="请选择"
            :options="CAN_USE_SCENE_OPTIONS"
            multiple
          />
        </FormItem>
      </Form>
    </Panel>
    <ButtonGroup :buttons="buttons" />
  </Spinner>
</template>

<script setup lang="tsx">
  import {
    Form2 as Form, FormItem2 as FormItem, Input, Select,
    Spinner, toast,
  } from '@xhs/delight'
  import {
    computed, onMounted,
    ref, withDefaults,
  } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import ButtonGroup from '~/components/ButtonGroup/Index.vue'
  import HeaderBreadcrumb from '~/components/HeaderBreadcrumb/Index.vue'
  import Panel from '~/components/Panel/Index.vue'
  import { FORM_TYPE, FORM_TYPE_TEXT } from '~/hooks/useFormilyType'
  import { postResourceUpsert } from '~/services/intelligentMarketing'
  import { handleError } from '../../ActivityCenter/utils/handleError'
  import { resourceType } from '../List/const'
  import { CAN_USE_SCENE_OPTIONS } from './const'

  const props = withDefaults(defineProps<{
    formType: FORM_TYPE
  }>(), {})

  const formRef = ref()
  const formValue = ref({
    name: '',
    platForms: [],
    status: '',
  })

  const loading = ref(false)
  const router = useRouter()
  const route = useRoute()
  const formTypeText = computed(() => FORM_TYPE_TEXT[props.formType])
  const formIsView = computed(() => props.formType === FORM_TYPE.VIEW)
  const formIsEdit = computed(() => props.formType === FORM_TYPE.EDIT)
  const formIsCopy = computed(() => props.formType === FORM_TYPE.COPY)

  const breadcrumbList = ref([
    { title: '条件中心', to: { name: 'ConditionCenter', query: { tab: 'conditionList' } } },
    { title: `${formTypeText.value}条件` },
  ])

  const { resourceRelationId } = route.params

  // 点击取消
  const clickCancel = () => {
    router.go(-1)
  }

  // 提交
  const submit = async () => {
    try {
      loading.value = true
      const params: any = {
        ...formValue.value,
        resourceType: resourceType.condition,
      }

      if (formIsEdit.value) {
        params.relationId = resourceRelationId
        await postResourceUpsert(params)
      } else {
        await postResourceUpsert(params)
      }
      toast.success({ description: '保存成功', closeable: true })
      router.go(-1)
    } catch (err: any) {
      handleError(err, { title: '保存失败' })
    } finally {
      loading.value = false
    }
  }

  // 点击保存，校验表单
  const handleSubmit = async () => {
    if (loading.value || formIsView.value) {
      return
    }
    await formRef.value.validate()
    await submit()
  }

  interface IButton {
    label: string
    type?: string
    onClick: () => void
  }

  const buttons = ref<IButton[]>([
    {
      label: formIsView.value ? '返回' : '取消',
      onClick: () => clickCancel(),
    },
  ])

  if (!formIsView.value) {
    buttons.value.push({
      label: `保存${formTypeText.value}`,
      type: 'primary',
      onClick: () => handleSubmit(),
    })
  }

  // 获取活动数据
  const fetchCrowdDetail = async () => {
    loading.value = true
    // const res = await getConditionComponentConfig({ id: resourceRelationId, platForm })
    // const condition = res?.data
    // Object.assign(formValue.value, {
    //   ...condition,
    // })
    formValue.value = {
      platForms: JSON.parse(route.query.platForms || '[]' as any),
      name: route.query.name,
      status: route.query.status,
    }

    // 当状态为online时，禁用已选平台
    if (formValue.value.status === 'ONLINE') {
      CAN_USE_SCENE_OPTIONS.forEach(option => {
        if (formValue.value.platForms.includes(option.value)) {
          option.disabled = true
        }
      })
    }
  }

  const init = async () => {
    try {
      if (formIsView.value || formIsEdit.value || formIsCopy.value) {
        await fetchCrowdDetail()
      }
    } catch (err) {
      toast.danger({ description: `表单信息初始化失败 ${err?.message || ''}`, closeable: true })
    } finally {
      loading.value = false
    }
  }

  onMounted(() => {
    init()
  })

</script>

<style>
.d-datepicker-dates {
  .d-datepicker-row {
    display: flex;
  }
}

.custom-option {
  display: flex;
  align-items: center;
  overflow: auto;
  border-radius: var(--size-radius-default);
  padding: var(--size-space-step-default);
  grid-gap: var(--size-space-step-default);
  gap: var(--size-space-step-default);
  cursor: pointer;
}
</style>
