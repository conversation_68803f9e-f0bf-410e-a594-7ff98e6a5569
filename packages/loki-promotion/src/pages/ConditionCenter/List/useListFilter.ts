import { ref } from 'vue'
import {
  Input,
  Select,
} from '@xhs/delight'
import type { IFilter } from '@xhs/delight-material-ultra-outline-filter'

export function useListFilter(fetchList: () => Promise<void>) {
  const filterParam = ref({
    // 搜索参数
    creatorName: undefined,
    page: undefined,
    pageSize: 10,
    total: 0,
  })

  const filterConfig: IFilter = {
    handleFilter: fetchList,
    filterItems: [
      {
        label: '条件名称',
        name: 'name',
        component: {
          is: Input,
          props: {
            placeholder: '请输入条件名称',
            clearable: true,
          },
        },
      },
      {
        label: '可用场景',
        name: 'scene',
        component: {
          is: Select,
          props: {
            placeholder: '请选择可用场景',
            options: [
            ],
          },
        },
      },
      {
        label: '状态',
        name: 'status',
        component: {
          is: Select,
          props: {
            placeholder: '请选择状态',
            options: [
            ],
          },
        },
      },
    ],
  }

  return {
    filterParam,
    filterConfig,
  }
}
