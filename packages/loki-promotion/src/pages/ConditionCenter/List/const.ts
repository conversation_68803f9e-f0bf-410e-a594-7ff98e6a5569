export const resourceType = {
  trigger: 'TRIGGER',
  container: 'CONTAINER',
  condition: 'CONDITION',
}

export const statusTextType = {
  ONLINE: '上线',
  OFFLINE: '下线',
}

export const statusTypeList = [
  {
    value: 'ONLINE',
    label: '上线',
    color: 'green',
  },
  {
    value: 'OFFLINE',
    label: '下线',
    color: 'orange',
  },
]

export const statusColorObj = {
  ONLINE: 'green',
  OFFLINE: 'orange',
}

export function convertObjectToString(obj) {
  return Object.fromEntries(
    Object.entries(obj).map(([key, value]) => [String(key), typeof value === 'object' && value !== null ? JSON.stringify(value) : String(value || '')]),
  )
}
