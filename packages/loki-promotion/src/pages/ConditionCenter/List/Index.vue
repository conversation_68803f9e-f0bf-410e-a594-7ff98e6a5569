<template>
  <div class="page-wrapper">
    <Tabs v-model="activeTab">
      <TabPane
        id="conditionList"
        label="条件管理"
      >
        <div class="item-wrapper">
          <ConditionList />
        </div>
      </TabPane>
      <TabPane
        id="conditionRuleList"
        label="条件规则管理"
      >
        <div class="item-wrapper">
          <ConditionRuleList />
        </div>
      </TabPane>
      <TabPane
        id="filterRuleList"
        label="筛选规则管理"
      >
        <div class="item-wrapper">
          <FilterRuleList />
        </div>
      </TabPane>
    </Tabs>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { Tabs, TabPane } from '@xhs/delight'
  import ConditionList from './ConditionList.vue'
  import ConditionRuleList from '~/pages/IntelligentMarketing/ConditionRuleConfig/index.vue'
  import FilterRuleList from '~/pages/IntelligentMarketing/FilterRuleConfig/index.vue'

  const route = useRoute()
  const router = useRouter()
  const activeTab = ref<string>(route.query.tab as string)

  watch(activeTab, newTab => {
    router.replace({
      query: { ...route.query, tab: newTab },
    })
  })
</script>

<style lang="stylus" scoped>
.page-wrapper {
  padding: 16px;
  background: #f5f5f5;
}

.item-wrapper {
  margin-top: 16px;
  background: #fff;
  padding: 16px;
  border-radius: 8px;
}

</style>
