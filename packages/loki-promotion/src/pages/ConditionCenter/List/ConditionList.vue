<template>
  <div class="filter-rule-page">
    <div class="search-bar">
      <Input
        v-model="queryParams.name"
        clearable
        placeholder="条件名称"
        @clear="loadData"
        @enter="loadData"
      />
      <Select
        v-model="queryParams.bizsceneId"
        clearable
        :options="sceneOptions"
        placeholder="可用场景"
      />
      <Select
        v-model="queryParams.status"
        :options="statusTypeList"
        clearable
        placeholder="请选择状态"
      />
      <Button
        type="secondary"
        @click="loadData"
      >搜索</Button>
    </div>
    <main>
      <Table
        :columns="tableColumns"
        :data-source="dataSource"
        :loading="isLoading"
      />
      <Pagination
        v-model="pageInfo.pageNo"
        style="margin-top: 20px;"
        :total="total"
        :page-size="pageInfo.pageSize"
        :page-size-options="[10, 20, 30]"
        @update:page-size="onUpdatePageSize"
        @update:current="onPageChange"
      />
    </main>
  </div>
</template>

<script lang="tsx" setup>
  import {
    ref, watch, h, computed,
  } from 'vue'
  import {
    Button, Table, Text, Space, toast, Pagination, Modal, Input, Select, Tag, Popover,
  } from '@xhs/delight'
  import { get } from 'lodash'
  import { ThContent } from '@xhs/delight/components/Table/interface'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'
  import {
    getResourceManagementList,
    postResourceUpsert,
  } from '~/services/intelligentMarketing'
  import {
    convertObjectToString,
    resourceType,
    statusColorObj,
    statusTextType,
    statusTypeList,
  } from './const'
  import { CAN_USE_SCENE_OPTIONS_MAP } from '../Create/const'

  const router = useRouter()

  const store = useStore()
  const sceneOptions = computed(() => store.state.intelligentMarketing.sceneOptions.map(v => ({ label: v.name, value: v.value })))
  const isLoading = ref(false)
  const dataSource = ref([]) // 当前展示的数据
  const total = ref(0) // 总条数
  const pageInfo = ref({
    pageNo: 1,
    pageSize: 10,
  })

  // 用于存储所有数据
  const allData = ref([])

  const queryParams = ref({
    name: '',
    bizsceneId: '',
    status: '',
  })

  // 根据当前的 pageInfo 来截取展示数据
  const handlePagination = () => {
    const start = (pageInfo.value.pageNo - 1) * pageInfo.value.pageSize
    const end = start + pageInfo.value.pageSize
    dataSource.value = allData.value.slice(start, end)
  }

  // 加载所有数据
  const loadData = async () => {
    isLoading.value = true
    try {
      const res: any = await getResourceManagementList({
        resourceType: resourceType.condition,
        queryParams: convertObjectToString(queryParams.value), // 不再传递分页信息，接口会一次性返回所有数据
        status: queryParams.value.status || '',
      })
      if (res?.code === 0) {
        allData.value = res?.data?.resources.map(v => ({
          ...v,
          ...JSON.parse(v.attrs),
        })) || []
        total.value = allData.value.length
        handlePagination() // 加载数据后开始分页
      } else {
        const errMsg = get(res, 'msg', '')
        throw new Error(errMsg)
      }
    } catch (err) {
      toast.danger({ description: `数据加载失败${err.message}` })
    } finally {
      isLoading.value = false
    }
  }

  // 用户更新 pageSize 时触发此函数
  const onUpdatePageSize = (newPageSize: number) => {
    pageInfo.value.pageSize = newPageSize || 10
    pageInfo.value.pageNo = 1 // 改变 pageSize 时重置页码
    handlePagination()
  }

  // 用户翻页时触发此函数
  const onPageChange = (newPageNo: number) => {
    pageInfo.value.pageNo = newPageNo || 1
    handlePagination()
  }

  // 监听分页和 allData 变化，一旦变化自动重新计算分页数据
  watch([pageInfo, allData], () => {
    handlePagination()
  }, { deep: true })

  // 编辑
  const edit = rowData => {
    router.push({ name: 'EditCondition', params: { resourceRelationId: rowData.resourceRelationId }, query: { platForms: JSON.stringify(rowData.platForms), name: rowData.name, status: rowData.status } })
  }

  // 下线筛选规则
  const offLine = rowData => {
    const offLineModal = Modal.warning({
      title: '提示',
      content: h(Text, {
        style: {
          whiteSpace: 'pre-wrap', textAlign: 'center', width: '100%', padding: '10px 0',
        },
      }, {
        default: () => `是否要${rowData.status === 'ONLINE' ? '下线' : '上线'}条件${rowData.resourceRelationId}`,
      }),
      async onConfirm(close) {
        try {
          offLineModal?.update({ loading: true })
          const res: any = await postResourceUpsert({
            resourceType: resourceType.condition,
            relationId: rowData.resourceRelationId,
            status: rowData.status === 'ONLINE' ? 'OFFLINE' : 'ONLINE',
          })
          if (res?.code === 0) {
            close?.()
            loadData()
          } else {
            throw new Error(get(res, 'msg', ''))
          }
        } catch (e) {
          toast.danger(`规则${rowData.status === 'ONLINE' ? '下线' : '上线'}失败${e.message}`)
        } finally {
          offLineModal?.update({ loading: false })
        }
      },
    })
  }

  const tableColumns = ref<ThContent[]>([
    {
      title: 'ID',
      dataIndex: 'resourceRelationId',
      align: 'center',
    },
    {
      title: '条件名称',
      dataIndex: 'name',
      align: 'center',
    },
    {
      title: '说明',
      dataIndex: 'help',
      align: 'center',
    },
    {
      title: '可用场景-可用触点',
      dataIndex: 'availableTriggers',
      align: 'center',
      render: ({ rowData }) => {
        const list = rowData.availableTriggers.split('\n').filter(v => v) || [] || []
        return (
        <Popover arrow={true}
                  v-slots={{
                    content: () => (
                      <div style="padding: var(--size-space-large); ">
                        {list.map(v => <Tag color="cyan" style="margin: 2px">{v}</Tag>)}
                      </div>
                    ),
                  }}>
          <Tag>{ list[0] }</Tag>
        </Popover>
      )
      },
    },
    {
      title: '可调用平台',
      dataIndex: 'platForms',
      align: 'center',
      render: ({ rowData }) => {
        const list = rowData.platForms || []
        return (
          list.map(v => <Tag color="blue" size="small" style="margin: 2px">{CAN_USE_SCENE_OPTIONS_MAP[v]}</Tag>)
        )
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      align: 'center',
      render: ({ rowData }) => (
      <Space>
        <Tag color={statusColorObj[rowData.status]}>{statusTextType[rowData.status]}</Tag>
      </Space>
    ),
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      render: ({ rowData }) => (
      <Space>
        <Text link onClick={() => offLine(rowData)}>{ rowData.status === 'ONLINE' ? '下线' : '上线' }</Text>
        <Text link onClick={() => edit(rowData)}>编辑</Text>
      </Space>
    ),
    },
  ])

  // 首次加载数据
  loadData()
</script>

<style lang="stylus">
.filter-rule-page {
  .search-bar {
    width: 70%;
    display: flex;
    gap: 14px;
  }

  main {
    margin-top: 20px;
  }

  .title {
    font-size 20px
    font-weight 500
    margin 40px 0 10px 0
  }
}
</style>
