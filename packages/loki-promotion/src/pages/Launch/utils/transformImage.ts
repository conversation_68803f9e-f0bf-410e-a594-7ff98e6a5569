import { imageUploader } from 'loki-shared/utils/upload'
import { getImageCoverBottomMainColor, getImageCoverTopMainColor } from './getBottomCoverColor'

const TOP_MASK_HEIGHT = 54 * 3
const BOTTOM_MASK_HEIGHT = 34 * 3

// 处理图片
function processImage(inputImage: string): Promise<HTMLCanvasElement | null> {
  return new Promise((resolve, reject) => {
    const image = document.createElement('img')
    image.crossOrigin = 'anonymous' // 处理跨域问题
    image.src = inputImage

    image.onload = () => {
      try {
        // 第一步：裁剪图片
        const cropRes = cropImageTo3by4(image)

        // 第二步：应用模糊和遮罩效果
        const finalCanvas = applyBlurAndMask(cropRes.canvas, cropRes.topColorArr, cropRes.bottomColorArr)

        resolve(finalCanvas)
      } catch (error) {
        reject(error)
      }
    }

    image.onerror = () => reject(new Error('图片加载失败'))
  })
}

// 裁剪图片成 3:4 的比例
function cropImageTo3by4(sourceImage: HTMLImageElement): {
  canvas: HTMLCanvasElement
  topColorArr: number[]
  bottomColorArr: number[]
} {
  const cropCanvas = document.createElement('canvas')
  const cropCtx = cropCanvas.getContext('2d')

  const targetRatio = 3 / 4
  const sourceRatio = sourceImage.width / sourceImage.height

  let sourceX = 0
  let sourceY = 0
  let sourceWidth = 0
  let sourceHeight = 0

  if (sourceRatio > targetRatio) {
    sourceHeight = sourceImage.height
    sourceWidth = sourceImage.height * targetRatio
    sourceX = (sourceImage.width - sourceWidth) / 2
    sourceY = 0
  } else {
    sourceWidth = sourceImage.width
    sourceHeight = sourceImage.width / targetRatio
    sourceX = 0
    sourceY = 0
  }

  const outputWidth = 300
  const outputHeight = 400

  cropCanvas.width = outputWidth
  cropCanvas.height = outputHeight

  cropCtx.drawImage(
    sourceImage,
    sourceX,
    sourceY,
    sourceWidth,
    sourceHeight,
    0,
    0,
    outputWidth,
    outputHeight,
  )

  const topColorArr = getImageCoverTopMainColor(sourceImage)
  const bottomColorArr = getImageCoverBottomMainColor(sourceImage)

  console.log('topColorArr:', topColorArr)
  console.log('bottomColorArr:', bottomColorArr)

  return {
    canvas: cropCanvas,
    topColorArr,
    bottomColorArr,
  }
}

// 应用模糊和遮罩效果
function applyBlurAndMask(sourceCanvas: HTMLCanvasElement, topColorArr: number[], bottomColorArr: number[]): HTMLCanvasElement {
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')

  canvas.width = sourceCanvas.width
  canvas.height = sourceCanvas.height

  // 绘制原图
  ctx.drawImage(sourceCanvas, 0, 0)

  // 应用渐变模糊效果
  const tempCanvas = document.createElement('canvas')
  const tempCtx = tempCanvas.getContext('2d')

  tempCanvas.width = canvas.width
  tempCanvas.height = canvas.height

  const blurList = [10, 6, 5, 3, 2, 1, 0.5, 0]

  // 1. 绘制顶部蒙层
  const itemHeight = TOP_MASK_HEIGHT / blurList.length

  for (let i = 0; i < blurList.length; i++) {
    tempCtx.clearRect(0, 0, tempCanvas.width, tempCanvas.height)
    tempCtx.filter = `blur(${blurList[i]}px)`
    tempCtx.drawImage(sourceCanvas, 0, 0)

    const yPos = itemHeight * i

    ctx.drawImage(
      tempCanvas,
      0,
      yPos,
      canvas.width,
      itemHeight,
      0,
      yPos,
      canvas.width,
      itemHeight,
    )
  }

  // 2. 绘制底部蒙层
  const bottomItemHeight = BOTTOM_MASK_HEIGHT / blurList.length
  const bottomStartY = canvas.height - BOTTOM_MASK_HEIGHT

  for (let i = 0; i < blurList.length; i++) {
    tempCtx.clearRect(0, 0, tempCanvas.width, tempCanvas.height)
    // 底部模糊从弱到强（与顶部相反）
    tempCtx.filter = `blur(${blurList[blurList.length - 1 - i]}px)`
    tempCtx.drawImage(sourceCanvas, 0, 0)

    const yPos = bottomStartY + bottomItemHeight * i

    ctx.drawImage(
      tempCanvas,
      0,
      yPos,
      canvas.width,
      bottomItemHeight,
      0,
      yPos,
      canvas.width,
      bottomItemHeight,
    )
  }

  // 绘制顶部渐变遮罩
  const topGradient = ctx.createLinearGradient(0, 0, 0, TOP_MASK_HEIGHT)
  topGradient.addColorStop(0, `rgba(${topColorArr[0]}, ${topColorArr[1]}, ${topColorArr[2]}, 1)`)
  topGradient.addColorStop(1, `rgba(${topColorArr[0]}, ${topColorArr[1]}, ${topColorArr[2]}, 0)`)

  ctx.fillStyle = topGradient
  ctx.fillRect(0, 0, canvas.width, TOP_MASK_HEIGHT)

  // 绘制底部渐变遮罩
  const bottomGradient = ctx.createLinearGradient(
    0,
    canvas.height - BOTTOM_MASK_HEIGHT,
    0,
    canvas.height,
  )
  bottomGradient.addColorStop(0, `rgba(${bottomColorArr[0]}, ${bottomColorArr[1]}, ${bottomColorArr[2]}, 0)`)
  bottomGradient.addColorStop(1, `rgba(${bottomColorArr[0]}, ${bottomColorArr[1]}, ${bottomColorArr[2]}, 1)`)

  ctx.fillStyle = bottomGradient
  ctx.fillRect(
    0,
    canvas.height - BOTTOM_MASK_HEIGHT,
    canvas.width,
    BOTTOM_MASK_HEIGHT,
  )

  return canvas
}

function canvasToBlob(canvas: HTMLCanvasElement, type = 'image/png', quality = 1.0) {
  return new Promise((resolve, reject) => {
    canvas.toBlob(blob => {
      if (blob) {
        resolve(blob)
      } else {
        reject(new Error('无法创建 Blob'))
      }
    }, type, quality)
  })
}

// 导出处理后的图片
export async function transformImage(url: string) {
  try {
    const resultCanvas: HTMLCanvasElement = await processImage(url)

    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    canvas.width = resultCanvas.width
    canvas.height = resultCanvas.height

    ctx.drawImage(resultCanvas, 0, 0)

    const blob: any = await canvasToBlob(canvas, 'image/png', 1)

    const file = new File([blob], 'transformed', { type: 'image/png' })

    const res = await imageUploader.post({
      Body: file,
    })

    return res.data?.url || ''
  } catch (err) {
    return ''
  }
}
