<template>
  <Space
    direction="vertical"
    align="start"
    :size="0"
  >
    <Space
      :size="5"
    >
      <Select
        v-model="newValue.dataType"
        :options="[
          { label: '商品', value: 'Goods' },
          { label: '笔记', value: 'Note' },
        ]"
        :disabled="disabled"
        style="width: 100px;"
        @change="handleDataTypeChange"
      />
      <Input
        :model-value="newValue?.itemId"
        :placeholder="`请输入${name}`"
        :disabled="disabled"
        clearable
        style="flex: none; width: 250px"
        @input="handleInput"
        @change="handleChange"
      />
      <div v-if="previewUrl">
        <img
          :src="previewUrl"
          style="width: 126px; height: 170px;"
        >
      </div>
      <table
        v-if="showTable"
        :border="1"
        :cellspacing="0"
        :cellpadding="5"
      >
        <tr>
          <th
            v-for="(th, i) in newValue.result"
            :key="i"
          >{{ th.label }}</th>
        </tr>
        <tr>
          <td
            v-for="(td, i) in newValue.result"
            :key="i"
          >
            <component :is="isComponent(td)" />
          </td>
        </tr>
      </table>
    </Space>
    <Spinner
      v-if="loading"
      tip="搜索中…"
      style="margin-top: 3px"
    />
    <InvalidMsg
      v-if="showError"
      msg="当前ID无效"
    />
  </Space>
</template>

<script setup lang="tsx">
  import {
    withDefaults, ref, computed, watch,
  } from 'vue'
  import { debounce } from 'lodash'
  import copyToClipboard from 'loki-shared/utils/copy'
  import {
    Input, Space, Text, Tooltip, toast, viewImgs, Tag, Spinner, Select,
  } from '@xhs/delight'
  import {
    searchDatasetItem,
    searchDatasetNote,
  } from '~/services/launch'
  import InvalidMsg from './InvalidMsg.vue'
  import { transformImage } from '../utils/transformImage'

  interface ISearch {
    itemId: string
    cover1: string
    type: 'component<itemSearchWithImageTransform>'
    dataType: string
    result?: any[]
  }

  const props = withDefaults(
    defineProps<{
      modelValue: ISearch
      name: string
      type: string
      disabled: boolean
      // eslint-disable-next-line @typescript-eslint/ban-types
      changeFn: Function
    }>(),
    {},
  )

  const loading = ref(false)
  const newValue = ref<ISearch>({
    itemId: '', cover1: '', type: 'component<itemSearchWithImageTransform>', result: [], dataType: 'Goods',
  })
  const previewUrl = ref('')

  // 展示点查结果表格
  const showTable = computed(() => {
    const { itemId, result } = newValue.value || {}
    return itemId && result?.length && !loading.value
  })
  // 展示点查出错提示（输入框值为空时不报错）
  const showError = computed(() => {
    const { itemId, result } = newValue.value || {}
    return itemId && !result?.length && !loading.value
  })

  watch(
    () => props.modelValue,
    async v => {
      const { result, cover1 } = v || {}
      const cover = result?.find((item: any) => item.type === 'image')?.value || ''

      newValue.value = {
        ...newValue.value,
        ...(v || {}),
      }

      // 如果已经生成过了则不处理
      if (cover1) {
        previewUrl.value = cover1
        return
      }

      if (cover) {
        // 处理图片
        const url = await transformImage(cover)
        previewUrl.value = url

        props.changeFn({
          ...(v || {}),
          itemId: v.itemId,
          cover1: url,
        })
      }
    },
    {
      immediate: true,
      deep: true,
    },
  )

  // 复制文本
  const copyText = (e, text) => {
    e.stopPropagation()
    if (`${text}`) {
      copyToClipboard(`${text}`)
      toast.success('复制成功')
    }
  }

  // 浏览大图
  const clickImage = (e, img) => {
    e.stopPropagation()
    if (!img) return
    viewImgs([img], {
      movable: true,
      zoomOnWheel: true,
      closeOnMask: true,
    })
  }

  const isComponent = (td: any) => {
    // 文本
    if (td.type === 'string') {
      return <Tooltip content={td.value}>
      <Text maxLines={3} ellipsis onClick={e => copyText(e, td.value)}>{td.value}</Text>
    </Tooltip>
    }
    // 图片
    if (td.type === 'image') {
      return <div class="image-wrapper" onClick={e => clickImage(e, td.value)}>
      {td.value ? <img src={td.value} /> : <Text class="no-data">暂无图片</Text>}
      <div class="image-mask" />
    </div>
    }
    // 到手价
    if (td.type === 'expectedPrice') {
      return <Text class="color-black" onClick={e => copyText(e, td.value)}>{td.value ? `￥${td.value}` : '-'}</Text>
    }
    // 原价
    if (td.type === 'minorPrice') {
      return <Text class="color-grey" onClick={e => copyText(e, td.value)}>{td.value ? `￥${td.value}` : '-'}</Text>
    }
    // 数字
    if (td.type === 'number') {
      return <Text class="color-green" onClick={e => copyText(e, td.value)}>{td.value}</Text>
    }
    // 上下架
    if (td.type === 'buyable') {
      return <Tag color={td.value ? 'green' : 'red'} size="small" style="transform: scale(0.8)">{td.value ? '上架' : '下架'}</Tag>
    }
    // 薯券类型
    if (td.type === 'discountType') {
      return <Tag color={td.value === 0 ? 'green' : 'red'} size="small" style="transform: scale(0.8)">{td.value === 0 ? '满减券' : '折扣券'}</Tag>
    }
    // 分销计划类型
    if (td.type === 'dsPlanType' && td.value !== undefined) {
      return <Tag color={td.value === 1 ? 'green' : 'blue'} size="small" style="transform: scale(0.8)">{td.value === 1 ? '营销计划' : '通用计划'}</Tag>
    }
    // 列表
    if (td.type === 'list') {
      return <div class="list-wrapper">
      {
        td.value.length ? td.value.map(v => <div class="row">
          {
            v.length ? v.map(v1 => <div class="column">{isComponent(v1)}</div>) : null
          }
        </div>) : <Text class="no-data">暂无数据</Text>
      }
    </div>
    }
    return null
  }

  const handleDataTypeChange = (e: any) => {
    newValue.value.dataType = e
    previewUrl.value = ''

    props.changeFn({
      ...newValue.value,
      cover1: '',
      result: [],
    })
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleInput = debounce(async v => {
    const { value } = v.target
    previewUrl.value = ''

    const fn = (result: any[] = []) => {
      newValue.value = {
        ...newValue.value,
        itemId: value,
        cover1: previewUrl.value,
        result,
      }
      props.changeFn(newValue.value)
    }

    if (value) {
      const typeMap = {
        'component<itemSearchWithImageTransform>': {
          Goods: {
            solutionKey: 'ItemGoods',
            promise: searchDatasetItem,
          },
          Note: {
            solutionKey: 'Note',
            promise: searchDatasetNote,
          },
        },
      }
      const targetType = typeMap[props.type] || {}
      const { solutionKey, promise } = targetType?.[newValue.value.dataType || 'Goods'] || {}
      if (!solutionKey) {
        toast.danger('该组件暂不支持搜索')
        return
      }
      const params = {
        primaryKeyList: [value],
        solutionKey,
      }
      loading.value = true
      const res = await promise(params)
      loading.value = false
      if (Object.keys(res).length) {
        fn(res[value])
      } else {
        fn()
      }
    } else {
      fn()
    }
  }, 200)

  const handleChange = v => {
    if (!v) {
      newValue.value = {
        ...newValue.value,
        itemId: '',
        cover1: '',
        result: [],
      }
      props.changeFn(newValue.value)
    }
  }
</script>

<style lang="stylus" scoped>
table {
  border: 1px solid #efefef
  border-collapse: collapse
  th, td {
    border: 1px solid #efefef
  }
  th {
    height: 20px
    line-height: 20px
    text-align: left
    white-space: nowrap
    font-size: 12px
    font-weight: normal
    background-color: #f7f7f7
  }
  td {
    background-color: #fff
    .d-text {
      width: 80px
      font-size: 12px
      cursor: pointer
      &.color-black {
        color: #000
        font-weight: 500
      }
      &.color-grey {
        color: #999
      }
      &.color-green {
        color: #00a830
      }
    }
  }
  .no-data {
    width: 66px !important
    line-height: 66px
    text-align: center
    color: #999
  }
  .image-wrapper {
    position: relative
    width: 66px
    height: 66px
    border-radius: 4px
    overflow: hidden
    cursor: pointer
    &:hover {
      img {
        transform: scale(1.1)
        transition: transform 0.2s linear
      }
    }
    img {
      width: 66px
      height: 66px
      transition: transform 0.2s linear
    }
    .image-mask {
      position: absolute
      top: 0
      left: 0
      width: 66px
      height: 66px
      background-color: rgba(0, 0, 0, 0.03)
    }
  }
  div {
    box-sizing: border-box
  }
  .list-wrapper {
    display: flex
    flex-direction: column
    height: 66px
    margin: -5px 0
    overflow-y: scroll
    scroll-behavior: smooth
    .row {
      display: flex
      align-items: center
      margin-bottom: 5px
      height: 66px
      &:last-of-type {
        margin-bottom: 0
      }
      .column {
        display: flex
        align-items: center
        justify-content: center
        width: 77PX
        height: 66PX
        padding: 5px
        &:not(first-of-type) {
          border-right: 1px solid #efefef
        }
        &:first-of-type {
          padding-left: 0
        }
        &:last-of-type {
          padding-right: 0
          border-right: none
        }
      }
    }
  }
}
</style>
