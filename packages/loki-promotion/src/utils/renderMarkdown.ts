import MarkdownIt from 'markdown-it'
import DOMPurify from 'dompurify'

const md = new MarkdownIt({
  html: true,
  linkify: true,
  breaks: true,
})

/**
 * 渲染 markdown 为安全 HTML
 */
export function renderMarkdown(text: string): string {
  if (!text) return ''
  // 去除所有 ```markdown ... ``` 或 ``` ... ``` 代码块包裹
  let fixed = text.replace(/```(?:markdown)?\s*([\s\S]*?)\s*```/gi, '$1')
  // 合并被拆开的标题行，兼容无空格写法
  fixed = fixed.replace(/^(#{1,6})\s*\n?([0-9]+\.)?\s*\n?([^ #\n])/gm, (_m, h, n, r) => `${h} ${n || ''}${r}`)
  const html = md.render(fixed)
  if (typeof window !== 'undefined' && typeof DOMPurify !== 'undefined') {
    try {
      return DOMPurify.sanitize(html, { USE_PROFILES: { html: true }, ADD_ATTR: ['target'] })
    } catch {
      return md.utils.escapeHtml(fixed)
    }
  }
  return html
}
