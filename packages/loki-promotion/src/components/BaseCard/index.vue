<template>
  <div class="base-card">
    <div
      v-if="title"
      class="title-wrap"
    >
      <div class="title">
        <span class="t">{{ title }}</span>
        <slot name="edit">
          <Icon
            v-if="editVisible"
            :icon="Edit"
            color="text-description"
            @click="edit"
          />
        </slot>
      </div>
      <slot name="action">
        <div class="action-wrap">
          <Icon
            v-if="deleteVisible"
            :icon="Delete"
            color="text-description"
            @click="remove"
          />
        </div>
      </slot>
    </div>
    <div
      v-if="desc || $slots.desc"
      class="desc-wrap"
    >
      <slot name="desc">
        <span>{{ desc }}</span>
      </slot>
    </div>
    <div
      v-if="$slots.footer"
      class="card-footer"
    >
      <slot name="footer">
        <Button>操作</Button>
      </slot>
    </div>
  </div>
</template>

<script lang="tsx" setup>
  import { Icon, Button } from '@xhs/delight'
  import { Delete, Edit } from '@xhs/delight/icons'

  interface Props {
    title?: string
    desc?: string
    deleteVisible?: boolean
    editVisible?: boolean
  }
  withDefaults(
    defineProps<Props>(),
    {
      title: '',
      desc: '',
      deleteVisible: true,
      editVisible: true,
    },
  )

  const emit = defineEmits(['edit', 'remove'])

  const edit = () => {
    emit('edit')
  }

  const remove = () => {
    emit('remove')
  }

</script>

<style lang="stylus" scoped>
.base-card
  height 200px
  background linear-gradient(180deg, rgba(58, 100, 255, 0.1) 0%, rgba(58, 100, 255, 0) 19.75%), #FFFFFF
  border-radius 8px
  position relative
  &:hover
    box-shadow 0px 4px 20px rgba(0, 0, 0, 0.06)
.title-wrap
  padding 20px 20px 0
  display flex
  align-items center
  font-size 16px
  line-height 24px
  font-weight 500
.title
  display flex
  align-items center
  flex 1
  .t
    padding-right 5px
.action-wrap
  display flex
  align-items center
.desc-wrap
  padding 5px 20px 0
  font-size 14px
  line-height 22px
  color rgba(0, 0, 0, 0.45)
.card-footer
  // border-top: 1px solid rgba(51, 51, 51, 0.1);
  position absolute
  bottom 0
  left 0
  width 100%
  height 56px
  display flex
  align-items center
  justify-content space-around
  padding 0 10px
  box-sizing border-box
  & :deep(.d-button)
    flex 1
    margin-left 10px
    margin-right 10px
.d-icon
  cursor pointer
</style>
