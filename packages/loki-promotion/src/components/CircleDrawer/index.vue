<template>
  <div class="circle-drawer">
    <SelectionSelect
      :model-value="props.value"
      :selection-where="SelectionWhere.Activity"
      :disabled="props.disabled"
      :entity-type="EntityType.SELLER"
      @update:model-value="handleChange"
    />
  </div>
</template>

  <script lang="tsx" setup>
  import { SelectionSelect, SelectionWhere, EntityType } from '@xhs/hawk/src/components-biz/SelectionSelect'

  const props = withDefaults(defineProps<{
    value: number
    disabled?: boolean
  }>(), {})

  const emits = defineEmits([
    'change',
  ])

  const handleChange = (v?: number) => {
    emits('change', v)
  }

  </script>

<style lang="stylus" scoped>
    .circle-drawer {
        display: flex
    }
</style>
