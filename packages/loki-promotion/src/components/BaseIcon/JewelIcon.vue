<template>
  <Icon
    v-bind="$attrs"
  >
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 17 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.33194 5.55702C6.08733 5.28183 5.66595 5.25704 5.39076 5.50165C5.11557 5.74626 5.09078 6.16765 5.3354 6.44283L8.00206 9.44283C8.12857 9.58516 8.30991 9.66659 8.50033 9.66659C8.69076 9.66659 8.8721 9.58516 8.99861 9.44283L11.6653 6.44283C11.9099 6.16765 11.8851 5.74626 11.6099 5.50165C11.3347 5.25704 10.9133 5.28183 10.6687 5.55702L8.50033 7.99646L6.33194 5.55702Z"
        fill="currentColor"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M3.73196 1.33325C3.47074 1.33325 3.23359 1.48581 3.12529 1.72352L1.227 5.89019C1.11977 6.12556 1.15835 6.4017 1.32598 6.59866L7.99264 14.432C8.11931 14.5808 8.3049 14.6666 8.50033 14.6666C8.69577 14.6666 8.88136 14.5808 9.00803 14.432L15.6747 6.59866C15.8423 6.4017 15.8809 6.12555 15.7737 5.89019L13.8754 1.72352C13.7671 1.48581 13.5299 1.33325 13.2687 1.33325H3.73196ZM2.61606 6.05728L4.16083 2.66659H12.8398L14.3846 6.05728L8.50033 12.9713L2.61606 6.05728Z"
        fill="currentColor"
      />
    </svg>
  </Icon>
</template>
<script lang="tsx" setup>
  import { Icon } from '@xhs/delight'
</script>
