<template>
  <Icon
    v-bind="$attrs"
  >
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 16 16"
      fill="currentColor"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M14.4221 1.48397C14.707 1.71712 14.749 2.13713 14.5159 2.4221L8.51589 9.75543C8.28455 10.0382 7.86873 10.0821 7.58346 9.85385L4.25012 7.18718C3.96261 6.95718 3.916 6.53765 4.14601 6.25014C4.37601 5.96263 4.79554 5.91602 5.08305 6.14603L7.90149 8.40078L13.4839 1.57778C13.7171 1.29282 14.1371 1.25082 14.4221 1.48397ZM2.99992 2.66661C2.81583 2.66661 2.66659 2.81585 2.66659 2.99994V12.9999C2.66659 13.184 2.81582 13.3333 2.99992 13.3333H12.9999C13.184 13.3333 13.3333 13.184 13.3333 12.9999V5.99994C13.3333 5.63175 13.6317 5.33327 13.9999 5.33327C14.3681 5.33327 14.6666 5.63175 14.6666 5.99994V12.9999C14.6666 13.9204 13.9204 14.6666 12.9999 14.6666H2.99992C2.07945 14.6666 1.33325 13.9204 1.33325 12.9999V2.99994C1.33325 2.07947 2.07945 1.33327 2.99992 1.33327H10.6666C11.0348 1.33327 11.3333 1.63175 11.3333 1.99994C11.3333 2.36813 11.0348 2.66661 10.6666 2.66661H2.99992Z"
        fill="currentColor"
      />
    </svg>
  </Icon>
</template>

<script lang="tsx" setup>
  import { Icon } from '@xhs/delight'
</script>
