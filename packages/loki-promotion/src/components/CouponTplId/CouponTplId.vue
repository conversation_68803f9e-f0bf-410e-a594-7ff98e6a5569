<template>
  <div>
    <Input
      v-model="dataValue"
      :type="type === 2 ? 'number' : 'text'"
      style="width:100%"
      :placeholder="placeholder"
      v-bind="$attrs"
      @input="changeId"
    />
    <div>
      <Text
        v-if="loading"
        loading
        icon-position="left"
      >加载中</Text>
    </div>
    <div v-if="type === 1 && coupon.couponTemplateId">
      <Text color="text-paragraph">{{ coupon.desc }}</Text> <br>
      <Text color="text-paragraph">
        券库存：{{ coupon.amount || '无限制' }} <br>  发放时间：{{ formatTime(coupon.startTime) }} ～ {{ formatTime(coupon.sendTime) }}
      </Text>
    </div>
    <div v-else-if="type === 2 && allowance.allowanceTemplateId">
      <Text color="text-paragraph">{{ allowance.desc }} <br> 发放时间：{{ formatTime(allowance.startTime) }} ～ {{ formatTime(allowance.sendTime) }}</Text> <br>
      <Text color="text-paragraph">
        津贴库存：{{ allowance.totalAmount || '无限制' }}
      </Text>
    </div>
  </div>
</template>

<script lang="tsx" setup>
  import { ref, watch } from 'vue'
  import { Input, Text } from '@xhs/delight'
  import dayjs from 'dayjs'

  import { CouponInfo, AllowanceInfo } from '~/types/benefitPool'
  import { debounce } from '~/utils/throttle-debounce'
  import { getCouponInfo } from '~/services/benefitPool'

  const props = withDefaults(
    defineProps<{
      placeholder: string
      type?: number // 1: '薯券',2: '津贴',3: '无奖品',4: '积分',
      value?: string
    }>(),
    {
      placeholder: '请输入券模版Id',
      type: 1,
      value: undefined,
    },
  )

  const emit = defineEmits(['change'])

  const loading = ref(false)
  const dataValue = ref(props.value)
  const coupon = ref({} as CouponInfo)
  const allowance = ref({} as AllowanceInfo)

  watch(
    () => props.value,
    () => {
      dataValue.value = props.value
    },
  )

  // 获取券模板信息
  const changeId = debounce(
    async (cancelLast = true) => {
      if (dataValue.value) {
        loading.value = true
        const res = await getCouponInfo({
          prizeType: props.type,
          templateId: dataValue.value,
        }, cancelLast).catch(err => {
          loading.value = false
          throw err
        })
        if (props.type === 1) {
          coupon.value = res?.couponInfo || {} as CouponInfo
        } else if (props.type === 2) {
          allowance.value = res?.allowanceInfo || {} as AllowanceInfo
        }
        loading.value = false
        if (res) {
          emit('change', dataValue.value)
        }
      } else {
        coupon.value = {} as CouponInfo
        allowance.value = {} as AllowanceInfo
        emit('change', dataValue.value)
      }
    },
  )

  if (props.value) {
    changeId(false)
  }

  const formatTime = (n:number) => (n ? dayjs.unix(n).format('YYYY-MM-DD') : '-')

</script>
