<template>
  <div>
    <Text
      v-if="mode === 'text' || disabled"
      class="pointer"
      :icon="disabled ? {} : {icon: Edit, color: 'text-description'}"
      icon-position="right"
      @click="mode = 'input'"
    >{{ value }}</Text>
    <InputNumber
      v-else
      v-model="dataValue"
      autofocus
      :max="9999999"
      style="width: 100px"
      placeholder="请输入"
      @blur="change"
    />
  </div>
</template>

<script lang="tsx" setup>
  import { ref, watchEffect } from 'vue'
  import { Text, InputNumber } from '@xhs/delight'
  import { Edit } from '@xhs/delight/icons'

  const props = withDefaults(
    defineProps<{
      value: number
      disabled?: boolean
    }>(),
    {
      disabled: false,
    },
  )

  const emit = defineEmits(['change'])

  const dataValue = ref(props.value)
  watchEffect(() => {
    dataValue.value = props.value
  })

  const mode = ref<'text' | 'input'>('text')

  const change = () => {
    mode.value = 'text'
    if (props.value === dataValue.value) return
    emit('change', dataValue.value)
  }

</script>

<style>
</style>
