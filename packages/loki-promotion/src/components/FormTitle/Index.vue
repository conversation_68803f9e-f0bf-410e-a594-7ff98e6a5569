<template>
  <div
    class="form-title"
    :style="{
      ...defaultStyle,
      ...style,
    }"
  >{{ title }}</div>
</template>

<script lang="tsx" setup>
  import { withDefaults, CSSProperties } from 'vue'

  const defaultStyle = {
    margin: '12px 0 30px',
    fontSize: '16px',
    fontWeight: '500',
    color: '#2D2D2D',
  }
  withDefaults(defineProps<{
    style?: CSSProperties
    title: string
  }>(), {
    style: () => ({}),
  })
</script>
