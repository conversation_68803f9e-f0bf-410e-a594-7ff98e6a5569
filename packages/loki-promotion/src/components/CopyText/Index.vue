<template>
  <div
    v-if="text"
    class="copy-text"
  >
    <slot />
    <div
      class="icon-wrapper"
      @click="copyText"
    >
      <Icon
        class="icon"
        :icon="Copy"
        color="primary"
        size="small"
      />
    </div>
  </div>
</template>

<script lang="tsx" setup>
  import {
    Icon, toast,
  } from '@xhs/delight'
  import { Copy } from '@xhs/delight/icons'
  import copyToClipboard from 'loki-shared/utils/copy'

  const props = defineProps<{
    text: string | number
  }>()

  const emit = defineEmits(['click'])

  const copyText = () => {
    const text = `${props.text}`
    if (text) {
      copyToClipboard(text).then(() => {
        toast.success({ description: '已复制到剪贴板', closeable: true })
      }).catch(() => {
        toast.danger('复制失败')
      })
    } else {
      toast.danger('请设置text属性')
    }
    emit('click')
  }
</script>

<style lang="stylus" scoped>
.copy-text {
  display: flex
  align-items: center
  .icon-wrapper {
    display: flex
    align-items: center
    justify-content: center
    padding: 5px
    cursor pointer
  }
}
</style>
