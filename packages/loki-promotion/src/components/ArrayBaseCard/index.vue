<template>
  <div
    v-for="(item,index) in data"
    :key="item.benifitId"
    class="card"
  >
    <Card>
      <!-- 暴露原有的插槽 -->
      <template
        v-for="(_, name) in $slots"
        :key="name"
        #[name]="props"
      >
        <!-- 将插槽和props穿入slot-->
        <slot
          :name="name"
          :index="index"
          :rowData="item"
          :dateSource="data"
          v-bind="props"
        />
      </template>
    </Card>
  </div>
</template>
<script setup lang="ts">
  import { Card } from '@xhs/delight'

  defineProps<{
    data:any
  }>()

</script>
<style>
.card{
  margin-bottom: 15px;
}
</style>
