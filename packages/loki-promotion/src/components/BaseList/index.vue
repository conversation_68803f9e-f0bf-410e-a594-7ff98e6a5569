<template>
  <div class="container">
    <div class="top">
      <slot name="top" />
    </div>

    <div class="list">
      <Spinner
        :spinning="isLoading"
        size="large"
        tip="加载中"
      >
        <Table
          v-if="dataSource.length !== 0"
          :columns="columns"
          :data-source="dataSource"
          :expanded="expanded"
          v-bind="$attrs"
          @update:expanded="$emit('update:expanded', $event)"
        >

          <!-- 暴露原有的插槽 -->
          <template
            v-for="(_, name) in $slots"
            :key="name"
            #[name]="props"
          >
            <slot
              :name="name"
              v-bind="props"
            />
          </template>
        </Table>
        <Result
          v-else
          class="res"
          status="empty"
          title="暂无内容"
          :sub-title="emptyText"
        />
      </Spinner>

    </div>

    <Pagination
      v-if="showTotal"
      :total="total"
      :page-size="pageSize"
      :style="{marginTop: '16px'}"
      @update:modelValue="(v)=>emit('onPagination',v)"
      @update:page-size="(v)=>emit('onPageSize',v)"
    />
  </div>
</template>
<script lang="tsx" setup>
  import {
    Spinner, Table, Result, Pagination,
  } from '@xhs/delight'

  import { TdContent, DataSource } from '@xhs/delight/types/components/Table/interface'

  withDefaults(defineProps<{
    isLoading: boolean
    columns: TdContent[]
    dataSource?: DataSource[]
    expanded?:(string | number)[]
    emptyText?: string
    showTotal?: boolean
    pageSize?: number
    total?: number
  }>(), {
    dataSource: () => [],
    expanded: () => [],
    emptyText: '请先去创建积分',
    showTotal: false,
    pageSize: 15,
    total: 0,
  })

  const emit = defineEmits(['onPagination', 'onPageSize', 'update:expanded'])

</script>

<style lang="stylus" scoped>
  .center
    position absolute
    top 50%
    left 50%
    transform translateX(-50%) translateY(-50%)

  .tips
    font-family 'PingFang SC'
    font-style  normal
    font-weight  400
    font-size  12px
    line-height  17px
    margin-top 32px
    color  rgba(0, 0, 0, 0.45)

  .container
    overflow-y auto

  .list
    position relative
    width 100%
    overflow-y auto
    margin-top 20px
  .res
    padding-bottom 50px

</style>
