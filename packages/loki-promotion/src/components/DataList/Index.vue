<template>
  <component
    :is="usePanel ? Panel : 'div'"
    class="data-list"
  >
    <!-- 操作区 -->
    <div
      v-if="buttons.length || slotHeaderOther"
      class="data-list-header"
    >
      <div class="main">
        <Space>
          <Button
            v-for="({ label, ...btn }, i) in buttons"
            :key="i"
            v-bind="btn"
          >
            {{ label }}
          </Button>
        </Space>
      </div>
      <div class="other">
        <slot name="headerOther" />
      </div>
    </div>
    <!-- 表格 -->
    <div class="data-list-body">
      <Spinner
        :spinning="loading"
        wrapper-class-name="spinner"
      >
        <Table
          :columns="columns"
          :data-source="dataSource"
        />
      </Spinner>
    </div>
    <!-- 分页 -->
    <div class="data-list-footer">
      <Pagination
        :model-value="page.pageNo"
        :page-size="page.pageSize"
        :page-size-options="page.pageSizeOptions"
        :total="page.total"
        @update:modelValue="onPageChange"
        @update:pageSize="onSizeChange"
      />
    </div>
  </component>
</template>

<script lang="tsx" setup>
  import { withDefaults, computed, useSlots } from 'vue'
  import {
    Space, Button, Table, Pagination, Spinner,
  } from '@xhs/delight'
  import type { ButtonContent } from '@xhs/delight'
  import Panel from '../Panel/Index.vue'

  const props = withDefaults(defineProps<{
    usePanel: boolean
    loading: boolean
    /** 操作区按钮 */
    buttons:(Partial<Partial<ButtonContent>> & { label: string })[]
    columns: any[]
    dataSource: any[]
    pagination: { total: number; pageNo: number; pageSize: number; pageSizeOptions: number[] }
  }>(), {
    loading: false,
    usePanel: false,
  })

  const page = computed(() => ({
    total: props.pagination?.total || 0,
    pageNo: props.pagination?.pageNo || 1,
    pageSize: props.pagination?.pageSize || 10,
    pageSizeOptions: props.pagination?.pageSizeOptions || [10, 20, 50],
  }))

  const emits = defineEmits([
    /** 页码变化的回调事件 */
    'page-change',
    /** 每页条数变化的回调事件 */
    'size-change',
  ])

  // 是否传了插槽
  const slotHeaderOther = computed(() => !!(useSlots().headerOther))

  // 页码变化
  function onPageChange(v) {
    emits('page-change', v)
  }

  // 每页条数变化
  function onSizeChange(v) {
    emits('size-change', v)
  }

</script>

<style lang="stylus" scoped>
.data-list {
  flex-direction: column
  align-items: stretch
  &-header {
    flex: none
    display: flex
    align-items: center
    justify-content: space-between
    margin-bottom: 20px
    .other {
      display: flex
      justify-content: flex-end
    }
  }
  &-body {
    flex: auto
    margin-bottom: 20px
  }
  &-footer {
    flex: none
  }
}
:deep(.d-td) {
  font-size: 12px
}
.spinner :deep(.d-spinner-mask) {
  background-color: #fff
}
</style>
