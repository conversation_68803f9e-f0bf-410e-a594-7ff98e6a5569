/**
 * 复选框选项数据结构
 */
export interface CheckboxOption {
  /** 显示文本 */
  label: string
  /** 选项值，必须唯一 */
  value: string | number
  /** 子选项列表（仅父级选项需要） */
  children?: CheckboxOption[]
  /** 是否禁用（可选） */
  disabled?: boolean
}

/**
 * 父级复选框状态
 */
export interface ParentCheckState {
  /** 是否选中 */
  checked: boolean
  /** 是否为半选状态 */
  indeterminate: boolean
}

/**
 * 组件 Props 类型
 */
export interface MultiLevelCheckboxProps {
  /** 选中的值数组 */
  modelValue: (string | number)[]
  /** 复选框选项数据 */
  options: CheckboxOption[]
}

/**
 * 组件 Emits 类型
 */
export interface MultiLevelCheckboxEmits {
  /** v-model 更新事件 */
  (e: 'update:modelValue', value: (string | number)[]): void
  /** 选中状态变化事件 */
  (e: 'change', value: (string | number)[]): void
}

/**
 * 组件实例类型
 */
export interface MultiLevelCheckboxInstance {
  /** 获取当前选中的值 */
  getSelectedValues: () => (string | number)[]
  /** 设置选中的值 */
  setSelectedValues: (values: (string | number)[]) => void
  /** 清空所有选择 */
  clearSelection: () => void
  /** 全选所有子项 */
  selectAll: () => void
  /** 获取所有可选值 */
  getAllSelectableValues: () => (string | number)[]
  /** 检查是否全选 */
  isAllSelected: () => boolean
  /** 检查是否有选中项 */
  hasSelection: () => boolean
}
