<template>
  <div class="multi-level-checkbox">
    <!-- 有子级的分组选项 -->
    <ParentChildCheckbox
      v-for="group in groupOptions"
      :key="group.value"
      :value="selectedValues"
      :group="group"
      class="checkbox-group"
      :disabled="disabled"
      @change="handleGroupChange"
    />

    <!-- 没有子级的独立选项 -->
    <div
      v-if="standaloneOptions.length > 0"
      class="checkbox-group standalone"
    >
      <div class="standalone-checkboxes">
        <CheckboxGroup
          v-model="selectedValues"
          :options="standaloneOptions"
          :disabled="disabled"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { CheckboxGroup } from '@xhs/delight'
  import { debounce } from 'lodash-es'
  import {
    computed,
    ref,
    toRefs,
    watch,
  } from 'vue'
  import ParentChildCheckbox from './ParentChildCheckbox.vue'

  // 直接定义类型接口
  interface CheckboxOption {
    /** 显示文本 */
    label: string
    /** 选项值，必须唯一 */
    value: string | number
    /** 子选项列表（仅父级选项需要） */
    children?: CheckboxOption[]
    /** 是否禁用（可选） */
    disabled?: boolean
    /** 描述 */
    description?: string
  }

  interface MultiLevelCheckboxProps {
    /** 选中的值数组 */
    value: (string | number)[]
    /** 复选框选项数据 */
    options: CheckboxOption[]
    /** 是否禁用 */
    disabled?: boolean
  }

  interface MultiLevelCheckboxEmits {
    /** v-model 更新事件 */
    (e: 'update:value', value: (string | number)[]): void
    /** 选中状态变化事件 */
    (e: 'change', value: (string | number)[]): void
  }

  // Props 定义
  const props = withDefaults(defineProps<MultiLevelCheckboxProps>(), {
    value: () => [],
    options: () => [],
    disabled: false,
  })

  // Emits 定义
  const emit = defineEmits<MultiLevelCheckboxEmits>()

  // 使用 toRefs 优化响应式性能
  const { value, options, disabled } = toRefs(props)

  // 统一的选中值状态 - 只存储实际的子级选项值，不包含父级值
  const selectedValues = ref<(string | number)[]>([...value.value])

  // 计算属性：有子级的选项
  const groupOptions = computed(() => options.value.filter(option => option.children && option.children.length > 0))

  // 计算属性：没有子级的独立选项
  const standaloneOptions = computed(() => options.value.filter(option => !option.children || option.children.length === 0))

  // 防抖的 emit 函数
  const debouncedEmit = debounce((newValue: (string | number)[]) => {
    emit('update:value', newValue)
    emit('change', newValue)
  }, 16)

  // 监听内部选中值变化
  watch(
    selectedValues,
    newValue => {
      debouncedEmit([...newValue])
    },
    { deep: true },
  )

  // 监听外部值变化
  watch(
    value,
    newValue => {
      if (JSON.stringify(newValue) !== JSON.stringify(selectedValues.value)) {
        selectedValues.value = [...newValue]
      }
    },
    { deep: true },
  )

  const handleGroupChange = (newValues: (string | number)[]) => {
    selectedValues.value = [...newValues]
  }
</script>

<style lang="stylus" scoped>
.multi-level-checkbox {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;

  .checkbox-group {
    // 独立选项样式
    &.standalone {
      .standalone-checkboxes {
        :deep(.d-checkbox-group) {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .d-checkbox {
            .d-checkbox__label {
              font-weight: 500;
              color: #333;
            }
          }
        }
      }
    }
  }
}
</style>
