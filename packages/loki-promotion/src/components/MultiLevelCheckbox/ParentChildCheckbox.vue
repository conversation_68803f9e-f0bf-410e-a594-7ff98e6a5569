<template>
  <div class="parent-child-checkbox">
    <!-- 父级复选框 -->
    <div class="parent-checkbox">
      <Checkbox
        ref="indicator"
        v-model="selectValue"
        :description="group.description"
        :disabled="disabled"
      >
        {{ group.label }}
      </Checkbox>
    </div>

    <!-- 子级复选框组 -->
    <div class="children-checkboxes">
      <CheckboxGroup
        v-model="selectValue"
        :indicator="indicator"
        :options="group.children"
        :disabled="disabled"
        @change="handleChildrenChange"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { Checkbox, CheckboxGroup } from '@xhs/delight'
  import { ref, toRefs, watch } from 'vue'

  // 直接定义类型接口
  interface CheckboxOption {
    /** 显示文本 */
    label: string
    /** 选项值，必须唯一 */
    value: string | number
    /** 子选项列表（仅父级选项需要） */
    children?: CheckboxOption[]
    /** 是否禁用（可选） */
    disabled?: boolean
    /** 描述 */
    description?: string
  }

  interface ParentChildCheckboxProps {
    /** 选中的值数组 */
    value: (string | number)[]
    /** 单个分组数据 */
    group: CheckboxOption
    /** 是否禁用 */
    disabled?: boolean
  }

  interface ParentChildCheckboxEmits {
    /** v-model 更新事件 */
    (e: 'update:value', value: (string | number)[]): void
    /** 选中状态变化事件 */
    (e: 'change', value: (string | number)[]): void
  }

  // Props 定义
  const props = withDefaults(defineProps<ParentChildCheckboxProps>(), {
    value: () => [],
    disabled: false,
  })

  // Emits 定义
  const emit = defineEmits<ParentChildCheckboxEmits>()

  // 使用 toRefs 优化响应式性能
  const { value, group, disabled } = toRefs(props)

  const selectValue = ref(value.value)

  // 监听 value 的变化
  watch(value, newValue => {
          if (JSON.stringify(newValue) !== JSON.stringify(selectValue.value)) {
            selectValue.value = newValue
          }
        },
        { deep: true, immediate: true })

  // const parentValue = ref()
  const indicator = ref()

  // 处理子级复选框组变化
  const handleChildrenChange = (newValues: unknown) => {
    if (!group.value.children) return

    // 类型转换：确保 newValues 是数组且包含正确类型
    const values = Array.isArray(newValues)
      ? (newValues as (string | number | boolean)[]).filter(v => typeof v === 'string' || typeof v === 'number') as (string | number)[]
      : []

    const newSelectedValues = [...values]
    emit('update:value', newSelectedValues)
    emit('change', newSelectedValues)
  }
</script>

<style lang="stylus" scoped>
.parent-child-checkbox {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 120px;

  .parent-checkbox {
    font-weight: 500;

    :deep(.d-checkbox) {
      .d-checkbox__label {
        font-weight: 500;
        color: #333;
      }
    }
  }

  .children-checkboxes {
    margin-left: 20px;

    :deep(.d-checkbox-group) {
      display: flex;
      flex-direction: column;
      gap: 6px;

      .d-checkbox {
        .d-checkbox__label {
          font-weight: 400;
          color: #666;
        }
      }
    }
  }
}
</style>
