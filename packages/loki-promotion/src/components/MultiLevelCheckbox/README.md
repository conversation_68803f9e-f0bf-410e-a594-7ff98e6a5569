# MultiLevelCheckbox 多级复选框组件

基于 @xhs/delight 组件库的多级复选框组件，支持父子级别的复选框结构和状态管理。

## 功能特性

- ✅ 支持多级复选框结构（树形结构）
- ✅ 父子级状态联动（全选/半选/未选中）
- ✅ v-model 双向绑定
- ✅ 扁平化数据输出（仅输出子级选中项）
- ✅ 支持数据回显
- ✅ TypeScript 支持

## 基础用法

```vue
<template>
  <MultiLevelCheckbox
    v-model="selectedValues"
    :options="checkboxOptions"
    @change="handleChange"
  />
</template>

<script setup>
import { ref } from 'vue'
import MultiLevelCheckbox from '~/components/MultiLevelCheckbox'
// 或者使用相对路径
// import MultiLevelCheckbox from '@/components/MultiLevelCheckbox'

const selectedValues = ref([])

const checkboxOptions = ref([
  {
    label: '笔记',
    value: 'note',
    children: [
      { label: '商笔', value: 'business_note' },
      { label: '购物笔记', value: 'shopping_note' },
      { label: '其他笔记', value: 'other_note' }
    ]
  },
  {
    label: 'K播',
    value: 'k_broadcast',
    children: [
      { label: '电商直播间', value: 'ecommerce_live' },
      { label: '店播', value: 'store_broadcast' },
      { label: '其他直播间', value: 'other_live' }
    ]
  }
])

const handleChange = (values) => {
  console.log('选中的值:', values)
}
</script>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| modelValue | 选中的值（支持 v-model） | `(string \| number)[]` | `[]` |
| options | 复选框选项数据 | `CheckboxOption[]` | `[]` |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| change | 选中状态发生变化时触发 | `(values: (string \| number)[]) => void` |
| update:modelValue | v-model 更新事件 | `(values: (string \| number)[]) => void` |

### 数据结构

```typescript
interface CheckboxOption {
  label: string                    // 显示文本
  value: string | number          // 选项值
  children?: CheckboxOption[]     // 子选项（仅父级需要）
}
```

## 状态管理规则

### 父级状态计算
- **未选中**：所有子级都未选中
- **半选中**：部分子级选中（indeterminate 状态）
- **选中**：所有子级都选中

### 交互行为
- **点击父级**：控制所有子级的选中/取消选中
- **点击子级**：仅影响该子级的选中状态，父级状态自动计算

### 数据输出
- 仅输出选中的**子级项目**的 value
- 父级项目不包含在输出数据中
- 输出格式为扁平化数组

## 样式定制

组件使用 Stylus 编写样式，支持通过 CSS 变量或深度选择器进行定制：

```stylus
.multi-level-checkbox {
  // 自定义间距
  gap: 32px;
  
  .checkbox-group {
    // 自定义分组样式
    .parent-checkbox {
      // 父级样式
    }
    
    .children-checkboxes {
      // 子级容器样式
      margin-left: 24px;
    }
  }
}
```

## 注意事项

1. **数据结构**：确保 options 中的父级项目包含 children 数组
2. **唯一性**：所有选项的 value 必须唯一
3. **回显**：组件会根据 modelValue 自动计算并显示正确的选中状态
4. **性能**：大量数据时建议使用虚拟滚动或分页加载
