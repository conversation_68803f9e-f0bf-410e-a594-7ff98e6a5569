<script lang="tsx">
  import {
    Field, Input, InputNumber, FormItem,
  } from '@xhs/delight-formily'
  import { defineComponent } from 'vue'
  import ToggleButton from '~/pages/Task/SceneStep/TaskCreate/components/toggleBtn'
  import useTaskConfig from '../../composables/useTaskConfig'

  export default defineComponent({
    setup() {
      const { activeOption, options } = useTaskConfig()
      const map = {
        1: InputNumber,
        2: Input,
        3: ToggleButton,
      }

      const Test = () => <>{
        // 在options中找到type为activeOption的option的configs
        (options.value.find(i => i.value === activeOption.value)?.configs ?? []).map(({
        key, label, placeholder, type,
      }) => {
        const Cmp = map[type]
        return <Field
          name={key}
          title={label}
          decorator={[FormItem]}
          component={[Cmp, {
                placeholder,
                options: [
                    { label: '是', value: true },
                    { label: '否', value: false },
                  ],
                }]}
          validator={type === 1 ? ['integer', { gte: 0 }] : []}
        />
      })
      }</>
      return () => <Test/>
    },
  })
</script>
