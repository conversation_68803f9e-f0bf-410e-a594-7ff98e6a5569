<template>
  <div>
    <SegmentControl
      :options="options"
      :model-value="form.segment"
      @change="segmentChange"
    />
    <DatePicker
      v-if="!form.segment"
      style="margin-top:20px;width:100%"
      :model-value="form.date"
      :placeholder="placeholder"
      :date-disabled="isDisabled"
      :hide-after-select="true"
      @change="pickerChange"
    />
    <DateRangePicker
      v-else
      style="margin-top:20px;width:100%"
      :date-disabled="isDisabled"
      :model-value="form.date"
      :placeholder="{start: '选择时间', end: '选择时间'}"
      @change="pickerChange"
    />
  </div>
</template>
<script setup lang="ts">
  import { ref, watch } from 'vue'
  import { SegmentControl, DatePicker, DateRangePicker } from '@xhs/delight'
  import dayjs, { Dayjs } from 'dayjs'

  const props = withDefaults(defineProps<{
    options?: { label: string; value: boolean }[]
    placeholder?:string
    dateDisabled?:(d:dayjs.Dayjs)=>boolean
    // segment代表选择的是是还是否，date就是DatePicker选中的时间范围
    // 其实在传入RangePicker里，date是一个对象{start:string,end:string}，但是如果这里写联合类型，非RangePicker会报错
    value: { segment:boolean; date:string }
    onlyFuture?: boolean
  }>(), {
    placeholder: '请选择开始日期',
    options: () => ([
      { label: '长期', value: false },
      { label: '自定义', value: true },
    ]),
    dateDisabled: () => false,
    onlyFuture: true,
  })

  const form = ref({ ...props.value })
  watch(
    () => props.value,
    v => {
      // @ts-ignore
      form.value = v || {}
    },
    {
      deep: true,
      immediate: true,
    },
  )

  type Emit = {
    (event: 'change', form: {segment:any; date:string}): void
    (event: 'blur', form: {segment:any; date:string}): void
  }
  const emit = defineEmits<Emit>()

  const now = dayjs()
  const oneYear = dayjs().add(1, 'year')
  const isDisabled = (d:Dayjs) => {
    if (props.onlyFuture && (d.isBefore(now, 'date') || d.isAfter(oneYear))) {
      return true
    }
    return props.dateDisabled(d)
  }

  const segmentChange = v => {
    form.value.segment = v
    emit('change', { ...form.value })
  }

  const pickerChange = v => {
    form.value.date = v
    emit('change', { ...form.value })
  }
</script>
