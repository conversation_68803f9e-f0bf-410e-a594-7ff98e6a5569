// <!-- custom component for formily  -->
import {
  defineComponent, h, ref, PropType,
} from 'vue'
import {
  connect, mapProps, useField,
} from '@formily/vue'
import {
  DatePicker as DelightDatePicker,
  DateRangePicker as DelightRangePicker,
} from '@xhs/delight'
import { DataField } from '@formily/core/esm/types'

import { transformComponent } from '@xhs/delight-formily/components/__builtins__'
import dayjs from 'dayjs'

/* eslint-disable @typescript-eslint/explicit-module-boundary-types */

type DateFormatType = 'year' | 'month' | 'day' | 'datetime' | 'datetimerange'
/**
 * year
 */
export const YEAR_FORMAT = 'YYYY'

/**
 * month
 */
export const YEAR_MONTH_FORMAT = 'YYYY-MM'

/**
 * day
 */
export const DAY_FORMAT = 'YYYY-MM-DD'

/**
 * datetime datetimerange
 */
export const DATE_TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss'

export type DatePickerProps = typeof DelightDatePicker & typeof DelightRangePicker & {
  isRange?: boolean
  type?: DateFormatType
}
export type DateRangePickerProps = typeof DelightRangePicker

const datePickerCompRef = ref()
const dateRangePickerCompRef = ref()

const TransformDatePicker = transformComponent<DatePickerProps>(DelightDatePicker, {
  change: 'update:modelValue',
}, undefined, datePickerCompRef)

const TransformDateRangePicker = transformComponent<DateRangePickerProps>(DelightRangePicker, {
  change: 'update:modelValue',
}, undefined, dateRangePickerCompRef)

const InnerDatePicker = connect(
  TransformDatePicker,
  mapProps(
    {
      value: 'modelValue',
      readOnly: 'disabled',
      required: true,
    },
  ),
)

const InnerDateRangePicker = connect(
  TransformDateRangePicker,
  mapProps(
    {
      value: 'modelValue',
      readOnly: 'disabled',
      required: true,
    },
  ),
)

export default defineComponent({
  name: 'DFormilyDatePicker',
  props: {
    format: {
      type: String,
      default: undefined,
    },
    type: {
      type: String as PropType<DateFormatType>,
      default: undefined,
    },
    isRange: {
      type: Boolean,
      default: false,
    },
    // transform 开启后
    // 1.若入参类型为 number (timestamp), 自动转化入参值 number -> date formation string
    // 2.回传给 formily 表单值，自动转化 date formation string -> number(timestamp)
    transform: {
      type: Boolean,
      default: undefined,
    },
  },
  emits: ['blur', 'focus', 'input', 'change'],
  setup(this, props, {
    attrs,
    slots,
    emit,
  }) {
    const vSlots = attrs?.['v-slots'] as Record<string, unknown> || {}
    const Component = props.isRange ? InnerDateRangePicker : InnerDatePicker
    const format = props.format || getDefaultFormat(props.type as DateFormatType)

    const fieldRef = useField<DataField>()

    return () => {
      const field = fieldRef.value

      return h(
        Component,
        {
          ...attrs,
          ...props,
          format,
          modelValue: props.transform ? getModelValueFromTransform(props.isRange, field.value, format) : field.value,
          'onUpdate:modelValue': function (val) {
            if (props.transform) {
              emit('change', transformModelValueToTimeStamp(props.isRange, val))
              return
            }

            emit('change', val)
          },
          onInput() {
            emit('input')
          },
          onFocus() {
            emit('focus')
          },
          onBlur() {
            emit('blur')
          },
          'onFocus:start': function () {
            if (!props?.isRange) {
              return
            }
            emit('focus')
          },
          'onFocus:end': function () {
            if (!props?.isRange) {
              return
            }
            emit('focus')
          },
          'onBlur:start': function () {
            if (!props?.isRange) {
              return
            }
            emit('blur')
          },
          'onBlur:end': function () {
            if (!props?.isRange) {
              return
            }
            emit('blur')
          },
          // 如果设置了required, 组件内部会调用内部逻辑，与 formily 的行为不符，这里兼容一下
          required: fieldRef.value?.selfErrors?.length < 1 ? false : attrs?.required,
          validate: () => fieldRef.value?.selfErrors?.length < 1,
          validateTiming: 'immediate',
        },
        {
          ...slots,
          ...vSlots,
        },
      )
    }
  },
})

export function getDefaultFormat(type: DatePickerProps['type']): string {
  if (type === 'year') {
    return YEAR_FORMAT
  }
  if (type === 'month') {
    return YEAR_MONTH_FORMAT
  }

  if (type === 'day') {
    return DAY_FORMAT
  }

  if (type === 'datetime' || type === 'datetimerange') {
    return DATE_TIME_FORMAT
  }

  return DATE_TIME_FORMAT
}

export function formatDate(v: number, format?: string): string {
  return dayjs.unix(v).format(format || DATE_TIME_FORMAT)
}

export function getTimeStamp(time: string): number | undefined {
  return time ? dayjs(time).unix() : undefined
}

export function getIsValueNumberType(isRange: boolean, value: any): boolean {
  if (isRange) {
    return typeof value?.start === 'number' || typeof value?.end === 'number'
  }

  return typeof value === 'number'
}

export function getModelValueFromTransform(isRange: boolean, value: any, format: string): string | {
  start?: string
  end?: string
} {
  if (!getIsValueNumberType(isRange, value)) {
    return value
  }

  if (isRange) {
    // 没有传 start 或 end ，必须给 undefined, 不然 DatePicker 选择框会自动消失
    return {
      start: value?.start ? formatDate(value?.start, format) : undefined,
      end: value?.end ? formatDate(value?.end, format) : undefined,
    }
  }

  return formatDate(value, format)
}

export function transformModelValueToTimeStamp(isRange: boolean, value: any) {
  if (isRange) {
    return {
      start: getTimeStamp(value?.start),
      end: getTimeStamp(value?.end),
    }
  }

  return getTimeStamp(value)
}
