<template>
  <div
    v-if="errorVisible"
    class="error-wrap"
  >
    <Icon :icon="errorIcon" />
    <div class="error-title">其他Tab表单验证失败，需要修改正确后重新提交</div>
    <Icon
      class="pointer"
      :icon="CloseIcon"
      @click="errorVisible = false"
    />
  </div>
  <OTabs
    :key="`${refreshKey}`"
    theme="card"
    :model-value="active"
    force-render
    style="width: 100%"
    @update:model-value="changeActive"
    @close="close"
  >
    <OTabPane
      v-for="(item, key) in data"
      :key="`${key}_${getLabel(key)}`"
      :icon="errors[key] ? errorIcon : undefined"
      :label="getLabel(key)"
      force-render
      :closeable="data.length > 1"
    >
      <div class="tab-content">
        <RecursionField
          :schema="schema.items"
          :name="key"
          only-render-properties
        />
      </div>
    </OTabPane>
    <OTabPane
      :label="`新建${props.title}`"
      :icon="Plus"
    />
  </OTabs>
</template>

<script lang="tsx" setup>
  import { ref, computed } from 'vue'
  import { Tabs, TabPane, Icon } from '@xhs/delight'
  import { ArrayField, onFormSubmit } from '@formily/core'
  import { RecursionField, useFieldSchema, useFormEffects } from '@formily/vue'
  import {
    observer, useField, useForm,
  } from '@xhs/delight-formily'
  import { Close as CloseIcon, Plus } from '@xhs/delight/icons'

  const errorIcon = () => `<svg
    width="100%"
    height="100%"
    viewBox="0 0 27 27"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M13.3333 26.6667C20.6971 26.6667 26.6667 20.6972 26.6667 13.3334C26.6667 5.96962 20.6971 7.82013e-05 13.3333 7.82013e-05C5.96954 7.82013e-05 0 5.96962 0 13.3334C0 20.6972 5.96954 26.6667 13.3333 26.6667ZM13.4136 5.71428C14.1518 5.71428 14.7502 6.31273 14.7502 7.05095V15.071C14.7502 15.8092 14.1518 16.4077 13.4136 16.4077C12.6753 16.4077 12.0769 15.8092 12.0769 15.071V7.05095C12.0769 6.31273 12.6753 5.71428 13.4136 5.71428ZM11.8089 19.3484C11.8089 20.2343 12.5271 20.9524 13.4129 20.9524C14.2988 20.9524 15.0169 20.2343 15.0169 19.3484C15.0169 18.4626 14.2988 17.7444 13.4129 17.7444C12.5271 17.7444 11.8089 18.4626 11.8089 19.3484Z"
      fill="#f03860"
    />
  </svg>`

  const OTabs = observer(Tabs, {})
  const OTabPane = observer(TabPane)

  interface Props {
    title: string
    modelValue: string[]
    defaultValue: any
  }

  const props = withDefaults(
    defineProps<Props>(),
    {
      title: '权益池',
      modelValue: () => [],
      defaultValue: () => ({ key: Math.random() }),
    },
  )

  const emit = defineEmits(['change'])

  const refreshKey = ref(1) // 因为组件props相应有问题，所以强制刷新
  const fieldRef = useField<ArrayField>()
  const form = useForm()
  const schema = useFieldSchema()

  const data = computed(() => fieldRef.value.value)

  const getLabel = (i:number) => `${props.title} ${i >= 9 ? (i + 1) : `0${i + 1}`}`

  // 高亮tabs --
  const active = ref(getLabel(0))
  // 错误提示是否显示
  const errorVisible = ref(false)
  // 错误内容
  const errors = ref<Record<number, number>>({})

  useFormEffects(() => {
    onFormSubmit(() => {
      const newErrors:Record<number, number> = {}

      const errorIndexReg = new RegExp(`^${schema.value.name}\\.(\\d+)\\..+$`)
      form.value.errors.forEach(item => {
        if (item.messages?.length) {
          const index = item.path?.match(errorIndexReg)?.[1]
          if (/\d/.test(index)) {
            newErrors[index] = 1
          }
        }
      })
      errors.value = newErrors
      refreshKey.value += 1

      errorVisible.value = !!Object.keys(errors.value).filter(i => getLabel(Number(i)) !== active.value).length
    })
  })

  // 表单tabs
  const tabsData = computed(() => {
    const closeable = data.value.length > 1
    const settledData = data.value.map((_, i) => ({
      key: typeof _ === 'object' ? `${_.key}${i}` : `${_}${i}`,
      label: getLabel(i),
      closeable,
    }))
    return [
      ...settledData,
      {
        label: `新建${props.title}`,
        closeable: false,
        key: 'add',
      },
    ]
  })

  // 切换tabs
  const changeActive = (label:string) => {
    const isAdd = label === `新建${props.title}`
    // 新增tab
    if (isAdd) {
      data.value.push(({ ...props.defaultValue }))
      active.value = getLabel(data.value.length - 1)
      refreshKey.value += 1
    } else {
      active.value = label
    }
  }

  // 删除tab
  const close = (label:string) => {
    const i = tabsData.value.findIndex(item => item.label === label)
    if (i === tabsData.value.length - 1) {
      // 添加逻辑
      return
    }
    data.value.splice(i, 1)
    active.value = getLabel(Math.min(data.value.length - 1, i))
    refreshKey.value += 1
    emit('change', data.value)
  }

</script>

<style lang="stylus" scoped>
.error-wrap
  height: 46px
  width 100%
  padding 0 12px
  background: #FEEBEF
  display flex
  align-items center
  font-weight: 400
  font-size: 14px
  color rgba(0, 0, 0, 0.85)
  box-sizing border-box
.error-title
  padding 0 .5em
  flex 1
.tab-content
  padding 20px 0
</style>
