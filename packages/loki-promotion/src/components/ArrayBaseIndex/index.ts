import {
  computed, defineComponent, h, watchEffect,
} from 'vue'
import {
  observer,
  // @ts-ignore
  ArrayCards,
} from '@xhs/delight-formily'

const ArrayBaseIndex = observer(
  defineComponent({
    name: 'ArrayBaseIndex',
    inheritAttrs: false,
    props: {
      title: {
        type: String,
        default: '',
      },
    },
    emits: ['change'],
    setup(props, { attrs, emit }) {
      const idx = ArrayCards.useIndex()

      const title = computed(() => {
        const i = Number(idx.value) + 1
        return `${props.title} - ${i > 9 ? i : `0${i}`}`
      })

      watchEffect(
        () => {
          emit('change', title.value)
        },
      )

      return () => h(
        'span',
        {
          ...attrs,
          class: 'index',
          style: 'font-size:14px;font-weight:500',
        },
        [title.value],
      )
    },
  }),
)

export default ArrayBaseIndex
