<template>
  <div
    class="button-group"
    :style="{
      ...defaultStyle,
      ...style,
    }"
  >
    <Space>
      <Button
        v-for="({ label, ...btn }, i) in showButtons"
        :key="btn.key || i"
        v-bind="btn"
      >
        {{ label }}
      </Button>
    </Space>
  </div>
</template>

<script lang="tsx" setup>
  import { withDefaults, CSSProperties, computed } from 'vue'
  import { Space, Button } from '@xhs/delight'
  import type { ButtonContent } from '@xhs/delight'

  const defaultStyle = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    margin: '40px 0',
    backgroundColor: 'transparent',
  }

  const props = withDefaults(defineProps<{
    style?: CSSProperties
    buttons:(ButtonContent & { label: string; key?: string; hide?: boolean })[]
  }>(), {
    style: () => ({}),
  })
  const showButtons = computed(() => props.buttons.filter(el => !el.hide))
</script>
