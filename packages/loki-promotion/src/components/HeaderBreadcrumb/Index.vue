<template>
  <div class="header-breadcrumb">
    <Breadcrumb :items="items" />
    <div class="other">
      <slot />
    </div>
  </div>
</template>
<script lang="tsx" setup>
  import { withDefaults } from 'vue'
  import { Breadcrumb } from '@xhs/delight'

  // eslint-disable-next-line no-undef
  withDefaults(defineProps<{
    items: any[]
  }>(), {})

</script>

<style lang="stylus" scoped>
.header-breadcrumb {
  display: flex
  align-items: center
  justify-content: space-between
  margin-bottom: 20px
}
</style>
