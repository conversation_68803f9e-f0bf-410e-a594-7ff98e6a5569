<template>
  <div class="gird-outer">
    <div class="grid-layout">
      <slot />
      <div
        v-if="addText"
        class="add-card center-flex pointer"
        @click="add"
      >
        <div class="icon-wrap center-flex">
          <Icon
            :icon="ListAdd"
            color="text-description"
          />
        </div>
        <span class="add-text">{{ addText }}</span>
      </div>
    </div>
  </div>
</template>

<script lang="tsx" setup>
  import { Icon } from '@xhs/delight'
  import { ListAdd } from '@xhs/delight/icons'

  interface Props {
    addText?: string
  }

  withDefaults(
    defineProps<Props>(),
    {
      addText: '',
    },
  )

  const emit = defineEmits(['add'])

  const add = () => {
    emit('add')
  }

</script>

<style lang="stylus" scoped>
.gird-outer
  min-height 60vh
.grid-layout
  display: grid;
  justify-content: center;
  gap: 20px;
  grid-template-columns: repeat(4,1fr);
  @media screen and (max-width: 1980px)
    grid-template-columns: repeat(3,1fr);
  @media screen and (max-width: 1500px)
    grid-template-columns: repeat(2,1fr);
  @media screen and (max-width: 900px)
    grid-template-columns: repeat(1,1fr);
.add-card
  height 200px
  background white
  border-radius 8px
  font-weight: 500
  font-size 16px
  line-height 24px
.icon-wrap
  border-radius 50%
  background rgba(0, 0, 0, 0.08)
  margin-right 8px
  width 24px
  height 24px
</style>
