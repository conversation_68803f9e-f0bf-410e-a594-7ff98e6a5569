<template>
  <div class="simple-card">
    <div
      v-if="title"
      class="title"
    >{{ title }}</div>
    <slot />
  </div>
</template>

<script lang="tsx" setup>
  withDefaults(
    defineProps<{
      title?:string
      loading?: boolean
    }>(),
    {
      title: '',
      loading: false,
    },
  )
</script>

<style lang="stylus" scoped>
.simple-card
  background-color white
  padding 20px 24px
  border-radius 8px
  margin-bottom 20px
.title
  font-weight 500
  font-size 16px
  line-height 32px
</style>
