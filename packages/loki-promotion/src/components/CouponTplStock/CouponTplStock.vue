<template>
  <div>
    <div>
      <InputNumber
        v-model="stock"
        style="width:100%"
        :placeholder="placeholder"
        :min="1"
        :max="9999999"
        v-bind="$attrs"
        @input="changeStock"
      />
    </div>
    <span
      v-if="$attrs?.description"
      className="tips"
    >{{ $attrs?.description }}</span>
  </div>
</template>

<script lang="tsx" setup>
  import { ref, watch } from 'vue'
  import { InputNumber } from '@xhs/delight'

  import { debounce } from '~/utils/throttle-debounce'

  import { checkBenefitConfig } from '~/services/benefitPool'

  const props = withDefaults(
    defineProps<{
      placeholder: string
      templateId: string
      type?: number // 1: '薯券',2: '津贴',3: '无奖品',4: '积分',
      value?: number
    }>(),
    {
      placeholder: '请输入',
      type: 1,
      value: undefined,
    },
  )

  const emit = defineEmits(['change'])

  const loading = ref(false)
  const stock = ref(props.value)

  watch(
    () => props.value,
    () => {
      stock.value = props.value
    },
  )

  // 修改库存
  const changeStock = debounce(
    async () => {
      if (!stock.value || stock.value < 0) return
      if ([1, 2].includes(props.type) && props.templateId) {
        loading.value = true
        const res = await checkBenefitConfig({
          prizeType: props.type,
          templateId: props.templateId,
          store: stock.value,
        })
        emit('change', res.vaild ? stock.value : '0')
      } else {
        emit('change', stock.value)
      }
    },
  )

</script>
<style scoped>
.tips {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  line-height: 26px;
}
</style>
