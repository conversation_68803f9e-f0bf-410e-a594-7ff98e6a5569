/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 20467
  * @name: 获取全量场景列表及触发点列表
  * @identifier: api.marketing.bizscene_list.get
  * @version: undefined
  * @path: /api/marketing/bizscene_list
  * @method: get
  * @description: 获取全量场景列表及触发点列表
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'loki-shared/http'

export interface IGetBizsceneListPayload {
	/** 操作用户 ID */
	userID?: string
}

export interface IBizscene {
	/** 场景 id */
	id?: number
	/** 场景名称 */
	name?: string
	/** 场景下策略优先级 */
	strategyPriorityMap?: Record<number, number>
	/** 策略数量 */
	strategyCount?: number
	/** 支持配置的配置列表 */
	configItems?: string[]
}

export interface ITrigger {
	/** 触发点 id */
	id?: number
	/** 触发点名称 */
	name?: string
	/** 所属场景 */
	bizsceneID?: number
}

export interface IContainerConfig {
	/** 展示名称 */
	name?: string
	/** 唯一标识 */
	value?: string
	/** 容器支持的配置 */
	configItems?: string[]
	/** actionType */
	actionType?: string
}

export interface IData {
	/** 场景列表 */
	bizscenes?: IBizscene[]
	/** 触发点列表 */
	triggers?: ITrigger[]
	/** 容器列表 */
	containerConfigs?: IContainerConfig[]
}

export interface IGetBizsceneListResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function getBizsceneList(params: IGetBizsceneListPayload, options = {}): Promise<IData> {
  return http.get('/api/marketing/bizscene_list', { params, transform: false, ...options })
}
