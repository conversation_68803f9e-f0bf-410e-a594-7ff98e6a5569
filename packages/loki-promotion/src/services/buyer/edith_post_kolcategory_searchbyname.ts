/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 32810
  * @name: 买手池搜索（名称）
  * @identifier: api.kolcategory.searchbyname.post
  * @version: undefined
  * @path: /api/kolcategory/searchbyname
  * @method: post
  * @description: 买手池搜索（名称）
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'loki-shared/http'

export interface IPostKolcategorySearchbynamePayload {
	/** distributorName */
	distributorName: string
}

export interface IResult {
	/** success */
	success: boolean
	/** code */
	code?: number
	/** errorCode */
	errorCode?: string
	/** message */
	message?: string
}

export interface IDistributorList {
	/** kol分类买手信息id */
	kolCategoryUserId: number
	/** 买手id */
	distributorId: string
	/** 买手名称 */
	distributorName: string
	/** 买手地址 */
	distributorLocation?: string
	/** 买手主推类目 */
	mianGoodsFirstCategoryName?: string
	/** 买手内容类目 */
	taxonomynameTagSetOnline?: string
	/** 买手logo */
	distributorLogo?: string
	/** 买手账号粉丝 */
	fansNumAccum?: number
	/** 橱窗商品数 */
	itemCnt?: number
	/** 买手简介 */
	distributorDescription?: string
	/** 展示状态 0:不可展示，1:可展示 */
	showStatus?: number
}

export interface IData {
	/** 返回数据 */
	result: IResult
	/** 买手列表 */
	distributorList?: IDistributorList[]
}

export interface IPostKolcategorySearchbynameResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postKolcategorySearchbyname(payload: IPostKolcategorySearchbynamePayload, options = {}): Promise<IData> {
  return http.post('/api/kolcategory/searchbyname', payload, { transform: false, ...options })
}
