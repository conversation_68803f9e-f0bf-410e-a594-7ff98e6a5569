/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */
/**
	* @XHS_API_KIT-INFO
	*
	* @id: 32742
	* @name: 鹰眼买手后台分类更新
	* @identifier: api.kolcategory.kolcategory_update.put
	* @version: undefined
	* @path: /api/kolcategory/kolcategory_update
	* @method: put
	* @description: 鹰眼买手后台分类更新
	*
	* @XHS_API_KIT-INFO
*/

import http from 'loki-shared/http'

export interface IRequest {
	/** 分类ID */
	id: number
	/** 更新后的分类名称 */
	name: string
}

export interface IPutKolcategoryUpdatePayload {
	/** 更新KOL分类请求 */
	request: IRequest
}

export interface IResult {
	/** success */
	success: boolean
	/** code */
	code?: number
	/** errorCode */
	errorCode?: string
	/** message */
	message?: string
}

export interface IPutKolcategoryUpdateResponse {
	/** 返回数据 */
	result: IResult
	/** 更新成功标识 */
	success?: boolean
}

export function putKolcategoryUpdate(payload: IPutKolcategoryUpdatePayload, options = {}): Promise<void> {
	return http.put('/api/kolcategory/kolcategory_update', payload, { transform: false, ...options })
}
