/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */
/**
	* @XHS_API_KIT-INFO
	*
	* @id: 32804
	* @name: 鹰眼买手后台查询买手列表
	* @identifier: api.kolcategory.kolcategoryuser_query.post
	* @version: undefined
	* @path: /api/kolcategory/kolcategoryuser_query
	* @method: post
	* @description: 鹰眼买手后台查询买手列表
	*
	* @XHS_API_KIT-INFO
*/

import http from 'loki-shared/http'

export interface IRequest {
	/** 父节点分类id */
	parentCategoryId?: number
}

export interface IPostKolcategoryuserQueryPayload {
	/** request */
	request: IRequest
}

export interface IResult {
	/** success */
	success: boolean
	/** code */
	code?: number
	/** errorCode */
	errorCode?: string
	/** message */
	message?: string
}

export interface ISubCategoryList {
	/** 分类id */
	id: number
	/** 分类名称 */
	title?: string
	/** 分类优先级 */
	priority?: number
	/** 分类层级 */
	level?: number
	/** 父分类id */
	parentId?: number
	/** 子分类列表 */
	subCategoryList?: ISubCategoryList[]
	/** 当前分类下买手数量 */
	totalKolUserCnt?: number
}

export interface ICategoryList {
	/** 分类id */
	id: number
	/** 分类名称 */
	title?: string
	/** 分类优先级 */
	priority?: number
	/** 分类层级 */
	level?: number
	/** 父分类id */
	parentId?: number
	/** 子分类列表 */
	subCategoryList?: ISubCategoryList[]
	/** 当前分类下买手数量 */
	totalKolUserCnt?: number
}

export interface IResp {
	/** 返回数据 */
	result: IResult
	/** 结果数据 */
	categoryList?: ICategoryList[]
}

export interface IPostKolcategoryuserQueryResponse {
	/** 返回值code */
	resp?: IResp
}

export function postKolcategoryuserQuery(payload: IPostKolcategoryuserQueryPayload, options = {}): Promise<void> {
	return http.post('/api/kolcategory/kolcategoryuser_query', payload, { transform: false, extractData: false, ...options })
}
