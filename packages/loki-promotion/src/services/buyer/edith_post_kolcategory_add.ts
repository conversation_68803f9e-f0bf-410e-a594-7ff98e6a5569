/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */
/**
	* @XHS_API_KIT-INFO
	*
	* @id: 32741
	* @name: 鹰眼买手后台分类新增
	* @identifier: api.kolcategory.kolcategory_add.post
	* @version: undefined
	* @path: /api/kolcategory/kolcategory_add
	* @method: post
	* @description: 鹰眼买手后台分类新增
	*
	* @XHS_API_KIT-INFO
	*/

import http from 'loki-shared/http'

export interface IRequest {
	/** 分类名称 */
	name: string
	/** 分类层级：1-一级分类，2-二级分类 */
	level: number
	/** 父分类ID，只有2级类目需要传 */
	parentId?: number
}

export interface IPostKolcategoryAddPayload {
	/** request */
	request: IRequest
}

export interface IResult {
	/** success */
	success: boolean
	/** code */
	code?: number
	/** errorCode */
	errorCode?: string
	/** message */
	message?: string
}

export interface IPostKolcategoryAddResponse {
	/** 返回数据 */
	result: IResult
	/** 创建成功标识 */
	success?: boolean
}

export function postKolcategoryAdd(payload: IPostKolcategoryAddPayload, options = {}): Promise<void> {
	return http.post('/api/kolcategory/kolcategory_add', payload, { transform: false, ...options })
}
