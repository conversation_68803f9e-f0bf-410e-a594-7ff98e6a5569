/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */
/**
	* @XHS_API_KIT-INFO
	*
	* @id: 32740
	* @name: 鹰眼买手后台查询分类
	* @identifier: api.kolcategory.kolcategory_query.get
	* @version: undefined
	* @path: /api/kolcategory/kolcategory_query
	* @method: get
	* @description: 鹰眼买手后台查询分类
	*
	* @XHS_API_KIT-INFO
*/

import http from 'loki-shared/http'

export interface IGetKolcategoryQueryPayload {
	/** 父节点分类id */
	parentCategoryId?: number
}

export interface IResult {
	/** 请求是否成功 */
	success: boolean
	/** 返回值code */
	code?: number
	/** 错误码 */
	errorCode?: string
	/** 信息 */
	message?: string
}

export interface ISubCategoryList {
	/** 分类id */
	id: number
	/** 分类名称 */
	title?: string
	/** 分类优先级 */
	priority?: number
	/** 分类层级 */
	level?: number
	/** 父分类id */
	parentId?: number
	/** 子分类列表 */
	subCategoryList?: ISubCategoryList[]
	/** 当前分类下买手数量 */
	totalKolUserCnt?: number
}

export interface ICategoryList {
	/** 分类id */
	id: number
	/** 分类名称 */
	title?: string
	/** 分类优先级 */
	priority?: number
	/** 分类层级 */
	level?: number
	/** 父分类id */
	parentId?: number
	/** 子分类列表 */
	subCategoryList?: ISubCategoryList[]
	/** 当前分类下买手数量 */
	totalKolUserCnt?: number
}

export interface IGetKolcategoryQueryResponse {
	/** 返回数据 */
	result: IResult
	/** 结果数据 */
	categoryList?: ICategoryList[]
}

export function getKolcategoryQuery(params: IGetKolcategoryQueryPayload, options = {}): Promise<void> {
	return http.get('/api/kolcategory/kolcategory_query', { params, transform: false, extractData: false, ...options })
}
