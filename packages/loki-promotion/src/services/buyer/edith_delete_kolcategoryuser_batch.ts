/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 91420
  * @name: 鹰眼买手后台买手批量删除
  * @identifier: api.kolcategory.kolcategoryuser_batch_delete.delete
  * @version: 3
  * @path: /api/kolcategory/kolcategoryuser_batch_delete
  * @method: delete
  * @description: 鹰眼买手后台买手批量删除
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'loki-shared/http'

export interface IRequest {
	/** 买手分类用户信息id列表 */
	idList: number[]
}

export interface IDeleteKolcategoryuserBatchPayload {
	/** 批量删除买手分类用户信息请求 */
	request: IRequest
}

export interface IResult {
	/** success */
	success: boolean
	/** code */
	code?: number
	/** errorCode */
	errorCode?: string
	/** message */
	message?: string
}

export interface IResp {
	/** result */
	result: IResult
	/** 是否成功 */
	success?: boolean
	/** 删除的数量 */
	deletedCount?: number
}

export interface IDeleteKolcategoryuserBatchResponse {
	/** 批量删除买手分类用户信息响应 */
	resp?: IResp
}

export function deleteKolcategoryuserBatch(params: IDeleteKolcategoryuserBatchPayload, options = {}): Promise<void> {
  return http.del('/api/kolcategory/kolcategoryuser_batch_delete', { params, transform: false, ...options })
}
