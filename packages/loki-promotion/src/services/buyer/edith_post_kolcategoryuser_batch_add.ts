/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 91414
  * @name: 鹰眼买手后台买手批量新增
  * @identifier: api.kolcategory.kolcategoryuser_batch_add.post
  * @version: 2
  * @path: /api/kolcategory/kolcategoryuser_batch_add
  * @method: post
  * @description: 鹰眼买手后台买手批量新增
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'loki-shared/http'

export interface IPostKolcategoryuserBatchAddPayload {
	/** 分类id */
	kolCategoryId: number
	/** 分类层级，本次写死2即可 */
	level: number
	/** 买手id,逗号分隔，支持批量导入 */
	distributorIdList: string
}

export interface IResult {
	/** success */
	success: boolean
	/** code */
	 code?: number
	/** errorCode */
	errorCode?: string
	/** message */
	message?: string
}

export interface IPostKolcategoryuserBatchAddResponse {
	/** 返回数据 */
	result: IResult
	/** 请求是否成功 */
	success?: boolean
}

export function postKolcategoryuserBatchAdd(payload: IPostKolcategoryuserBatchAddPayload, options = {}): Promise<void> {
  return http.post('/api/kolcategory/kolcategoryuser_batch_add', payload, { transform: false, ...options })
}
