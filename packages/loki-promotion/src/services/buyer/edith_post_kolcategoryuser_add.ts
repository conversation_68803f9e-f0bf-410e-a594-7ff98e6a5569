/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 91413
  * @name: 鹰眼买手后台买手新增
  * @identifier: api.kolcategory.kolcategoryuser_add.post
  * @version: 2
  * @path: /api/kolcategory/kolcategoryuser_add
  * @method: post
  * @description: 鹰眼买手后台买手新增
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'loki-shared/http'

export interface IPostKolcategoryuserAddPayload {
	/** 用户id */
	userId: string
	/** 用户名称 */
	name: string
	/** 买手简介 */
	kolDesc?: string
	/** 一级买手分类id */
	firstKolCategoryId: number
	/** 买手分类id */
	kolCategoryId: number
}

export interface IResult {
	/** success */
	success?: boolean
	/** code */
	code?: number
	/** errorCode */
	errorCode?: string
	/** message */
	message?: string
}

export interface IPostKolcategoryuserAddResponse {
	/** 返回数据 */
	result: IResult
	/** 请求是否成功 */
	success: boolean
	/** 创建后的买手分类用户信息id */
	kolCategoryUserId?: number
}

export function postKolcategoryuserAdd(payload: IPostKolcategoryuserAddPayload, options = {}): Promise<void> {
  return http.post('/api/kolcategory/kolcategoryuser_add', payload, { transform: false, ...options })
}
