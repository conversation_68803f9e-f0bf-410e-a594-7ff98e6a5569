/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 32752
  * @name: 鹰眼买手后台买手编辑
  * @identifier: api.kolcategory.kolcategoryuser_edit.post
  * @version: undefined
  * @path: /api/kolcategory/kolcategoryuser_edit
  * @method: post
  * @description: 鹰眼买手后台买手编辑
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'loki-shared/http'

export interface IPostKolcategoryuserEditPayload {
	/** kol分类买手信息id */
	kolCategoryUserId: number
	/** 买手id */
	distributorId: string
	/** 买手简介，不超过24个字 */
	distributorDescription?: string
}

export interface IResult {
	/** success */
	success: boolean
	/** code */
	code?: number
	/** errorCode */
	errorCode?: string
	/** message */
	message?: string
}

export interface IPostKolcategoryuserEditResponse {
	/** 返回数据 */
	result: IResult
	/** 请求是否成功 */
	success?: boolean
}

export function postKolcategoryuserEdit(payload: IPostKolcategoryuserEditPayload, options = {}): Promise<void> {
  return http.post('/api/kolcategory/kolcategoryuser_edit', payload, { transform: false, ...options })
}
