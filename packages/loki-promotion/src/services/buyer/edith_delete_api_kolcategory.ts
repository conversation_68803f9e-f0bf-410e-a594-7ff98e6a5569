/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 32753
  * @name: 鹰眼买手后台分类删除
  * @identifier: api.kolcategory.kolcategory_delete.delete
  * @version: undefined
  * @path: /api/kolcategory/kolcategory_delete
  * @method: delete
  * @description: 鹰眼买手后台分类删除
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'loki-shared/http'

export interface IDeleteApiKolcategoryPayload {
	/** 分类ID */
	id: number
}

export interface IResult {
	/** success */
	success: boolean
	/** code */
	code?: number
	/** errorCode */
	errorCode?: string
	/** message */
	message?: string
}

export interface IDeleteApiKolcategoryResponse {
	/** 返回值code */
	result: IResult
	/** 请求是否成功 */
	success?: boolean
}

export function deleteApiKolcategory(params: IDeleteApiKolcategoryPayload, options = {}): Promise<void> {
  return http.del('/api/kolcategory/kolcategory_delete', { params, transform: false, ...options })
}
