/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */
/**
	* @XHS_API_KIT-INFO
	*
	* @id: 32806
	* @name: 鹰眼买手后台买手池搜索
	* @identifier: api.kolcategory.searchbyid.post
	* @version: undefined
	* @path: /api/kolcategory/searchbyid
	* @method: post
	* @description: 鹰眼买手后台买手池搜索
	*
	* @XHS_API_KIT-INFO
*/

import http from 'loki-shared/http'

export interface IRequest {
	/** 买手分类id列表 */
	distributorIdList: string[]
}

export interface IPostKolcategorySearchbyidPayload {
	/** base.Context */
	/** 买手池搜索请求 */
	request: IRequest
}

export interface IResult {
	/** success */
	success: boolean
	/** code */
	code?: number
	/** errorCode */
	errorCode?: string
	/** message */
	message?: string
}

export interface IDistributorList {
	/** kol分类买手信息id */
	kolCategoryUserId: number
	/** 买手id */
	distributorId: string
	/** 买手名称 */
	distributorName: string
	/** 买手地址 */
	distributorLocation?: string
	/** 买手主推类目 */
	mianGoodsFirstCategoryName?: string
	/** 买手内容类目 */
	taxonomynameTagSetOnline?: string
	/** 买手logo */
	distributorLogo?: string
	/** 买手账号粉丝 */
	fansNumAccum?: number
	/** 橱窗商品数 */
	itemCnt?: number
	/** 买手简介 */
	distributorDescription?: string
	/** 展示状态 0:不可展示，1:可展示 */
	showStatus?: number
}

export interface IResp {
	/** 返回数据 */
	result: IResult
	/** 买手列表 */
	distributorList?: IDistributorList[]
}

export interface IPostKolcategorySearchbyidResponse {
	/** ========== 买手搜索相关响应 ==========/ 买手池搜索响应 */
	resp?: IResp
}

export function postKolcategorySearchbyid(payload: IPostKolcategorySearchbyidPayload, options = {}): Promise<void> {
	return http.post('/api/kolcategory/searchbyid', payload, { transform: false, extractData: false, ...options })
}
