/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 32789
  * @name: 鹰眼买手后台分类顺序调整
  * @identifier: api.kolcategory.kolcategory_adjust.post
  * @version: undefined
  * @path: /api/kolcategory/kolcategory_adjust
  * @method: post
  * @description: 鹰眼买手后台分类顺序调整

  * 
  * @XHS_API_KIT-INFO
*/

import http from 'loki-shared/http'

export interface IRequest {
	/** 分类id排序列表 */
	idIndexList: number[]
	/** 分类层级：1-一级分类，2-二级分类 */
	level: number
	/** 父分类ID，只有2级类目需要传 */
	parentId?: number
}

export interface IPostKolcategoryAdjustPayload {
	/** 排序KOL分类请求 */
	request: IRequest
}

export interface IResult {
	/** success */
	success: boolean
	/** code */
	code?: number
	/** errorCode */
	errorCode?: string
	/** message */
	message?: string
}

export interface IPostKolcategoryAdjustResponse {
	/** 返回值code */
	result: IResult
	/** 请求是否成功 */
	success?: boolean
}

export function postKolcategoryAdjust(payload: IPostKolcategoryAdjustPayload, options = {}): Promise<void> {
  return http.post('/api/kolcategory/kolcategory_adjust', payload, { transform: false, ...options })
}
