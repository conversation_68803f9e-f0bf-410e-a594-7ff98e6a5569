/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 91417
  * @name: 鹰眼买手后台买手删除
  * @identifier: api.kolcategory.kolcategoryuser_delete.delete
  * @version: 2
  * @path: /api/kolcategory/kolcategoryuser_delete
  * @method: delete
  * @description: 鹰眼买手后台买手删除
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'loki-shared/http'

export interface IDeleteKolcategoryKolcategoryuserPayload {
	/** 买手分类用户信息id */
	id: number
}

export interface IResult {
	/** success */
	success: boolean
	/** code */
	code?: number
	/** errorCode */
	errorCode?: string
	/** message */
	message?: string
}

export interface IDeleteKolcategoryKolcategoryuserResponse {
	/** 返回数据 */
	result: IResult
	/** 请求是否成功 */
	success?: boolean
}

export function deleteKolcategoryKolcategoryuser(params: IDeleteKolcategoryKolcategoryuserPayload, options = {}): Promise<void> {
  return http.del('/api/kolcategory/kolcategoryuser_delete', { params, transform: false, ...options })
}
