/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 54269
  * @name: 统一频控列表/查询
  * @identifier: api.usergrowth.frequency_rule.query.post
  * @version: undefined
  * @path: /api/usergrowth/frequency_rule/query
  * @method: post
  * @description: 
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'loki-shared/http'

export interface IPostFrequencyRuleQueryPayload {
	/** 名称 */
	name: string
}

export interface IExtra {
	/** 1-强制更新当前rn并重新打开，2-在ios跳转小红书AppStore下载，在android同1效果 */
	forceUpdate?: number
	/** 强制更新过程中，展示此信息 */
	forceMessage?: string
}

export interface IResponse {
	/** 是否成功 */
	success: boolean
	/** 返回信息 */
	msg?: string
	/** 返回编码 */
	code?: number
	/** 额外信息，rn强制升级等 */
	extra?: IExtra
}

export interface ITriggerFrequency {
	/** 时间单位 */
	unitType?: string
	/** 窗口长度 */
	unitNum?: number
	/** 允许次数 */
	allowNum?: number
}

export interface INegativeFrequency {
	/** 时间单位 */
	unitType?: string
	/** 窗口长度 */
	unitNum?: number
	/** 允许次数 */
	allowNum?: number
}

export interface IData {
	/** 通用返回 */
	response: IResponse
	/** 频控 id */
	id?: number
	/** 频控名称 */
	name?: string
	/** 频控范围 */
	range?: string
	/** 频控维度 */
	dimension?: string
	/** 触发频控配置 */
	triggerFrequency?: ITriggerFrequency[]
	/** 领取频控配置 */
	negativeFrequency?: INegativeFrequency[]
}

export interface IPostFrequencyRuleQueryResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postFrequencyRuleQuery(payload: IPostFrequencyRuleQueryPayload, options = {}): Promise<IData> {
  return http.post('/api/usergrowth/frequency_rule/query', payload, { transform: false, ...options })
}
