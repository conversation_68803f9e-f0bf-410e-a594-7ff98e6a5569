/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */
/**
	* @XHS_API_KIT-INFO
	* 
	* @id: 54278
	* @name: 统一优先级编辑
	* @identifier: api.usergrowth.prority_rule.edit.post
	* @version: undefined
	* @path: /api/usergrowth/prority_rule/edit
	* @method: post
	* @description: 统一优先级编辑，修改排序
	* 
	* @XHS_API_KIT-INFO
*/

import http from 'loki-shared/http'

export interface IPostProrityRuleEditPayload {
	/** id */
	prorityRuleId?: number
	/** 操作人 */
	order?: number
}

export interface IExtra {
	/** 1-强制更新当前rn并重新打开，2-在ios跳转小红书AppStore下载，在android同1效果 */
	forceUpdate?: number
	/** 强制更新过程中，展示此信息 */
	forceMessage?: string
}

export interface IResponse {
	/** 是否成功 */
	success: boolean
	/** 返回信息 */
	msg?: string
	/** 返回编码 */
	code?: number
	/** 额外信息，rn强制升级等 */
	extra?: IExtra
}

export interface IData {
	/** 处理结果 */
	response: IResponse
}

export interface IPostProrityRuleEditResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postProrityRuleEdit(payload: IPostProrityRuleEditPayload, options = {}): Promise<IData> {
	return http.post('/api/usergrowth/prority_rule/edit', payload, { transform: false, ...options })
}
