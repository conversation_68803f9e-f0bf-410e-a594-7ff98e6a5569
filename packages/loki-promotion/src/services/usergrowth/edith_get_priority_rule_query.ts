/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 54437
  * @name: 统一优先级列表/查询
  * @identifier: api.usergrowth.priority_rule.query.get
  * @version: undefined
  * @path: /api/usergrowth/priority_rule/query
  * @method: get
  * @description: 
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'loki-shared/http'

export interface IGetPriorityRuleQueryPayload {
	/** 优先级维度 */
	dimension?: string
	/** 场景 id */
	bizsceneId?: number
	/** 时机 id */
	triggerId?: number
}

export interface IExtra {
	/** 1-强制更新当前rn并重新打开，2-在ios跳转小红书AppStore下载，在android同1效果 */
	forceUpdate?: number
	/** 强制更新过程中，展示此信息 */
	forceMessage?: string
}

export interface IResponse {
	/** 是否成功 */
	success: boolean
	/** 返回信息 */
	msg?: string
	/** 返回编码 */
	code?: number
	/** 额外信息，rn强制升级等 */
	extra?: IExtra
}

export interface IRuleList {
	/** 统一优先级名称 */
	name?: string
	/** 优先级序号 */
	order?: number
	/** 优先级维度 */
	dimension?: string
	/** 场景 id */
	bizsceneId?: number
	/** 时机 id */
	triggerId?: number
	/** 实例 id */
	instanceId?: number
	/** 实例名称 */
	instanceName?: string
	/** 创建人 */
	creator?: string
}

export interface IData {
	/** 通用返回 */
	response: IResponse
	/** 具体对象信息 */
	ruleList?: IRuleList[]
}

export interface IGetPriorityRuleQueryResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function getPriorityRuleQuery(params: IGetPriorityRuleQueryPayload, options = {}): Promise<IData> {
  return http.get('/api/usergrowth/priority_rule/query', { params, transform: false, ...options })
}
