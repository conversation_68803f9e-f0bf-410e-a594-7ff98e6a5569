/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 54237
  * @name: 查看投放方案
  * @identifier: api.usergrowth.launch_project.get.post
  * @version: undefined
  * @path: /api/usergrowth/launch_project/get
  * @method: post
  * @description: 查看投放方案
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'loki-shared/http'

export interface IPostLaunchProjectGetPayload {
	/** 操作用户 ID */
	userID?: string
	/** 操作用户 Name */
	userName?: string
	/** 投放计划 */
	id?: number
}

export interface IExtra {
	/** 1-强制更新当前rn并重新打开，2-在ios跳转小红书AppStore下载，在android同1效果 */
	forceUpdate?: number
	/** 强制更新过程中，展示此信息 */
	forceMessage?: string
}

export interface IResponse {
	/** 响应结果 */
	success: boolean
	/** 信息 */
	msg?: string
	/** 响应码 */
	code?: number
	/** 额外信息，rn强制升级等 */
	extra?: IExtra
}

export interface ILaunchProject {
	/** 主键 */
	id?: number
	/** 名称 */
	name?: string
	/** 投放方案类型 */
	projectType?: string
	/** 投放方案信息 json格式 */
	projectInfo?: string
	/** 场景ID */
	bizSceneId?: number
	/** 触点ID */
	triggerId?: number
	/** 频控ID */
	frequencyIds?: number[]
	/** 状态 */
	status?: number
	/** 开始时间 */
	startAt?: number
	/** 结束时间 */
	endAt?: number
	/** 创建人 */
	createdBy?: string
	/** 创建时间 */
	createdAt?: number
	/** 更新时间 */
	updatedAt?: number
	/** 更新人 */
	updatedBy?: string
	/** 环境 */
	env?: string
}

export interface IData {
	/** 处理结果 */
	response: IResponse
	/** 投放方案 */
	launchProject?: ILaunchProject
}

export interface IPostLaunchProjectGetResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postLaunchProjectGet(payload: IPostLaunchProjectGetPayload, options = {}): Promise<IData> {
  return http.post('/api/usergrowth/launch_project/get', payload, { transform: false, ...options })
}
