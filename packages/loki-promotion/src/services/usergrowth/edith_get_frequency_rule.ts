/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 54313
  * @name: 查看统一频控详情
  * @identifier: api.usergrowth.frequency_rule.get.get
  * @version: undefined
  * @path: /api/usergrowth/frequency_rule/get
  * @method: get
  * @description: 
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'loki-shared/http'

export interface IGetFrequencyRulePayload {
	/** id */
	frequencyRuleId: number
}

export interface IExtra {
	/** 1-强制更新当前rn并重新打开，2-在ios跳转小红书AppStore下载，在android同1效果 */
	forceUpdate?: number
	/** 强制更新过程中，展示此信息 */
	forceMessage?: string
}

export interface IResponse {
	/** 是否成功 */
	success: boolean
	/** 返回信息 */
	msg?: string
	/** 返回编码 */
	code?: number
	/** 额外信息，rn强制升级等 */
	extra?: IExtra
}

export interface ITriggerFrequency {
	/** 时间单位 */
	unitType?: string
	/** 窗口长度 */
	unitNum?: number
	/** 允许次数 */
	allowNum?: number
}

export interface IClaimFrequency {
	/** 时间单位 */
	unitType?: string
	/** 窗口长度 */
	unitNum?: number
	/** 允许次数 */
	allowNum?: number
}

export interface IFrequencyRule {
	/** 频控 id */
	id?: number
	/** 频控名称 */
	name?: string
	/** 频控范围 */
	range?: string
	/** 频控维度 */
	dimension?: string
	/** 触发频控配置 */
	triggerFrequency?: ITriggerFrequency[]
	/** 领取频控配置 */
	claimFrequency?: IClaimFrequency[]
}

export interface IData {
	/** 处理结果 */
	response: IResponse
	/** 具体对象信息 */
	frequencyRule?: IFrequencyRule
}

export interface IGetFrequencyRuleResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function getFrequencyRule(params: IGetFrequencyRulePayload, options = {}): Promise<IData> {
  return http.get('/api/usergrowth/frequency_rule/get', { params, transform: false, ...options })
}
