/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 54233
  * @name: marketingintegration-service-default
  * @identifier: api.usergrowth.launch_project.delete.post
  * @version: undefined
  * @path: /api/usergrowth/launch_project/delete
  * @method: post
  * @description: 下线投放方案
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'loki-shared/http'

export interface IPostLaunchProjectDeletePayload {
	/** 主键id */
	id?: number
	/** 操作人 */
	userName?: string
}

export interface IExtra {
	/** 1-强制更新当前rn并重新打开，2-在ios跳转小红书AppStore下载，在android同1效果 */
	forceUpdate?: number
	/** 强制更新过程中，展示此信息 */
	forceMessage?: string
}

export interface IResponse {
	/** 响应结果 */
	success: boolean
	/** 信息 */
	msg?: string
	/** 响应码 */
	code?: number
	/** 额外信息，rn强制升级等 */
	extra?: IExtra
}

export interface IData {
	/** 处理结果 */
	response: IResponse
}

export interface IPostLaunchProjectDeleteResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postLaunchProjectDelete(payload: IPostLaunchProjectDeletePayload, options = {}): Promise<IData> {
  return http.post('/api/usergrowth/launch_project/delete', payload, { transform: false, ...options })
}
