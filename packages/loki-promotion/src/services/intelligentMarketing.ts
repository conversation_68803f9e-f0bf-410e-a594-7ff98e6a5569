import http from 'loki-shared/http'
import {
  ICreateFrequencyGroup, IServerStrategyInfo, IDavidCrowd, IDavidCrowdReq,
  IPromotionCycle, IPromotionCycleListParams, IPromotionCycleListResponse, IPromotionCycleDetailParams,
} from '~/types/intelligentMarketing'

const {
  get, post, put,
} = http

/** 查询单条策略 */
export function getStrategy(resourceParams: { strategyId: number }): Promise<any> {
  return get('STRATEGY_CONTENT', { resourceParams })
}

/** 查询策略列表 */
export function getStrategyList(params: {
  nameLike?: string
  bizscenID?: number
  status?: string
  expFlags?: string
  labels?: string
  minStartAt?: number
  maxStartAt?: number
  minEndAt?: number
  maxEndAt?: number
  page: number
  pageSize: number
  version?: string | number
}): Promise<any> {
  return get('STRATEGY_LIST', { params }, {
    transform: false,
  })
}

/** 查询策略列表 */
export function getStrategyListInFrequencyGroup(params: {
  nameLike?: string
  bizscenID?: number
  status?: string
  expFlags?: string
  labels?: string
  minStartAt?: number
  maxStartAt?: number
  minEndAt?: number
  maxEndAt?: number
  page: number
  pageSize: number
  version?: string | number
}): Promise<any> {
  return get('STRATEGY_LIST_In_Frequency_Group', { params }, {
    transform: false,
  })
}

/** 创建策略 */
export function createStrategy(payload: { strategy: IServerStrategyInfo }): Promise<any> {
  return post('STRATEGY_CREATE', payload, {
    transform: false,
    extractData: false,
  })
}

/** 批量创建策略 */
export function batchCreateStrategy(payload): Promise<any> {
  return post('BATCH_STRATEGY_CREATE', payload, {
    transform: false,
    extractData: false,
  })
}

/** 更新策略 */
export function updateStrategy(resourceParams: { strategyId: number }, payload: { strategy: IServerStrategyInfo }): Promise<any> {
  return put('STRATEGY_UPDATE', payload, {
    resourceParams,
    transform: false,
    extractData: false,
  })
}

/** 更新策略状态 */
export function updateStrategyStatus(resourceParams: { strategyId: number }, payload: { status: 'ONLINE' | 'OFFLINE' | 'DELETED' }): Promise<any> {
  return put('STRATEGY_STATUS_UPDATE', payload, {
    resourceParams,
    transform: false,
    extractData: false,
  })
}

/** 查询频控组列表 */
export function getFrequencyList(params: {
  page: number
  size: number
  type: string
}): Promise<any> {
  return get('FREQUENCY_LIST', {
    params,
  })
}

/** 创建频控组 */
export function createFrequency(payload: { frequencyGroup: ICreateFrequencyGroup }): Promise<any> {
  return post('FREQUENCY_CREATE', payload, {
    transform: false,
    extractData: false,
  })
}

/** 更新频控组 */
export function updateFrequency(resourceParams: { frequencyGroupId: number }, payload: { frequencyGroup: ICreateFrequencyGroup }): Promise<any> {
  return put('FREQUENCY_UPDATE', payload, {
    resourceParams,
    transform: false,
    extractData: false,
  })
}

/** 频控组点查接口 */
export function getFrequencyGroup(resourceParams: { frequencyGroupId: number }): Promise<any> {
  return get('FREQUENCY_GROUP_CONTENT', {
    resourceParams,
  })
}

/** 获取全量场景列表及触发点列表 */
export function getBizSceneList(): Promise<any> {
  return get('BIZSCENE_LIST', {
    params: {
      limitStrategyVersion: 0,
    },
  },
  {
    transform: false,
  })
}
/** 获取全量场景列表及触发点列表V2 */
export function getBizSceneListV2(frontierType): Promise<any> {
  return get('BIZSCENE_LIST', {
    params: {
      limitStrategyVersion: 1,
      frontierType,
    },
  },
  {
    transform: false,
  })
}

/** 获取场景下策略优先级 */
export function getStrategyPriorityByScene(resourceParams: { bizsceneId: number }): Promise<any> {
  return get('STRATEGY_PRIORITY', {
    resourceParams,
  })
}

/** 更新场景下策略优先级 */
export function updateStrategyPriority(
  payload: {
    bizscene: {
      strategyPriorityMap: Record<string, number>
      id: number
    }
  },
  resourceParams: { bizsceneId: number },
): Promise<any> {
  return put('STRATEGY_PRIORITY_UPDATE', payload, {
    resourceParams,
    transform: false,
    extractData: false,
  })
}

/** 获取可选容器列表 */
export function getContainerList(): Promise<any> {
  return post('CONTAINER_LIST', {})
}

/** 创建人群包 */
export function createCrowd(payload: {
  name: string
  userLifeCycles: string[]
  davidCrowdIds: string[]
}): Promise<any> {
  return post('CROWD_CREATE', payload, {
    extractData: false,
  })
}

/** 查询人群包 */
export function searchCrowd(params: { query: string }): Promise<any> {
  return get('CROWD_SEARCH', {
    params,
  })
}

/** 查询ECRM人群包 & 人群规则 */
export function getECRMCrowd(params: {
  crowdName: string
  queryType: 1 | 2
}): Promise<any> {
  return get('ECRM_CROWD', {
    params,
    transform: false,
  })
}

/** 查询David人群包 */
export function getDavidCrowd(params: IDavidCrowdReq): Promise<{ davidCrowds?: IDavidCrowd[] }> {
  return get('DAVID_CROWD', {
    params,
    transform: true,
  })
}

/** 获取策略条件项信息 */
export function getConditionInfo(payload: {
  bizsceneId: number
  triggerId: number
  byTriggerManage?: boolean
}): Promise<any> {
  return post('CONDITION_INFO', payload, {
    transform: false,
  })
}

/** 获取策略条件项信息 */
export function getConditionInfoV2(payload: {
  bizsceneId?: number
  triggerId?: number
  byTriggerManage: boolean
}): Promise<any> {
  return post('CONDITION_INFO_V2', payload, {
    transform: false,
  })
}

/** 查询算法映射规则列表 */
export function getFuncRulesList(params: {
  page: number
  pageSize: number
}): Promise<any> {
  return get('FUNC_RULES_LIST', { params }, {
    transform: false,
  })
}

/** 获取可选标签信息 */
export function getLabelInfo(): Promise<any> {
  return post('LABEL_INFO', {}, {
    transform: false,
  })
}

/** 获取容器列表 */
export function getContainerInfo(payload: {
  bizsceneId: number
  triggerId: number
}): Promise<any> {
  return post('CONTAINER_INFO', payload, {
    transform: false,
  })
}

/** 获取容器列表 */
export function getTriggerInfo(payload: {
  bizsceneId: number
  bizsceneConfig: {
    noteContentType: string[]
    noteSource: string[]
    videoNoteFeedSource: string[]
  }
}): Promise<any> {
  return post('TRIGGER_INFO', payload, {
    transform: false,
  })
}
/** 创建算法映射规则 */
export function createChooseRule(payload): Promise<any> {
  return post('CHOOSE_RULE_CREATE', payload, {
    transform: false,
    extractData: false,
  })
}

/** 更新算法映射规则 */
export function updateChooseRule(payload): Promise<any> {
  return put('CHOOSE_RULE_UPDATE', payload, {
    transform: false,
    extractData: false,
  })
}

/** 查询策略模版列表 */
export function getFuncTemplatesList(params: {
  page: number
  pageSize: number
}): Promise<any> {
  return get('FUNC_TEMPLATES_LIST', { params }, {
    transform: false,
  })
}

/** 创建策略模版 */
export function createFuncTemplate(payload) {
  return post('CREATE_FUNC_TEMPLATE', payload, {
    transform: false,
  })
}

/** 根据模版创建策略 */
export function createStrategyByTemplate(payload) {
  return post('CREATE_STRATEGY__BY_TEMPLATE', payload, {
    transform: false,
    extractData: false,
  })
}

/** 创建策略组 */
export function createStrategyGroup(payload) {
  return post('CREATE_STRATEGY_GROUP', payload, {
    transform: false,
    extractData: false,
  })
}

/** 策略组列表 */
export function getStrategyGroupList(params) {
  return get('GET_STRATEGY_GROUP_LIST', { params }, {
    transform: false,
  })
}

/** 根据id查询策略组 */
export function getStrategyGroup(resourceParams: { id: number }): Promise<any> {
  return get('GET_STRATEGY_GROUP', { resourceParams })
}

/** 预览策略 */
export function getStrategyPreview(payload): Promise<any> {
  return post('GET_STRATEGY_PREVIEW', payload, {
    transform: false,
    extractData: false,
  })
}

/** 提交策略组生成策略 */
export function strategyGroupSubmit(payload) {
  return post('STRATEGY_GROUP_SUBMIT', payload, {
    transform: false,
    extractData: false,
  })
}

/** 更新策略组状态 */
export function updateStrategyGroupStatus(payload): Promise<any> {
  return put('UPDATE_STRATEGY_GROUP_STATUS', payload, {
    transform: false,
    extractData: false,
  })
}

/** 删除策略组 */
export function deleteStrategyGroup(payload): Promise<any> {
  return put('DELETE_STRATEGY_GROUP', payload, {
    transform: false,
    extractData: false,
  })
}

/** 获取优惠券信息 */
export function getCouponInfo(params) {
  return get('COUPON_TEMPLATE_SEARCH', { params, withCredentials: true }, {
    transform: true,
  })
}

/** 查询条件规则列表 */
export function getConditionRuleList(payload: {
  pageNum: number
  pageSize: number
  nameLike?: string
  ruleEntityType?: string
}): Promise<any> {
  return post('CONDITION_RULE_LIST', payload, {
    transform: false,
    extractData: false,
  })
}

/** 创建条件规则 */
export function createConditionRule(payload): Promise<any> {
  return post('CREATE_CONDITION_RULE', payload, {
    transform: false,
    extractData: false,
  })
}

/** 更新条件规则 */
export function updateConditionRule(resourceParams, payload): Promise<any> {
  return post('UPDATE_CONDITION_RULE', payload, {
    resourceParams,
    transform: false,
    extractData: false,
  })
}

/** 下线条件规则 */
export function offlineConditionRule(resourceParams: {
  conditionRuleId: number
}): Promise<any> {
  return post('OFFLINE_CONDITION_RULE', {}, {
    resourceParams,
    transform: false,
    extractData: false,
  })
}

/** 获取条件规则详情 */
export function getConditionRuleDetail(resourceParams: {
  conditionRuleId: number
}): Promise<any> {
  return get('CONDITION_RULE_ITEM', {}, {
    resourceParams,
    transform: false,
    extractData: false,
  })
}

/** 查询筛选规则列表 */
export function getFilterRuleList(payload: {
  pageNum: number
  pageSize: number
  nameLike?: string
  ruleEntityType?: string
}): Promise<any> {
  return post('FILTER_RULE_LIST', payload, {
    transform: false,
    extractData: false,
  })
}

/** 创建筛选规则 */
export function createFilterRule(payload): Promise<any> {
  return post('CREATE_FILTER_RULE', payload, {
    transform: false,
    extractData: false,
  })
}

/** 更新筛选规则 */
export function updateFilterRule(resourceParams, payload): Promise<any> {
  return post('UPDATE_FILTER_RULE', payload, {
    resourceParams,
    transform: false,
    extractData: false,
  })
}

/** 下线筛选规则 */
export function offlineFilterRule(resourceParams: {
  filterRuleId: number
}): Promise<any> {
  return post('OFFLINE_FILTER_RULE', {}, {
    resourceParams,
    transform: false,
    extractData: false,
  })
}

/** 获取筛选规则详情 */
export function getFilterRuleDetail(resourceParams: {
  filterRuleId: number
}): Promise<any> {
  return get('FILTER_RULE_ITEM', {}, {
    resourceParams,
    transform: false,
    extractData: false,
  })
}

/** 查询投放计划 */
export function getLaunchPlanDetail(params): Promise<any> {
  return get('/api/usergrowth/launchplan', {
    params,
  }, {
    transform: false,
    // baseURL:'http://local.xiaohongshu.com:8003'
  })
}

/** 创建投放计划 */
export function createLaunchPlan(payload): Promise<any> {
  return post('/api/usergrowth/launchplan', payload, {
    transform: false,
    // baseURL:'http://local.xiaohongshu.com:8003'
  })
}

/** 下线投放计划 */
export function offlineLaunchPlan(payload): Promise<any> {
  return post('/api/usergrowth/launchplan/offline', payload, {
    transform: false,
    // baseURL:'http://local.xiaohongshu.com:8003'
  })
}

/** 删除投放计划 */
export function delLaunchPlan(payload): Promise<any> {
  return post('LAUNCHPLAN_DELETE', payload, {
    transform: false,
    // baseURL:'http://local.xiaohongshu.com:8003'
  })
}

/** 复制投放计划 */
export function copyLaunchPlan(payload): Promise<any> {
  return post('/api/usergrowth/launchplan/copy', payload, {
    transform: false,
    // baseURL:'http://local.xiaohongshu.com:8003'
  })
}

/** 编辑投放计划 */
export function editLaunchPlan(payload): Promise<any> {
  return put('/api/usergrowth/launchplan', payload, {
    transform: false,
    // baseURL:'http://local.xiaohongshu.com:8003'
  })
}

/** 获取买手直播容器表单信息 */
export function getBuyerLiveTable(params: {
  pageNum: number
  groupId: string
}): Promise<any> {
  return get('GET_BUYER_LIVE_TABLE', { params }, {
    transform: false,
    extractData: false,
  })
}

/** 策略-玩法召回查询 */
export function getStrategyRecall(payload): Promise<any> {
  return post('/api/usergrowth/strategy/playcenter', payload, {
    transform: false,
  })
}

/** 查询资源管理列表 */
export function getResourceManagementList(params): Promise<any> {
  return get('RESOURCE_MANAGEMENT_LIST', {}, {
    params,
    transform: false,
    extractData: false,
  })
}
/** 编辑资源管理列表 */
export function postResourceUpsert(payload): Promise<any> {
  return post('RESOURCE_UPSERT', payload, {
    transform: false,
    extractData: false,
  })
}

/** 校验实验 */
export function checkExperientConfig(payload): Promise<any> {
  return post('/api/usergrowth/checkmarketingconfig', payload, {
    transform: false,
    // baseURL:'http://local.xiaohongshu.com:8003'
  })
}

/** 策略debug */
export function strategyDebugQuery(payload): Promise<any> {
  return post('/api/marketing/strategy_execution/debug', payload, {
    transform: false,
    // baseURL:'http://local.xiaohongshu.com:8003'
  })
}

/** 查询debug白名单列表 */
export function queryDebugWhitelist(params: { whiteUserId?: string }): Promise<any> {
  return get('/api/usergrowth/debug_whitelist/query', { params }, {
    transform: false,
  })
}

/** 新增debug白名单 */
export function addDebugWhitelist(payload: { whiteUserName: string; whiteUserId: string | number }): Promise<any> {
  return post('/api/usergrowth/debug_whitelist/add', payload, {
    transform: false,
  })
}

/** 删除debug白名单 */
export function deleteDebugWhitelist(payload: { whiteUserId: string | number }): Promise<any> {
  return post('/api/usergrowth/debug_whitelist/delete', payload, {
    transform: false,
  })
}

/** 流程实例列表 */
export function sopInstanceList(payload): Promise<any> {
  return post('/api/usergrowth/sopinstance/list', payload, {
    transform: false,
  })
}

/** 流程模版列表 */
export function sopTemplateList(): Promise<any> {
  return post('/api/usergrowth/soptemplate/list', {}, {
    transform: false,
  })
}

/** 新建流程实例 */
export function instanceCreate(payload): Promise<any> {
  return post('/api/usergrowth/instance/create', payload, {
    transform: false,
  })
}
/** 查看流程详情  */
export function instanceDetail(id): Promise<any> {
  return get(`/api/usergrowth/instance/${id}`, {}, {
    transform: false,
  })
}
/** 更新流程实例节点信息 V1 */
export function updateInstanceDetail(payload): Promise<any> {
  return post('/api/usergrowth/instancenode/update', payload, {
    transform: false,
    extractData: false,
  })
}
/** 查询 SOP 中对应的投放计划 V1 */
export function getSOPLaunchPlan(id): Promise<any> {
  return get(`/api/usergrowth/sop/launchplan/${id}`, {}, {
    transform: false,
  })
}
/** 更新流程实例 V1 */
export function updateSOPInstance(payload): Promise<any> {
  return post('/api/usergrowth/sopinstance/update', payload, {
    transform: false,
    extractData: false,
  })
}
/** 创建策略预览令牌 V1 */
export function createStrategyPreviewToken(payload): Promise<any> {
  return post('/api/marketing/create_strategy_preview_token', payload, {
    transform: false,
    extractData: false,
  })
}
/** 权益池修改库存接口 V1 */
export function modifyStock(payload): Promise<any> {
  return post('/api/usergrowth/prizerule/modifystock', payload, {
    transform: false,
    extractData: false,
  })
}

/** 创建大促周期 */
export function createPromotionCycle(payload: { periodPlan: IPromotionCycle }): Promise<any> {
  return post('CREATE_PROMOTION_CYCLE', payload, {
    transform: false,
    extractData: false,
  })
}

/** 更新大促周期 */
export function updatePromotionCycle(payload: { periodPlan: IPromotionCycle }): Promise<any> {
  return put('UPDATE_PROMOTION_CYCLE', payload, {
    transform: false,
    extractData: false,
  })
}

/** 查询条件组件列表 */
export function getConditionComponentList(params): Promise<any> {
  return get('CONDITION_COMPONENT_LIST', { params }, {
    transform: false,
  })
}

/** 查询条件组件 byID */
export function getConditionComponentConfig(params): Promise<any> {
  return get('CONDITION_COMPONENT_CONFIG', { params }, {
    transform: false,
  })
}

/** 条件组件数据保存 V3 */
export function upsertConditionComponent(payload): Promise<any> {
  return post('UPSERT_CONDITION_COMPONENT', payload, {
    transform: false,
  })
}

/** 查询大促周期列表 */
export function getPromotionCycleList(params: IPromotionCycleListParams): Promise<IPromotionCycleListResponse> {
  return get('GET_PROMOTION_CYCLE_LIST', { params }, {
    transform: false,
  })
}

/** 删除大促周期 */
export function deletePromotionCycle(payload: { id: number }): Promise<any> {
  return post('DELETE_PROMOTION_CYCLE', payload, {
    transform: false,
    extractData: false,
  })
}

/** 查询大促周期详情 */
export function getPromotionCycleDetail(params: IPromotionCycleDetailParams): Promise<any> {
  return get('GET_PROMOTION_CYCLE_DETAIL', { params }, {
    transform: false,
  })
}

/** 策略触发预估人数编辑 */
export function triggerDataEdit(payload): Promise<any> {
  return post('TRIGGER_DATA_EDIT', payload, {
    transform: false,
    extractData: false,
  })
}

/** 策略触发数据查询  */
export function triggerDataQuery(params): Promise<any> {
  return get('TRIGGER_DATA_QUERY', { params }, {
    transform: false,
  })
}

/** 策略全局管理预览 */
export function strategyGlobalManagementPreview(params): Promise<any> {
  return get('STRATEGY_GLOBAL_MANAGEMENT_PREVIEW', { params }, {
    transform: false,
  })
}
