import {
  ApplicationTwo, Airplane, Home, Toolkit, Archery, Data,
} from '@xhs/delight/icons'
import { MenuContent, MenuDescription } from '@xhs/delight'

const menus: (MenuContent | MenuDescription)[] = [
  {
    icon: ApplicationTwo,
    title: '营销活动',
    collapse: false,
    children: [
      {
        title: '活动报名',
        to: { name: 'ActivityPage' },
      },
      {
        title: '商家报名审核',
        to: { name: 'sellerRegisExamine' },
      },
      {
        title: '素材模版',
        to: { name: 'ActivityMaterialList' },
      },

    ],
  },
  {
    icon: Toolkit,
    title: '营销工具',
    collapse: false,
    children: [
      {
        title: '定金预售管理',
        to: { name: 'PresaleList' },
      },
      {
        title: '名单配置中心',
        to: { name: 'Blacklist' },
      },
    ],
  },
  {
    icon: Airplane,
    title: '投放系统',
    collapse: false,
    children: [
      {
        title: '资源位管理',
        to: { name: 'ResourcePage' },
      },
      {
        title: '数据集管理',
        to: { name: 'DatasetPage' },
      },
      {
        title: '[产研]表单管理',
        to: { name: 'FormPage' },
      },
      {
        title: '[产研]数据集类型管理',
        to: { name: 'DatasetTypePage' },
      },
    ],
  },
  {
    icon: Home,
    title: '任务活动',
    collapse: false,
    children: [
      {
        title: '业务场景',
        to: { name: 'taskScene' },
      },
      {
        title: '积分管理',
        to: { name: 'taskPoint' },
      },
    ],
  },
  {
    icon: Archery,
    title: '用增互动平台',
    collapse: false,
    children: [
      {
        title: '智能营销V2',
        to: { name: 'IntelligentMarketing' },
      },
      {
        title: '活动列表',
        to: { name: 'PlayActivityList' },
      },
      {
        title: '任务活动平台',
        to: { name: 'TaskActivityList' },
      },
      {
        title: '玩法实例管理',
        to: { name: 'ComponentInstanceList' },
      },
      {
        title: '人群列表',
        to: { name: 'CrowdList' },
      },
      {
        title: '奖励中心',
        to: { name: 'RewardRuleList' },
      },
      {
        title: '玩法管理',
        to: { name: 'PlayList' },
      },
      {
        title: '条件中心',
        to: { name: 'ConditionCenter' },
      },
    ],
  },
  {
    icon: Data,
    title: '数据管理',
    collapse: false,
    children: [
      {
        title: '标签管理',
        to: { name: 'TagList' },
      },
      {
        title: '规则列表',
        to: { name: 'RuleList' },
      },
      {
        title: '源数据列表',
        to: { name: 'MetadataList' },
      },
    ],
  },
]

export default menus
