import { EDITH_HOST } from '~/constants'

/* eslint-disable no-template-curly-in-string */
const PROMOTION_API_LIST = {
  /** 代码组织规范：请按大模块分组（比如活动报名、投放系统、以及未来的xxx），每行右侧注释该api的功能 */

  /** 活动报名 */
  ACTIVITY_CREATE: '/api/edith/campaign/activities', // 父活动
  ACTIVITY_LIST: '/api/edith/campaign/activities',
  ACTIVITY_DETAIL: '/api/edith/campaign/v2/activities/${id}',
  ACTIVITY_UPDATE: '/api/edith/campaign/activities/${id}',
  ACTIVITY_DELETE: '/api/edith/campaign/activities/${id}',
  ACTIVITY_STATE: '/api/edith/campaign/activities/state/${id}',
  ACTIVITY_EXPORT: '/api/edith/campaign/activities/${id}/campaign-items/files',
  SUB_ACTIVITY_CREATE: '/api/edith/campaign/v2/activities/${id}/sub-activities', // 子活动
  SUB_ACTIVITY_DETAIL: '/api/edith/campaign/v2/activities/${id}/sub-activities/${subId}',
  SUB_ACTIVITY_UPDATE: '/api/edith/campaign/v2/activities/${id}/sub-activities/${subId}',
  SUB_ACTIVITY_DELETE: '/api/edith/campaign/activities/${id}/sub-activities/${subId}',
  SELLER_GROUP: '/api/edith/campaign/activities/seller-group', // 校验商家白名单
  CATEGORIES: '/api/edith/campaign/categories', // 获取品类
  VALIDATE_SUB_ACTIVITY_NAME: '/api/edith/campaign/activity/${id}/check_duplicate', // 校验名称是否重复
  VALIDATE_SUB_ACTIVITY_ENABLE_MODIFY: '/api/edith/campaign/sub_activity/${subId}/enable_modify_params', // 校验是否可编辑锁库存 子活动进行时间
  SUB_ACTIVITY_SELLER_ADD: '/api/edith/campaign/sub-activities/${subActivityId}/seller', // 新增邀约商家
  SUB_ACTIVITY_SELLER_LIST: '/api/edith/campaign/sub-activities/${subId}/seller', // 邀约商家列表
  SUB_ACTIVITY_SELLER_DELETE: '/api/edith/campaign/sub-activities/${subId}/seller/${sellerId}', // 删除邀约商家
  PROMOTION_GROUP: '/api/edith/campaign/v2/group', // 促销组
  PROMOTION_GROUP_CREATE: '/api/edith/promotion/v2/group', // 创建促销组
  ACTIVITY_CAMPAIGN_ITEMS_CNT: '/api/edith/campaign/v2/activities/${id}/sub-activities/${subId}/campaign-items-cnt', // 活动审核总数
  ACTIVITY_CAMPAIGN_ITEMS: '/api/edith/campaign/v2/activities/${id}/sub-activities/${subId}/campaign-items', // 审核活动列表
  APPROVE_CAMPAIGN_ITEMS: '/api/edith/campaign/v2/activities/${id}/sub-activities/${subId}/campaign-items/states', // 审核商品
  ACTIVITY_SELLERS: '/api/edith/campaign/activities/${id}/sub-activities/${subId}/sellers', // 获取报名了子活动的卖家
  ACTIVITY_BRANDS: '/api/edith/campaign/activities/${id}/sub-activities/${subId}/brands', // 获取报名了子活动的品牌
  CAMPAIGN_FULL_OPERATION: '/api/edith/campaign/v2/activities/${id}/sub-activities/${subId}/full-operation', // 全量审核通过
  CAMPAIGN_ITEMS_TARGET_TOTAL: '/api/edith/campaign/v2/activities/${id}/sub-activities/${subId}/campaign-items/target-total', // 搜索结果导出数量查询
  OPERATOR_LIST: '/api/edith/campaign/operator', // 获取运营列表
  QUERY_OPERATION: '/api/hawk/user/queryOperatorList', // 获取运营列表
  SELLER_MAIN_CATEGORY: '/api/activity/analyser/meta/meta-info-v2', // 商家主营类目
  RED_LABEL_REGIS_COUNT: '/api/edith/campaign/redlabel-seller/count', // redlabel商家入驻
  RED_LABEL_SELLER_LIST: '/api/edith/campaign/red-label-seller', // redlabel商家列表
  RED_LABEL_STATUS_CHANGE: '/api/edith/campaign/red-label-seller/status', // redlabel审核
  ACTIVITY_ATMOSPHERE_RESOURCE_USERS: '/api/edith/campaign/resource_users', // 活动氛围白名单
  ACTIVITY_ATMOSPHERE_CONFIRM: '/api/edith/campaign/activities/resource/state/${activityId}', // 活动氛围确认
  SUB_ACTIVITY_GOODS_UPLOAD_TEMPLATE: '/api/edith/campaign/sub_activity/${subActivityId}/upload_template', // 子活动商品上传模板
  ACTIVITY_ATMOSPHERE_PIN: '/api/edith/campaign/activities/resource/top-state/${activityId}', // 氛围置顶
  TAG_LIST: '/api/edith/begeta/taglist',

  /** 投放系统 */
  FORM_LIST: '/api/edith/alita/form/query', // 查询表单定义数据列表
  FORM_UPDATE: '/api/edith/alita/form/update', // 创建、修改表单定义数据
  FORM_DELETE: '/api/edith/alita/form/delete', // 删除表单定义数据
  RESOURCE_LIST: '/api/edith/alita/resource/query', // 查询资源位列表
  RESOURCE_UPDATE: '/api/edith/alita/resource/update', // 创建、修改资源位
  RESOURCE_DELETE: '/api/edith/alita/resource/delete', // 删除资源位
  DATASET_TYPE_LIST: '/api/edith/alita/solution/query', // 查询数据集类型（后端叫解决方案）
  DATASET_TYPE_UPDATE: '/api/edith/alita/solution/update', // 创建、修改数据集类型
  DATASET_TYPE_DELETE: '/api/edith/alita/solution/delete', // 删除数据集类型
  LAUNCH_PLAN_LIST: '/api/edith/alita/plan/query', // 查询投放计划列表
  LAUNCH_PLAN_UPDATE: '/api/edith/alita/plan/update', // 创建、修改投放计划
  LAUNCH_PLAN_ONLINE: '/api/edith/alita/plan/online', // 上线投放计划
  LAUNCH_PLAN_OFFLINE: '/api/edith/alita/plan/offline', // 下线投放计划
  LAUNCH_PLAN_DELETE: '/api/edith/alita/plan/delete', // 删除投放计划
  DATASET_LIST: '/api/edith/alita/dataset/query', // 查询数据集列表
  DATASET_UPDATE: '/api/edith/alita/dataset/update', // 创建、修改数据集
  DATASET_DELETE: '/api/edith/alita/dataset/delete', // 删除数据集
  DATASET_EXCEL_TEMPLATE: '/api/edith/alita/dataset/query/excel', // 获取数据集导入模板链接
  DATASET_CONNECT: '/api/edith/alita/plan/bind_dataset', // 关联数据集
  DATASET_CONTENT: '/api/edith/alita/content/query', // 查询数据集内容
  DATASET_CONTENT_UPDATE: '/api/edith/alita/content/update', // 创建、修改数据集单项
  DATASET_CONTENT_UP: '/api/edith/alita/content/lift_to_top', // 置顶数据集单项
  DATASET_ENTITY_SEARCH: '/api/edith/rocking/data/entity/batch/get', // 数据集实体搜索
  CROWD_LIST: '/api/edith/alita/crowd/find', // 查询人群包列表
  POOL_CONFIG_LIST: '/api/edith/alita/pool/config/find', // 查询选品池ID列表
  USER_TRAILER_LIST: '/api/edith/alita/user_trailer/query', // 查询直播预告列表

  /** 大促  */
  QUERY_REHERSAL_ACCOUNT: '/api/edith/seer/account', // 预演账号列表
  APPLY_REHERSAL_ACCOUNT: '/api/edith/seer/account', // 预演申请账号列表
  UPDATE_REHERSAL_ACCOUNT: '/api/edith/seer/account/${id}/status', // 更新预演账号状态
  QUERY_REHERSAL_SERVICE: '/api/edith/seer/service-entry', // 预演服务列表
  REHERSAL_SERVICE_ACTION: '/api/edith/seer/service-entry/${serviceEntryId}/status', // 预演服务操作
  REGISTER_REHERSAL_SERVICE: '/api/edith/seer/service-entry', // 预演服务注册
  QUERY_REHERSAL_CHAIN: '/api/edith/seer/chain', // 预演链路列表
  REHERSAL_CHAIN_ACITON: '/api/edith/seer/chain', // 预演联路操作
  REGISTER_REHERSAL_CHAIN: '/api/edith/seer/chain', // 新增链路
  DELETE_REHERSAL_CHAIN_NODE: '/api/edith/seer/chain/service-entry', // 预演删除链路节点
  CREATE_REHERSAL_CHAIN_NODE: '/api/edith/seer/chain/service-entry', // 预演添加链路服务节点
  QUERY_ONES_SERVICE: '/api/edith/seer/ones-service-entry', // ones 服务查询
  QUERY_ONES_APPLICATION: '/api/edith/seer/ones/application', // ones 应用查询

  /** 定金预售 */
  PRESALE_LIST: '/api/edith/presale/activity',
  PRESALE_CREATE: '/api/edith/presale/activity/create',
  PRESALE_EDIT: '/api/edith/presale/activity/edit',
  PRESALE_ACTIVITY_STATUS: '/api/edith/presale/sale/active',
  PRESALE_SALE_LIST: '/api/edith/presale/sales',
  PRESALE_SALE_EDIT: '/api/edith/presale/sale/edit',
  PRESALE_SALE_ADD: '/api/edith/presale/sale/create',
  PRESALE_SKU_PRICE: '/api/edith/presale/product/sku',
  PRESALE_ACTIVE_UPDATE: '/api/edith/presale/activity/active',
  DATASET_SKU_SEARCH: '/api/edith/alita/content/get_sku', // 数据集商品搜索

  /** 智能营销V2 */
  STRATEGY_CONTENT: '/api/marketing/strategy/${strategyId}', // 查询单条策略
  STRATEGY_LIST: '/api/marketing/strategy_list', // 查询策略列表
  STRATEGY_LIST_In_Frequency_Group: '/api/usergrowth/strategy_list_in_frequency_group',
  STRATEGY_CREATE: '/api/marketing/strategy', // 创建策略
  BATCH_STRATEGY_CREATE: '/api/marketing/strategy/batch', // 批量创建策略
  STRATEGY_UPDATE: '/api/marketing/strategy/${strategyId}', // 更新策略
  STRATEGY_STATUS_UPDATE: '/api/marketing/strategy_status/${strategyId}', // 更新策略状态
  FREQUENCY_LIST: '/api/marketing/frequency_group_list', // 查询频控组列表
  FREQUENCY_CREATE: '/api/marketing/frequency_group', // 创建频控组
  FREQUENCY_UPDATE: '/api/marketing/frequency_group/${frequencyGroupId}', // 更新频控组
  BIZSCENE_LIST: '/api/marketing/bizscene_list', // 获取全量场景列表及触发点列表
  STRATEGY_PRIORITY: '/api/marketing/bizscene/${bizsceneId}', // 获取场景下策略优先级
  STRATEGY_PRIORITY_UPDATE: '/api/marketing/bizscene/${bizsceneId}', // 更新场景下策略优先级
  CONTAINER_LIST: '/api/marketing/container_list', // 获取可选容器列表
  FREQUENCY_GROUP_CONTENT: '/api/marketing/frequency_group/${frequencyGroupId}', // 频控组点查接口
  CROWD_CREATE: '/api/usergrowth/compass/crowd', // 创建人群包
  CROWD_SEARCH: '/api/usergrowth/compass/crowd', // 查询人群包
  ECRM_CROWD: '/api/marketing/crowd', // 查询ECRM人群规则 & 人群包
  DAVID_CROWD: '/api/usergrowth/compass/david/crowd', // 查询DAVID人群规则 & 人群包
  CONDITION_INFO: '/api/marketing/condition_list', // 获取策略条件项信息
  CONDITION_INFO_V2: '/api/usergrowth/trigger_manage/condition_list', // 获取策略条件项信息
  LABEL_INFO: '/api/marketing/label_list', // 获取可配置标签列表
  CONTAINER_INFO: '/api/marketing/container_list', // 获取容器列表
  TRIGGER_INFO: '/api/marketing/trigger_list', // 获取触发点列表
  FUNC_RULES_LIST: '/api/marketing/benefit_choose_rule', // 查询算法映射列表
  CHOOSE_RULE_CREATE: '/api/marketing/benefit_choose_rule', // 创建算法映射规则
  CHOOSE_RULE_UPDATE: '/api/marketing/benefit_choose_rule', // 更新算法映射规则
  FUNC_TEMPLATES_LIST: '/api/marketing/algo_strategy_template', // 策略模版列表
  CREATE_FUNC_TEMPLATE: '/api/marketing/algo_strategy_template', // 创建策略模版
  CREATE_STRATEGY__BY_TEMPLATE: '/api/marketing/strategy/create_by_template', // 根据模版创建策略
  COUPON_TEMPLATE_SEARCH: '/api/edith/coupon/templates/reder_search', // 券模版信息查询
  CREATE_STRATEGY_GROUP: '/api/marketing/strategy_group', // 创建策略组
  GET_STRATEGY_GROUP_LIST: '/api/marketing/strategy_group', // 获取策略组列表
  GET_STRATEGY_GROUP: '/api/marketing/strategy_group/${id}', // 点查策略组
  GET_STRATEGY_PREVIEW: '/api/usergrowth/preview_strategy', // 预览策略
  STRATEGY_GROUP_SUBMIT: '/api/marketing/strategy_group/commit', // 提交策略组生成策略
  UPDATE_STRATEGY_GROUP_STATUS: '/api/marketing/strategy_group/strategy_status', // 更新策略组状态
  DELETE_STRATEGY_GROUP: '/api/marketing/strategy_group_status', // 删除策略组
  GET_BUYER_LIVE_TABLE: '/api/marketing/buyer_live_table', // 获取买手直播容器表单信息
  /** 条件规则 */
  CONDITION_RULE_LIST: '/api/marketing/list/condition_rule', // 查询条件规则列表
  CREATE_CONDITION_RULE: '/api/marketing/condition_rule', // 创建条件规则
  UPDATE_CONDITION_RULE: '/api/marketing/condition_rule/${conditionRuleId}', // 更新条件规则
  OFFLINE_CONDITION_RULE: '/api/marketing/offline/condition_rule/${conditionRuleId}', // 下线条件规则
  CONDITION_RULE_ITEM: '/api/marketing/condition_rule/${conditionRuleId}', // 获取条件规则详情
  /** 筛选规则 */
  FILTER_RULE_LIST: '/api/marketing/list/filter_rule', // 查询筛选规则列表
  CREATE_FILTER_RULE: '/api/marketing/filter_rule', // 创建筛选规则
  UPDATE_FILTER_RULE: '/api/marketing/v1/filter_rule/${filterRuleId}', // 更新筛选规则
  OFFLINE_FILTER_RULE: '/api/marketing/offline/filter_line/${filterRuleId}', // 更新筛选规则
  FILTER_RULE_ITEM: '/api/marketing/filter_rule/${filterRuleId}', // 获取筛选规则详情
  /** 权益组 */
  CHECK_BENEFITS: '/api/usergrowth/benefits/check',

  /** Ditto 搭建 */
  DITTO_USER_INFO: '/api/ditto/users',

  /** 用户拉群 */
  JOIN_WECHAT_GROUP: '/api/edith/campaign/join_wechat_group',

  /** 活动日历 */
  CREATE_ACTIVITY_CALENDAR: '/api/edith/campaign/activity_calendar', // 创建活动日历
  UPDATE_ACTIVITY_CALENDAR: '/api/edith/campaign/activity_calendar/${calendarId}', // 编辑活动日历
  UPDATE_ACTIVITY_CALENDAR_STATE: '/api/edith/campaign/activity_calendar/state/${calendarId}',
  QUERY_ACTIVITY_CALENDAR_DETAIL: '/api/edith/campaign/activity_calendar/${calendarId}', // 查询活动日历详情
  QUERY_ACTIVITY_CALENDAR_LIST: '/api/edith/campaign/activity_calendar', // 查询活动日历列表
  DEL_ACTIVITY_CALENDAR: '/api/edith/campaign/activity_calendar/${calendarId}',

  /** 活动审核依赖接口 */
  SELLER_CATEGORY: '/api/activity/analyser/meta/meta-info-v2',
  GOODS_CATEGORY: '/api/hawk/selection/categoryTree',
  GOODS_CATEGORY_BY_ID: '/api/hawk/selection/categoryTree/${id}',
  TAG_2: '/api/edith/begeta/taglist',
  GET_QUERY_OPERATION: '/api/hawk/user/queryOperatorList',
  GET_META_TAG_NAME: '/api/hawk/meta/tagName',

  /** 新版审核接口 */
  NEW_GET_AUDIT_STATE_COUNT: '/api/edith/campaign/activities/${activityId}/sub-activities/${subActivityId}/campaign-items/count',
  NEW_GET_AUDIT_OBJECT: '/api/edith/campaign/activities/${activityId}/sub-activities/${subActivityId}/campaign-items',
  NEW_AUDIT_OBJECT_BY_SEARCH_RESULT: '/api/edith/campaign/activities/${activityId}/sub-activities/${subActivityId}/campaign-items/search-audit',
  NEW_AUDIT_ITEMS: '/api/edith/campaign/activities/${activityId}/sub-activities/${subActivityId}/campaign-items/audit',

  /** 查询选品池 */
  GET_SELECTION_POOL_LIST: `${EDITH_HOST}/api/tianchi/selectconfig/list`,
  // 圈选池
  SELECTION_POOL_CONFIG: '/api/hawk/selection/pool_config',

  /** 活动中心 */
  PLAY_LIST: '/api/usergrowth/activity_center_manage/list/activity_type', // 查询玩法列表
  PLAY_ITEM: '/api/usergrowth/activity_center_manage/activity_type/${playId}', // 查询玩法信息
  PLAY_UPDATE: '/api/usergrowth/activity_center_manage/activity_type/${playId}', // 更新玩法
  PLAY_ACTIVITY_LIST: '/api/usergrowth/activity_center_manage/list/activities', // 查询玩法活动列表
  PLAY_ACTIVITY_ITEM: '/api/usergrowth/activity_center_manage/activity/${activityId}', // 查询玩法活动信息
  PLAY_ACTIVITY_CREATE: '/api/usergrowth/activity_center_manage/activity', // 创建玩法活动
  PLAY_ACTIVITY_UPDATE: '/api/usergrowth/activity_center_manage/activity/${activityId}', // 创建玩法活动
  PLAY_ACTIVITY_OFFLINE: '/api/usergrowth/activity_center_manage/offline/activity/${activityId}', // 下线玩法活动
  REWARD_RULE_LIST: '/api/usergrowth/prize-center/prize_rule', // 查询奖励规则列表
  REWARD_RULE_ITEM: '/api/usergrowth/prize-center/prize_rule/${ruleId}', // 查询奖励规则信息
  REWARD_RULE_CREATE: '/api/usergrowth/prize-center/prize_rule', // 创建奖励规则
  REWARD_RULE_UPDATE: '/api/usergrowth/prize-center/prize_rule/${ruleId}', // 更新奖励规则
  REWARD_RULE_OFFLINE: '/api/usergrowth/prize-center/prize_rule/${ruleId}', // 下线奖励规则
  USER_GROWTH_CROWD_SEARCH: '/api/usergrowth/compass/crowd', // 查询人群包
  USER_GROWTH_DAVID_CROWD: '/api/usergrowth/compass/david/crowd', // 查询DAVID人群规则 & 人群包
  USER_GROWTH_CROWD_CREATE: '/api/usergrowth/compass/crowd', // 创建人群包

  /** 氛围中心 */
  CREATE_SCENE: '/api/atmosphere/createscene',
  UPDATE_SCENE: '/api/atmosphere/updatescene',
  PAGE_SCENE: '/api/atmosphere/pagescene',
  SCENE_DETAIL: '/api/atmosphere/scene/detail',
  ATMOSPHERE_TAG_LIST: '/api/atmosphere/tag/enums/list',
  ATMOSPHERE_TAG_TYPE_LIST: '/api/atmosphere/tag/types/list',
  ATMOSPHERE_TAG_STYLES_LIST: '/api/atmosphere/tag/styles/list',
  LAYOUT_TEMPLATE: '/api/atmosphere/layout/template/get',
  PAGE_LAYOUT: '/api/atmosphere/layout/page',
  LAYOUT_DETAIL: '/api/atmosphere/layout/detail',
  CREATE_LAYOUT: '/api/atmosphere/layout/create',
  UPDATE_LAYOUT: '/api/atmosphere/layout/update',
  DEL_LAYOUT: '/api/atmosphere/layout/del',
  SCENE_SEARCH: '/api/atmosphere/scene/search',

  /** 数据中心 */
  COMPASS_TAG_LIST: '/api/usergrowth/compass/tag/query', // 查询标签列表
  COMPASS_TAG_HIERARCHY_LIST: '/api/usergrowth/compass/taghierarchy/query', // 查询标签列表
  COMPASS_TAG_DETAIL: '/api', // 查询标签详情
  COMPASS_RULE_LIST: '/api/usergrowth/compass/query/crowd/rule', // 查询规则列表
  COMPASS_RULE_DETAIL: '/api/usergrowth/query/crowd/rule/id', // 查询规则详情
  COMPASS_RULE_UPDATE: '/api/usergrowth/compass/edit/crowd/rule', // 更新规则
  COMPASS_RULE_DELETE: '/api/usergrowth/crowd/delete', // 删除规则
  COMPASS_METADATA_LIST: '/api/usergrowth/compass/query/meta', // 查询元数据列表
  CROWD_USAGE: '/api/usergrowth/crowd/usage', // 查询用增人群包使用方
  COMPASS_METADATA_DETAIL: '/api', // 查询元数据详情
  /** 触发规则 */
  TRIGGER_RULE: '/api/usergrowth/trigger_manage/trigger_rule', // 查询触发规则详情
  TRIGGER_RULE_LIST: '/api/usergrowth/trigger_manage/trigger_rule_list', // 查询触发规则列表
  CREATE_TRIGGER_RULE: '/api/usergrowth/trigger_manage/create/trigger_rule', // 创建触发规则
  UPDATE_TRIGGER_RULE: '/api/usergrowth/trigger_manager/update/trigger_rule', // 修改触发规则
  OFFLINE_TRIGGER_RULE: '/api/usergrowth/trigger_manage/trigger_rule/offline', // 下线触发规则
  TRIGGER_BY_TRIGGER_RULE: '/api/usergrowth/trigger_manage/trigger_point_by_rule_id', // 根据触发规则ID查询触点
  LAUNCH_BY_TRIGGER_RULE: '/api/usergrowth/trigger_manage/launch_plans_by_rule_id', // 根据触发规则ID查询投放计划
  /** 触点 */
  TRIGGER_POINT: '/api/usergrowth/trigger_manage/trigger_point', // 查询触点详情
  TRIGGER_LIST: '/api/usergrowth/trigger_manage/trigger_point_list', // 查询触点列表
  QUERY_AVAILABLE_SWITCH: '/api/marketing/strategy/query_available_switch', // 查询策略可显示的开关
  CREATE_TRIGGER_POINT: '/api/usergrowth/trigger_manage/trigger_point/create', // 创建触点
  UPDATE_TRIGGER_POINT: '/api/usergrowth/trigger_manage/trigger_point/update', // 更新触点
  OFFLINE_TRIGGER_POINT: '/api/usergrowth/trigger_manage/trigger_point/offline', // 下线触点
  /** 容器规则 */
  CONTAINER_RULE: '/api/usergrowth/trigger_manage/container_rule/query', // 查询容器详情
  CONTAINER_RULE_LIST: '/api/usergrowth/trigger_manage/container_rule/list', // 查询容器列表
  CREATE_CONTAINER_RULE: '/api/usergrowth/trigger_manage/container_rule/create', // 创建容器规则
  UPDATE_CONTAINER_RULE: '/api/usergrowth/trigger_manage/container_rule/update', // 更新容器规则
  OFFLINE_CONTAINER_RULE: '/api/usergrowth/trigger_manage/container_rule/offline', // 下线容器规则
  CONTAINER_RULE_CONTAINER: '/api/usergrowth/get_all_container_list', // 查询容器详情里的全部容器
  TRIGGER_CONDITION_INFO: '/api/usergrowth/trigger_manage/condition_list',
  LAUNCHPLAN_DELETE: '/api/usergrowth/launchplan/delete', // 删除投放计划
  RESOURCE_MANAGEMENT_LIST: '/api/usergrowth/platform/resource/list', // 资源管理 list
  RESOURCE_UPSERT: '/api/usergrowth/platform/resource/upsert', // 编辑资源
  CONDITION_COMPONENT_LIST: '/api/marketing/condition_component/query_condition_component', // 查询条件组件
  CONDITION_COMPONENT_CONFIG: '/api/marketing/condition_component/query_condition_component_config', // 查询条件组件by ID

  /** 黑白名单管理 */
  WHITELIST_LIST: '/api/usergrowth/crowd/whitelist', // 查询黑白名单列表
  WHITELIST_DETAIL: '/api/usergrowth/crowd/whitelist/one', // 查询黑白名单详情
  WHITELIST_CREATE: '/api/usergrowth/crowd/whitelist/create', // 创建黑白名单
  WHITELIST_UPDATE: '/api/usergrowth/crowd/whitelist/update', // 更新黑白名单
  WHITELIST_DELETE: '/api/usergrowth/crowd/whitelist/delete', // 删除黑白名单

  /** 任务 */
  TASK_ACTIVITY_LIST: '/api/usergrowth/task/pagegetactivitylist', // 查询任务列表
  TASK_ACTIVITY_ITEM: '/api/usergrowth/activity_center_manage/task/${taskId}', // 查询任务信息
  TASK_ACTIVITY_CREATE: '/api/usergrowth/task/createtaskactivity', // 创建活动任务
  TASK_ACTIVITY_UPDATE: '/api/usergrowth/task/updatetaskactivity', // 更新活动任务
  CREATE_TASK: '/api/usergrowth/task/createtask', // 创建任务
  UPDATE_TASK: '/api/usergrowth/task/updatetask', // 更新任务
  TASK_ACTIVITY_DETAIL: '/api/usergrowth/task/gettaskactivitybyidforb', // 查询任务信息
  GRAY_SCALE_RECORD: '/api/usergrowth/gray/record', // 根据活动获取灰度发布记录
  GRAY_SCALE_STATUS: '/api/usergrowth/gray/status', // 批量获取活动维度灰度状态
  GRAY_SCALE_CONFIG: '/api/usergrowth/gray/config', // 更新灰度活动实例状态，包括开始和暂停
  DELETE_TASK_ACTIVITY: '/api/usergrowth/task/deletetaskactivity', // 删除任务
  OFFLINE_TASK_ACTIVITY: '/api/usergrowth/task/offlinetaskactivity', // 下线任务

  /** 大促周期 */
  CREATE_PROMOTION_CYCLE: '/api/usergrowth/periodplan/create', // 创建大促周期
  UPDATE_PROMOTION_CYCLE: '/api/usergrowth/periodplan/update', // 更新大促周期
  GET_PROMOTION_CYCLE_LIST: '/api/usergrowth/periodplan', // 查询大促周期列表
  DELETE_PROMOTION_CYCLE: '/api/usergrowth/periodplan/delete', // 删除大促周期
  GET_PROMOTION_CYCLE_DETAIL: '/api/usergrowth/periodplan/detail', // 查询大促周期详情

  /** 任务话题预览 */
  TASK_TOPIC_PREVIEW: '/api/usergrowth/task/tasktopicpreview', // 任务话题预览接口
  TASK_NOTE_PREVIEW: '/api/usergrowth/task/tasknotepreview', // 任务笔记预览接口
  TRIGGER_DATA_EDIT: '/api/marketing/strategy/trigger_data/edit', // 策略触发预估人数编辑
  TRIGGER_DATA_QUERY: '/api/marketing/strategy/trigger_data/query', // 策略触发数据查询
  UPSERT_CONDITION_COMPONENT: '/api/marketing/conditon_component/upsert_condition_component', // 条件组件数据保存 V3

  /** 玩法组件实例管理 */
  ACTIVITY_COMPONENT_LIST: '/api/usergrowth/activity/components', // 查询玩法组件实例列表
  ACTIVITY_COMPONENT_DETAIL: '/api/usergrowth/activity/component', // 查询玩法组件实例详情
  ACTIVITY_COMPONENT_CREATE: '/api/usergrowth/activity/component', // 创建玩法组件实例
  ACTIVITY_COMPONENT_UPDATE: '/api/usergrowth/activity/component', // 更新玩法组件实例
  ACTIVITY_COMPONENT_OFFLINE: '/api/usergrowth/activity/component/offline', // 下线玩法组件实例

  /** 策略全局管理 */
  STRATEGY_GLOBAL_MANAGEMENT_PREVIEW: '/api/usergrowth/strategy/previewmarket',
}

export default PROMOTION_API_LIST
