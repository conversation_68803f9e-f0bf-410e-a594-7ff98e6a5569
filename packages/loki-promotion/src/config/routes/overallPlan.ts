import { LauncherOptions } from '@xhs/launcher'

const Routes: LauncherOptions['routes'] = [
  {
    name: 'OverallPlan',
    path: 'overall-plan',
    meta: { title: '整体计划管理', activeMenu: 'Overall_Plan_List' },
    component: () => import('../../pages/OverallPlan/Index.vue'),
    redirect: {
      name: 'PlanList',
    },
    children: [
      {
        name: 'PlanList',
        path: 'plan-list',
        meta: { title: '整体计划列表', activeMenu: 'Overall_Plan_List' },
        component: () => import('../../pages/OverallPlan/PlanList/PlanList.vue'),
      },
      {
        name: 'PlanDetail',
        path: 'plan-detail',
        meta: { title: '整体计划列表', activeMenu: 'Overall_Plan_List' },
        component: () => import('../../pages/OverallPlan/PlanList/PlanDetail.vue'),
      },
      {
        name: 'FrequencyControl',
        path: 'frequency-control',
        meta: { title: '统一频控', activeMenu: 'Overall_Plan_List' },
        component: () => import('../../pages/OverallPlan/FrequencyControl/FrequencyControl.vue'),
      },
      {
        name: 'FrequencyControlDetail',
        path: 'frequency-control-detail',
        meta: { title: '统一频控', activeMenu: 'Overall_Plan_List' },
        component: () => import('../../pages/OverallPlan/FrequencyControl/FrequencyControlDetail.vue'),
      },
      {
        name: 'PriorityControl',
        path: 'priority-control',
        meta: { title: '统一优先级', activeMenu: 'Overall_Plan_List' },
        component: () => import('../../pages/OverallPlan/PriorityControl/PriorityControlList.vue'),
      },
      {
        name: 'PriorityControlDetail',
        path: 'priority-control-detail',
        meta: { title: '统一优先级', activeMenu: 'Overall_Plan_List' },
        component: () => import('../../pages/OverallPlan/PriorityControl/PriorityControlDetail.vue'),
      },
    ],
  },
]

export default Routes
