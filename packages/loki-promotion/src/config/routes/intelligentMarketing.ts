import { LauncherOptions } from '@xhs/launcher'

const intelligentMarketingRoutes: LauncherOptions['routes'] = [
  {
    name: 'IntelligentMarketing',
    path: 'intelligent-marketing',
    meta: {
      title: '智能营销V2', activeMenu: 'Smart_Operate_V2',
    },
    component: () => import('../../pages/IntelligentMarketing/Index.vue'),
    redirect: {
      name: 'LaunchPlan',
    },
    children: [
      {
        name: 'LaunchPlan',
        path: 'launch-plan',
        meta: {
          title: '投放计划', activeMenu: 'Smart_Operate_V2', pageKey: 'launch_plan_list',
        },
        component: () => import('../../pages/IntelligentMarketing/LaunchPlan/Index.vue'),
      },
      {
        name: 'StrategyGlobalManagement',
        path: 'strategy-global-management',
        meta: {
          title: '策略全局管理', activeMenu: 'Smart_Operate_V2',
        },
        component: () => import('../../pages/IntelligentMarketing/StrategyGlobalManagement/Index.vue'),
      },
      {
        name: 'ManagePoint',
        path: 'manage-point',
        meta: {
          title: '触点管理', activeMenu: 'Smart_Operate_V2',
        },
        component: () => import('../../pages/IntelligentMarketing/ManagePoint/Index.vue'),
      },
      {
        name: 'EquityPool',
        path: 'equity-pool',
        meta: {
          title: '权益池', activeMenu: 'Smart_Operate_V2', pageKey: 'promotion_equity_pool_list',
        },
        component: () => import('../../pages/IntelligentMarketing/EquityPool/EquityPool.vue'),
      },
      {
        name: 'EquityPoolDetail',
        path: 'equity-pool-detail',
        meta: {
          title: '权益池', activeMenu: 'Smart_Operate_V2',
        },
        component: () => import('../../pages/IntelligentMarketing/EquityPool/EquityPoolDetail.vue'),
      },
      {
        name: 'Scenes',
        path: 'scenes',
        meta: {
          title: '场景列表-历史', activeMenu: 'Smart_Operate_V2',
        },
        component: () => import('../../pages/IntelligentMarketing/StrategyList/Index.vue'),
      },
      {
        name: 'ScenesV2',
        path: 'scenesV2',
        meta: {
          title: '场景列表', activeMenu: 'Smart_Operate_V2',
        },
        component: () => import('../../pages/IntelligentMarketing/StrategyListV2/Index.vue'),
      },
      {
        name: 'PriorityManage',
        path: 'priority-manage',
        meta: {
          title: '优先级管理', activeMenu: 'Smart_Operate_V2',
        },
        component: () => import('../../pages/IntelligentMarketing/PriorityManage/Index.vue'),
      },
      {
        name: 'FrequencyManage',
        path: 'frequency-manage',
        meta: {
          title: '全局频控管理', activeMenu: 'Smart_Operate_V2',
        },
        component: () => import('../../pages/IntelligentMarketing/FrequencyManage/Index.vue'),
      },
      {
        name: 'AlgorithmConfig',
        path: 'algorithm-config',
        meta: {
          title: '算法配置', activeMenu: 'Smart_Operate_V2',
        },
        component: () => import('../../pages/IntelligentMarketing/AlgorithmConfig/Index.vue'),
      },
      {
        name: 'StrategyGroup',
        path: 'strategy-group',
        meta: {
          title: '策略组', activeMenu: 'Smart_Operate_V2',
        },
        component: () => import('../../pages/IntelligentMarketing/Group/StrategyGroup.vue'),
      },
      {
        name: 'Strategy',
        path: 'strategy',
        meta: {
          title: 'Group', activeMenu: 'Smart_Operate_V2',
        },
        component: () => import('../../pages/IntelligentMarketing/Group/Strategy.vue'),
        props: {
          formType: 'create',
        },
      },
      {
        name: 'ConditionRuleConfig',
        path: 'condition-rule-config',
        meta: {
          title: '条件规则配置', activeMenu: 'Smart_Operate_V2',
        },
        component: () => import('../../pages/IntelligentMarketing/ConditionRuleConfig/index.vue'),
      },
      {
        name: 'FilterRuleConfig',
        path: 'filter-rule-config',
        meta: {
          title: '筛选规则配置', activeMenu: 'Smart_Operate_V2',
        },
        component: () => import('../../pages/IntelligentMarketing/FilterRuleConfig/index.vue'),
      },
      {
        name: 'LaunchPlanDetail',
        path: 'launch-plan-detail',
        meta: {
          title: '投放计划详情', activeMenu: 'Smart_Operate_V2',
        },
        component: () => import('../../pages/IntelligentMarketing/LaunchPlan/LaunchPlanDetail.vue'),
      },
      {
        name: 'LaunchPlanDetailV2',
        path: 'launch-plan-detailV2',
        meta: {
          title: '投放计划详情', activeMenu: 'Smart_Operate_V2', pageKey: 'launch_plan_detail',
        },
        component: () => import('../../pages/IntelligentMarketing/LaunchPlan/LaunchPlanDetailV2.vue'),
      },
      {
        name: 'PointDetail',
        path: 'manage-point-detail',
        meta: {
          title: '触点详情', activeMenu: 'Smart_Operate_V2',
        },
        component: () => import('../../pages/IntelligentMarketing/ManagePoint/Point/PointDetail.vue'),
      },
      {
        name: 'PointRuleDetail',
        path: 'manage-point-rule-detail',
        meta: {
          title: '触发规则详情', activeMenu: 'Smart_Operate_V2',
        },
        component: () => import('../../pages/IntelligentMarketing/ManagePoint/PointRule/PointRuleDetail.vue'),
      },
      {
        name: 'ContainerDetail',
        path: 'manage-point-container-detail',
        meta: {
          title: '容器规则详情', activeMenu: 'Smart_Operate_V2',
        },
        component: () => import('../../pages/IntelligentMarketing/ManagePoint/Container/ContainerDetail.vue'),
      },
      {
        name: 'ResourceManagement',
        path: 'resource-management',
        meta: {
          title: '资源管理', activeMenu: 'Smart_Operate_V2',
        },
        component: () => import('../../pages/IntelligentMarketing/ResourceManagement/index.vue'),
      },
      {
        name: 'StrategyDebug',
        path: 'strategy-debug',
        meta: {
          title: '策略调试', activeMenu: 'Smart_Operate_V2',
        },
        component: () => import('../../pages/IntelligentMarketing/StrategyDebug/Index.vue'),
      },
      {
        name: 'WhitelistDetail',
        path: 'whitelist-detail',
        meta: {
          title: '白名单详情', activeMenu: 'Smart_Operate_V2',
        },
        component: () => import('../../pages/IntelligentMarketing/StrategyDebug/WhitelistDetail.vue'),
      },
      {
        name: 'StrategySOP',
        path: 'strategy-sop',
        meta: {
          title: '标准流程', activeMenu: 'Smart_Operate_V2',
        },
        component: () => import('../../pages/IntelligentMarketing/StrategySOP/Index.vue'),
      },
      {
        name: 'EditStrategySOP',
        path: 'edit-strategy-sop/:id',
        meta: {
          title: '编辑策略流程', activeMenu: 'Smart_Operate_V2',
        },
        component: () => import('../../pages/IntelligentMarketing/StrategySOP/SOPDetail/Index.vue'),
        props: {
          formType: 'edit',
        },
      },
      {
        name: 'ViewStrategySOP',
        path: 'view-strategy-sop/:id',
        meta: {
          title: '查看策略流程', activeMenu: 'Smart_Operate_V2',
        },
        component: () => import('../../pages/IntelligentMarketing/StrategySOP/SOPDetail/Index.vue'),
        props: {
          formType: 'view',
        },
      },
    ],
  },
  {
    name: 'StrategyList',
    path: 'strategy-list',
    meta: {
      title: '策略列表', activeMenu: 'Smart_Operate_V2',
    },
    component: () => import('../../pages/IntelligentMarketing/StrategyList/List.vue'),
  },
  {
    name: 'StrategyListV2',
    path: 'strategy-listV2',
    meta: {
      title: '策略列表', activeMenu: 'Smart_Operate_V2',
    },
    component: () => import('../../pages/IntelligentMarketing/StrategyListV2/List.vue'),
  },
  {
    name: 'CreateStrategy',
    path: 'create-strategy',
    meta: {
      title: '新建策略', activeMenu: 'Smart_Operate_V2',
    },
    component: () => import('../../pages/IntelligentMarketing/CreateStrategy.vue'),
    props: {
      formType: 'create',
    },
  },
  {
    name: 'EditStrategy',
    path: 'edit-strategy/:strategyId',
    meta: {
      title: '编辑策略', activeMenu: 'Smart_Operate_V2',
    },
    component: () => import('../../pages/IntelligentMarketing/CreateStrategy.vue'),
    props: {
      formType: 'edit',
    },
  },
  {
    name: 'ViewStrategy',
    path: 'view-strategy/:strategyId',
    meta: {
      title: '策略详情', activeMenu: 'Smart_Operate_V2',
    },
    component: () => import('../../pages/IntelligentMarketing/CreateStrategy.vue'),
    props: {
      formType: 'view',
    },
  },
  {
    name: 'CopyStrategy',
    path: 'copy-strategy/:strategyId',
    meta: {
      title: '复制策略', activeMenu: 'Smart_Operate_V2',
    },
    component: () => import('../../pages/IntelligentMarketing/CreateStrategy.vue'),
    props: {
      formType: 'copy',
    },
  },
  {
    name: 'FuncRules',
    path: 'func-rules',
    meta: {
      title: '映射规则', activeMenu: 'Smart_Operate_V2',
    },
    component: () => import('../../pages/IntelligentMarketing/AlgorithmConfig/FuncRules/Index.vue'),
  },
  {
    name: 'FuncTemplates',
    path: 'func-templates',
    meta: {
      title: '策略模版', activeMenu: 'Smart_Operate_V2',
    },
    component: () => import('../../pages/IntelligentMarketing/AlgorithmConfig/FuncTemplates/Index.vue'),
  },
  {
    name: 'CreateFuncTemplate',
    path: 'create-func-template',
    meta: {
      title: '新建模版', activeMenu: 'Smart_Operate_V2',
    },
    component: () => import('../../pages/IntelligentMarketing/AlgorithmConfig/FuncTemplates/CreateFuncTemplate/Index.vue'),
    props: {
      formType: 'create',
    },
  },
  {
    name: 'CreateTree',
    path: 'create-tree',
    meta: {
      title: 'Group', activeMenu: 'Smart_Operate_V2',
    },
    component: () => import('../../pages/IntelligentMarketing/Group/Group.vue'),
    props: {
      formType: 'create',
    },
  },
  {
    name: 'PromotionCycle',
    path: 'promotion-cycle',
    meta: {
      title: '大促周期', activeMenu: 'Smart_Operate_V2',
    },
    component: () => import('../../pages/IntelligentMarketing/PromotionCycle/Index.vue'),
  },
  {
    name: 'PromotionCycleCreate',
    path: 'promotion-cycle-create',
    meta: {
      title: '新建大促周期', activeMenu: 'Smart_Operate_V2',
    },
    component: () => import('../../pages/IntelligentMarketing/PromotionCycle/Create.vue'),
  },
  {
    name: 'PromotionCycleDetail',
    path: 'promotion-cycle-detail',
    meta: {
      title: '大促周期详情', activeMenu: 'Smart_Operate_V2',
    },
    component: () => import('../../pages/IntelligentMarketing/PromotionCycle/Create.vue'),
  },
  // 编辑大促周期
  {
    name: 'EditPromotionCycle',
    path: 'edit-promotion-cycle/:id',
    meta: {
      title: '编辑大促周期', activeMenu: 'Smart_Operate_V2',
    },
    component: () => import('../../pages/IntelligentMarketing/PromotionCycle/Create.vue'),
  },
  {
    name: 'AnalyzeStrategyData',
    path: 'analyze-strategy-data/:strategyId',
    meta: {
      title: '调试数据', activeMenu: 'Smart_Operate_V2',
    },
    component: () => import('../../pages/IntelligentMarketing/Group/AnalyzeStrategyData.vue'),
  },
]

export default intelligentMarketingRoutes
