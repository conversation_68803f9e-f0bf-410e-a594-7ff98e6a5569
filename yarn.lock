# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ant-design/colors@^4.0.5":
  version "4.0.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@ant-design/colors/-/colors-4.0.5.tgz#d7d100d7545cca8f624954604a6892fc48ba5aae"
  integrity sha512-3mnuX2prnWOWvpFTS2WH2LoouWlOgtnIpc6IarWN6GOzzLF8dW/U8UctuvIPhoboETehZfJ61XP+CGakBEPJ3Q==
  dependencies:
    tinycolor2 "^1.4.1"

"@antv/algorithm@^0.1.8":
  version "0.1.26"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/algorithm/-/algorithm-0.1.26.tgz#e3f5e7f1d8db5b415c3f31e32b119cbcafc8f5de"
  integrity sha512-DVhcFSQ8YQnMNW34Mk8BSsfc61iC1sAnmcfYoXTAshYHuU50p/6b7x3QYaGctDNKWGvi1ub7mPcSY0bK+aN0qg==
  dependencies:
    "@antv/util" "^2.0.13"
    tslib "^2.0.0"

"@antv/dom-util@^2.0.1", "@antv/dom-util@^2.0.2":
  version "2.0.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/dom-util/-/dom-util-2.0.4.tgz#b09b56c56fec42896fc856edad56b595b47ab514"
  integrity sha512-2shXUl504fKwt82T3GkuT4Uoc6p9qjCKnJ8gXGLSW4T1W37dqf9AV28aCfoVPHp2BUXpSsB+PAJX2rG/jLHsLQ==
  dependencies:
    tslib "^2.0.3"

"@antv/event-emitter@^0.1.1", "@antv/event-emitter@~0.1.0":
  version "0.1.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/event-emitter/-/event-emitter-0.1.3.tgz#3e06323b9dcd55a3241ddc7c5458cfabd2095164"
  integrity sha512-4ddpsiHN9Pd4UIlWuKVK1C4IiZIdbwQvy9i7DUSI3xNJ89FPUFt8lxDYj8GzzfdllV0NkJTRxnG+FvLk0llidg==

"@antv/g-base@^0.5.1", "@antv/g-base@^0.5.12":
  version "0.5.15"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/g-base/-/g-base-0.5.15.tgz#4d4174c956ba652a4b55c4f6a2c28aaa92e7738d"
  integrity sha512-QOtq50QpnKez9J75/Z8j2yZ7QDQdk8R8mVQJiHtaEO5eI7DM4ZbrsWff/Ew26JYmPWdq7nbRuARMAD4PX9uuLA==
  dependencies:
    "@antv/event-emitter" "^0.1.1"
    "@antv/g-math" "^0.1.9"
    "@antv/matrix-util" "^3.1.0-beta.1"
    "@antv/path-util" "~2.0.5"
    "@antv/util" "~2.0.13"
    "@types/d3-timer" "^2.0.0"
    d3-ease "^1.0.5"
    d3-interpolate "^3.0.1"
    d3-timer "^1.0.9"
    detect-browser "^5.1.0"
    tslib "^2.0.3"

"@antv/g-canvas@^0.5.2":
  version "0.5.14"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/g-canvas/-/g-canvas-0.5.14.tgz#099668cb65d9c89dc2fc1000313c18298dcf8a13"
  integrity sha512-IUGLEMIMAUYgaBMT8h3FTmYQYz7sjQkKWwh6Psqx+UPK86fySa+G8fMRrh1EqAL07jVB+GRnn6Ym+3FoFUgeFg==
  dependencies:
    "@antv/g-base" "^0.5.12"
    "@antv/g-math" "^0.1.9"
    "@antv/matrix-util" "^3.1.0-beta.1"
    "@antv/path-util" "~2.0.5"
    "@antv/util" "~2.0.0"
    gl-matrix "^3.0.0"
    tslib "^2.0.3"

"@antv/g-math@^0.1.1", "@antv/g-math@^0.1.9":
  version "0.1.9"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/g-math/-/g-math-0.1.9.tgz#1f981b9aebf5c024f284389aa3e5cba8cefa1f28"
  integrity sha512-KHMSfPfZ5XHM1PZnG42Q2gxXfOitYveNTA7L61lR6mhZ8Y/aExsYmHqaKBsSarU0z+6WLrl9C07PQJZaw0uljQ==
  dependencies:
    "@antv/util" "~2.0.0"
    gl-matrix "^3.0.0"

"@antv/g-svg@^0.5.1", "@antv/g-svg@^0.5.2":
  version "0.5.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/g-svg/-/g-svg-0.5.7.tgz#d63db5f8590a5f3ceab097c183ec80ed143f0a50"
  integrity sha512-jUbWoPgr4YNsOat2Y/rGAouNQYGpw4R0cvlN0YafwOyacFFYy2zC8RslNd6KkPhhR3XHNSqJOuCYZj/YmLUwYw==
  dependencies:
    "@antv/g-base" "^0.5.12"
    "@antv/g-math" "^0.1.9"
    "@antv/util" "~2.0.0"
    detect-browser "^5.0.0"
    tslib "^2.0.3"

"@antv/g-webgpu-core@^0.7.2":
  version "0.7.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/g-webgpu-core/-/g-webgpu-core-0.7.2.tgz#65ef2a1253e319ffb2ed568d222b8313635d6677"
  integrity sha512-xUMmop7f3Rs34zFYKXLqHhDR1CQTeDl/7vI7Sn3X/73BqJc3X3HIIRvm83Fg2CjVACaOzw4WeLRXNaOCp9fz9w==
  dependencies:
    eventemitter3 "^4.0.0"
    gl-matrix "^3.1.0"
    lodash "^4.17.15"
    probe.gl "^3.1.1"

"@antv/g-webgpu-engine@^0.7.2":
  version "0.7.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/g-webgpu-engine/-/g-webgpu-engine-0.7.2.tgz#64631f24930f449ef41ffff6429d4fb9519eca23"
  integrity sha512-lx8Y93IW2cnJvdoDRKyMmTdYqSC1pOmF0nyG3PGGyA0NI9vBYVgO0KTF6hkyWjdTWVq7XDZyf/h8CJridLh3lg==
  dependencies:
    "@antv/g-webgpu-core" "^0.7.2"
    gl-matrix "^3.1.0"
    lodash "^4.17.15"
    regl "^1.3.11"

"@antv/g-webgpu@0.7.2":
  version "0.7.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/g-webgpu/-/g-webgpu-0.7.2.tgz#39ba2123816322fec9563236211ad8ab1e40924d"
  integrity sha512-kw+oYGsdvj5qeUfy5DPb/jztZBV+2fmqBd3Vv8NlKatfBmv8AirYX/CCW74AUSdWm99rEiLyxFB1VdRZ6b/wnQ==
  dependencies:
    "@antv/g-webgpu-core" "^0.7.2"
    "@antv/g-webgpu-engine" "^0.7.2"
    gl-matrix "^3.1.0"
    gl-vec2 "^1.3.0"
    lodash "^4.17.15"

"@antv/g6-core@0.8.21":
  version "0.8.21"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/g6-core/-/g6-core-0.8.21.tgz#efb55365e047f304a178eccc1249cd4feedb30c4"
  integrity sha512-zgOn/OW6/RSCPopiNseiLJc0oVV10yoJYS6phcQxceQQHSfwofFkEN2OQvpCCCQ2aMZ9rOkeYNvSdLnAzYyCKg==
  dependencies:
    "@antv/algorithm" "^0.1.8"
    "@antv/dom-util" "^2.0.1"
    "@antv/event-emitter" "~0.1.0"
    "@antv/g-base" "^0.5.1"
    "@antv/g-math" "^0.1.1"
    "@antv/matrix-util" "^3.1.0-beta.3"
    "@antv/path-util" "^2.0.3"
    "@antv/util" "~2.0.5"
    ml-matrix "^6.5.0"
    tslib "^2.1.0"

"@antv/g6-element@0.8.21":
  version "0.8.21"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/g6-element/-/g6-element-0.8.21.tgz#a1406a4afc203d6de7fa4d81b70acd5633547f24"
  integrity sha512-tf+aHYUdbmF1qCCKf15HXJnyV0VZHaE5sxG04caKkF3AB5gXjvYCp+f9cD1lKVy1nfZHUcGCPaWPK8lz/H/9gQ==
  dependencies:
    "@antv/g-base" "^0.5.1"
    "@antv/g6-core" "0.8.21"
    "@antv/util" "~2.0.5"

"@antv/g6-pc@0.8.21":
  version "0.8.21"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/g6-pc/-/g6-pc-0.8.21.tgz#5d25d60d740c74d5fe6f3a641eec337d0a4db256"
  integrity sha512-x2+JcLkjToZ4U06aa1W9qJHB3UERqYPqUsFs/POSsFtwF80HmG7iBKeu53k3XfqZ9N2/W4WeoDvx20/NgLolCg==
  dependencies:
    "@ant-design/colors" "^4.0.5"
    "@antv/algorithm" "^0.1.8"
    "@antv/dom-util" "^2.0.1"
    "@antv/event-emitter" "~0.1.0"
    "@antv/g-base" "^0.5.1"
    "@antv/g-canvas" "^0.5.2"
    "@antv/g-math" "^0.1.1"
    "@antv/g-svg" "^0.5.1"
    "@antv/g6-core" "0.8.21"
    "@antv/g6-element" "0.8.21"
    "@antv/g6-plugin" "0.8.21"
    "@antv/hierarchy" "^0.6.10"
    "@antv/layout" "^0.3.0"
    "@antv/matrix-util" "^3.1.0-beta.3"
    "@antv/path-util" "^2.0.3"
    "@antv/util" "~2.0.5"
    color "^3.1.3"
    d3-force "^2.0.1"
    dagre "^0.8.5"
    insert-css "^2.0.0"
    ml-matrix "^6.5.0"

"@antv/g6-plugin@0.8.21":
  version "0.8.21"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/g6-plugin/-/g6-plugin-0.8.21.tgz#ac35eeba410573d87eeb1e49915f5595cdaedab6"
  integrity sha512-gRRR8d+F/8CChwwH1rPkAESwLlW8c+ZbuszFn3WOiu7SjSQqNk4LGoNAHgCEUO+jDvAj4xeRZDGl0vlY2oqaSQ==
  dependencies:
    "@antv/dom-util" "^2.0.2"
    "@antv/g-base" "^0.5.1"
    "@antv/g-canvas" "^0.5.2"
    "@antv/g-svg" "^0.5.2"
    "@antv/g6-core" "0.8.21"
    "@antv/g6-element" "0.8.21"
    "@antv/matrix-util" "^3.1.0-beta.3"
    "@antv/path-util" "^2.0.3"
    "@antv/scale" "^0.3.4"
    "@antv/util" "^2.0.9"
    insert-css "^2.0.0"

"@antv/g6@^4.8.21":
  version "4.8.21"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/g6/-/g6-4.8.21.tgz#a79b0692c0debe0331e0c02af0b420c9aec06c93"
  integrity sha512-0MQT9cU3bKTpFTzcZsR2tQAsDy/FGIzQwCiSR9BhVkDgtwg6bs176ZdWia+/QflWOAg5tQ/C82wnqBXAzpGZnQ==
  dependencies:
    "@antv/g6-pc" "0.8.21"

"@antv/graphlib@^1.0.0":
  version "1.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/graphlib/-/graphlib-1.2.0.tgz#c88f97d4b3456d261480a1207ffc4fbc5d7dcf0d"
  integrity sha512-hhJOMThec51nU4Fe5p/viLlNIL71uDEgYFzKPajWjr2715SFG1HAgiP6AVylIeqBcAZ04u3Lw7usjl/TuI5RuQ==

"@antv/hierarchy@^0.6.10":
  version "0.6.11"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/hierarchy/-/hierarchy-0.6.11.tgz#244d6820347170e0107f3611802d1e5bb089ca7a"
  integrity sha512-RJVhEMCuu4vj+Dt25lXIiNdd7jaqm/fqWGYikiELha4S5tnzdJoTUaUvvpfWlxLx4B0RsS9XRwBs1bOKN71TKg==
  dependencies:
    "@antv/util" "^2.0.7"

"@antv/layout@^0.3.0":
  version "0.3.23"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/layout/-/layout-0.3.23.tgz#00e5f47cf29841808afb233f0585565d793ef2b7"
  integrity sha512-F/CyfQuc1WSgCVemX0jA3pE3XuDRbDJmMueY1cL8WgL6nhdzm3/jg5UPamwbBVnhLk+rzNUDYdEIyX+RJbpcMA==
  dependencies:
    "@antv/g-webgpu" "0.7.2"
    "@antv/graphlib" "^1.0.0"
    "@antv/util" "^3.3.2"
    d3-force "^2.1.1"
    d3-quadtree "^2.0.0"
    dagre-compound "^0.0.11"
    ml-matrix "^6.5.0"

"@antv/matrix-util@^3.0.4":
  version "3.0.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/matrix-util/-/matrix-util-3.0.4.tgz#ea13f158aa2fb4ba2fb8d6b6b561ec467ea3ac20"
  integrity sha512-BAPyu6dUliHcQ7fm9hZSGKqkwcjEDVLVAstlHULLvcMZvANHeLXgHEgV7JqcAV/GIhIz8aZChIlzM1ZboiXpYQ==
  dependencies:
    "@antv/util" "^2.0.9"
    gl-matrix "^3.3.0"
    tslib "^2.0.3"

"@antv/matrix-util@^3.1.0-beta.1", "@antv/matrix-util@^3.1.0-beta.3":
  version "3.1.0-beta.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/matrix-util/-/matrix-util-3.1.0-beta.3.tgz#e061de8fa7be04605a155c69cc5ce9082eedddee"
  integrity sha512-W2R6Za3A6CmG51Y/4jZUM/tFgYSq7vTqJL1VD9dKrvwxS4sE0ZcXINtkp55CdyBwJ6Cwm8pfoRpnD4FnHahN0A==
  dependencies:
    "@antv/util" "^2.0.9"
    gl-matrix "^3.4.3"
    tslib "^2.0.3"

"@antv/path-util@^2.0.3", "@antv/path-util@~2.0.5":
  version "2.0.15"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/path-util/-/path-util-2.0.15.tgz#a6f691dfc8b7bce5be7f0aabb5bd614964325631"
  integrity sha512-R2VLZ5C8PLPtr3VciNyxtjKqJ0XlANzpFb5sE9GE61UQqSRuSVSzIakMxjEPrpqbgc+s+y8i+fmc89Snu7qbNw==
  dependencies:
    "@antv/matrix-util" "^3.0.4"
    "@antv/util" "^2.0.9"
    tslib "^2.0.3"

"@antv/scale@^0.3.4":
  version "0.3.18"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/scale/-/scale-0.3.18.tgz#b911f431b3e0b9547b6a65f66d0d3fa295b5ef32"
  integrity sha512-GHwE6Lo7S/Q5fgaLPaCsW+CH+3zl4aXpnN1skOiEY0Ue9/u+s2EySv6aDXYkAqs//i0uilMDD/0/4n8caX9U9w==
  dependencies:
    "@antv/util" "~2.0.3"
    fecha "~4.2.0"
    tslib "^2.0.0"

"@antv/util@^2.0.13", "@antv/util@^2.0.7", "@antv/util@^2.0.9", "@antv/util@~2.0.0", "@antv/util@~2.0.13", "@antv/util@~2.0.3", "@antv/util@~2.0.5":
  version "2.0.17"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/util/-/util-2.0.17.tgz#e8ef42aca7892815b229269f3dd10c6b3c7597a9"
  integrity sha512-o6I9hi5CIUvLGDhth0RxNSFDRwXeywmt6ExR4+RmVAzIi48ps6HUy+svxOCayvrPBN37uE6TAc2KDofRo0nK9Q==
  dependencies:
    csstype "^3.0.8"
    tslib "^2.0.3"

"@antv/util@^3.3.2":
  version "3.3.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/util/-/util-3.3.4.tgz#305090df484a45f3c6fbab1c80a2e1885a452fab"
  integrity sha512-NGRjPCPje8GIC14Ye7sjebamFIjxoZ+mCIqfXz6wD/M6fA9SgJivzmLB3Ry01Wq8PI36oOVv9BwrAGV1JD8vjA==
  dependencies:
    fast-deep-equal "^3.1.3"
    gl-matrix "^3.3.0"
    tslib "^2.3.1"

"@babel/code-frame@^7.0.0":
  version "7.18.6"
  resolved "http://npm.devops.xiaohongshu.com:7001/@babel/code-frame/download/@babel/code-frame-7.18.6.tgz#3b25d38c89600baa2dcc219edfa88a74eb2c427a"
  integrity sha1-OyXTjIlgC6otzCGe36iKdOssQno=
  dependencies:
    "@babel/highlight" "^7.18.6"

"@babel/helper-string-parser@^7.19.4":
  version "7.19.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/@babel/helper-string-parser/download/@babel/helper-string-parser-7.19.4.tgz#38d3acb654b4701a9b77fb0615a96f775c3a9e63"
  integrity sha1-ONOstlS0cBqbd/sGFalvd1w6nmM=

"@babel/helper-validator-identifier@^7.18.6", "@babel/helper-validator-identifier@^7.19.1":
  version "7.19.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.19.1.tgz#7eea834cf32901ffdc1a7ee555e2f9c27e249ca2"
  integrity sha1-fuqDTPMpAf/cGn7lVeL5wn4knKI=

"@babel/highlight@^7.18.6":
  version "7.18.6"
  resolved "http://npm.devops.xiaohongshu.com:7001/@babel/highlight/download/@babel/highlight-7.18.6.tgz#81158601e93e2563795adcbfbdf5d64be3f2ecdf"
  integrity sha1-gRWGAek+JWN5Wty/vfXWS+Py7N8=
  dependencies:
    "@babel/helper-validator-identifier" "^7.18.6"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@babel/parser@^7.16.4":
  version "7.19.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/@babel/parser/download/@babel/parser-7.19.3.tgz#8dd36d17c53ff347f9e55c328710321b49479a9a"
  integrity sha1-jdNtF8U/80f55VwyhxAyG0lHmpo=

"@babel/runtime-corejs2@^7.10.2":
  version "7.21.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@babel/runtime-corejs2/download/@babel/runtime-corejs2-7.21.0.tgz#79374a14846b38764077089707566066721bcc16"
  integrity sha1-eTdKFIRrOHZAdwiXB1ZgZnIbzBY=
  dependencies:
    core-js "^2.6.12"
    regenerator-runtime "^0.13.11"

"@babel/runtime@^7.0.0":
  version "7.21.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@babel/runtime/download/@babel/runtime-7.21.5.tgz#8492dddda9644ae3bda3b45eabe87382caee7200"
  integrity sha1-hJLd3alkSuO9o7Req+hzgsrucgA=
  dependencies:
    regenerator-runtime "^0.13.11"

"@babel/runtime@^7.10.5", "@babel/runtime@^7.7.2":
  version "7.19.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@babel/runtime/download/@babel/runtime-7.19.0.tgz#22b11c037b094d27a8a2504ea4dcff00f50e2259"
  integrity sha1-IrEcA3sJTSeoolBOpNz/APUOIlk=
  dependencies:
    regenerator-runtime "^0.13.4"

"@babel/runtime@^7.12.0":
  version "7.22.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@babel/runtime/download/@babel/runtime-7.22.5.tgz#8564dd588182ce0047d55d7a75e93921107b57ec"
  integrity sha1-hWTdWIGCzgBH1V16dek5IRB7V+w=
  dependencies:
    regenerator-runtime "^0.13.11"

"@babel/types@^7.16.8":
  version "7.21.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/@babel/types/download/@babel/types-7.21.4.tgz#2d5d6bb7908699b3b416409ffd3b5daa25b030d4"
  integrity sha1-LV1rt5CGmbO0FkCf/TtdqiWwMNQ=
  dependencies:
    "@babel/helper-string-parser" "^7.19.4"
    "@babel/helper-validator-identifier" "^7.19.1"
    to-fast-properties "^2.0.0"

"@blueprintjs/icons@^3.10.0":
  version "3.33.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@blueprintjs/icons/download/@blueprintjs/icons-3.33.0.tgz#4dacdb7731abdf08d1ab240f3a23a185df60918b"
  integrity sha1-TazbdzGr3wjRqyQPOiOhhd9gkYs=
  dependencies:
    classnames "^2.2"
    tslib "~2.3.1"

"@bufbuild/connect-node@~0.8.5":
  version "0.8.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@bufbuild/connect-node/download/@bufbuild/connect-node-0.8.5.tgz#a7728e6e64e7e9489ac8e254ea4b0990de8e8cde"
  integrity sha1-p3KObmTn6UiayOJU6ksJkN6OjN4=
  dependencies:
    "@bufbuild/connect" "0.8.5"
    headers-polyfill "^3.1.2"

"@bufbuild/connect@0.8.5", "@bufbuild/connect@~0.8.5":
  version "0.8.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@bufbuild/connect/download/@bufbuild/connect-0.8.5.tgz#5ace863108c846c31085b77e9a03e60ee1c443c3"
  integrity sha1-Ws6GMQjIRsMQhbd+mgPmDuHEQ8M=

"@bufbuild/protobuf@1.2.0", "@bufbuild/protobuf@^1.2.0", "@bufbuild/protobuf@~1.2.0":
  version "1.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@bufbuild/protobuf/download/@bufbuild/protobuf-1.2.0.tgz#7efd5d4aa0540b108eb3b89b42507739e0974299"
  integrity sha1-fv1dSqBUCxCOs7ibQlB3OeCXQpk=

"@bufbuild/protoc-gen-connect-es@~0.8.5":
  version "0.8.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@bufbuild/protoc-gen-connect-es/download/@bufbuild/protoc-gen-connect-es-0.8.5.tgz#2002d36c780a23d04570f0d90e117d32bc6b28ac"
  integrity sha1-IALTbHgKI9BFcPDZDhF9MrxrKKw=
  dependencies:
    "@bufbuild/protobuf" "^1.2.0"
    "@bufbuild/protoplugin" "^1.2.0"

"@bufbuild/protoc-gen-es@~1.2.0":
  version "1.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@bufbuild/protoc-gen-es/download/@bufbuild/protoc-gen-es-1.2.0.tgz#80ee6bb58bb5c695cc386f7c5537249bd8c0bdbe"
  integrity sha1-gO5rtYu1xpXMOG98VTckm9jAvb4=
  dependencies:
    "@bufbuild/protoplugin" "1.2.0"

"@bufbuild/protoplugin@1.2.0", "@bufbuild/protoplugin@^1.2.0":
  version "1.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@bufbuild/protoplugin/download/@bufbuild/protoplugin-1.2.0.tgz#ec7cdb477047e009cc38ef79147de8c7329ecf4a"
  integrity sha1-7HzbR3BH4AnMOO95FH3oxzKez0o=
  dependencies:
    "@bufbuild/protobuf" "1.2.0"
    "@typescript/vfs" "^1.4.0"
    typescript "4.5.2"

"@emotion/cache@^10.0.15":
  version "10.0.29"
  resolved "http://npm.devops.xiaohongshu.com:7001/@emotion/cache/download/@emotion/cache-10.0.29.tgz#87e7e64f412c060102d589fe7c6dc042e6f9d1e0"
  integrity sha1-h+fmT0EsBgEC1Yn+fG3AQub50eA=
  dependencies:
    "@emotion/sheet" "0.9.4"
    "@emotion/stylis" "0.8.5"
    "@emotion/utils" "0.11.3"
    "@emotion/weak-memoize" "0.2.5"

"@emotion/hash@0.8.0":
  version "0.8.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@emotion/hash/download/@emotion/hash-0.8.0.tgz#bbbff68978fefdbe68ccb533bc8cbe1d1afb5413"
  integrity sha1-u7/2iXj+/b5ozLUzvIy+HRr7VBM=

"@emotion/memoize@0.7.4":
  version "0.7.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/@emotion/memoize/download/@emotion/memoize-0.7.4.tgz#19bf0f5af19149111c40d98bb0cf82119f5d9eeb"
  integrity sha1-Gb8PWvGRSREcQNmLsM+CEZ9dnus=

"@emotion/serialize@^0.11.9":
  version "0.11.16"
  resolved "http://npm.devops.xiaohongshu.com:7001/@emotion/serialize/download/@emotion/serialize-0.11.16.tgz#dee05f9e96ad2fb25a5206b6d759b2d1ed3379ad"
  integrity sha1-3uBfnpatL7JaUga211my0e0zea0=
  dependencies:
    "@emotion/hash" "0.8.0"
    "@emotion/memoize" "0.7.4"
    "@emotion/unitless" "0.7.5"
    "@emotion/utils" "0.11.3"
    csstype "^2.5.7"

"@emotion/sheet@0.9.4", "@emotion/sheet@^0.9.3":
  version "0.9.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/@emotion/sheet/download/@emotion/sheet-0.9.4.tgz#894374bea39ec30f489bbfc3438192b9774d32e5"
  integrity sha1-iUN0vqOeww9Im7/DQ4GSuXdNMuU=

"@emotion/stylis@0.8.5":
  version "0.8.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@emotion/stylis/download/@emotion/stylis-0.8.5.tgz#deacb389bd6ee77d1e7fcaccce9e16c5c7e78e04"
  integrity sha1-3qyzib1u530ef8rMzp4WxcfnjgQ=

"@emotion/unitless@0.7.5":
  version "0.7.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@emotion/unitless/download/@emotion/unitless-0.7.5.tgz#77211291c1900a700b8a78cfafda3160d76949ed"
  integrity sha1-dyESkcGQCnALinjPr9oxYNdpSe0=

"@emotion/utils@0.11.3", "@emotion/utils@^0.11.2":
  version "0.11.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/@emotion/utils/download/@emotion/utils-0.11.3.tgz#a759863867befa7e583400d322652a3f44820924"
  integrity sha1-p1mGOGe++n5YNADTImUqP0SCCSQ=

"@emotion/weak-memoize@0.2.5":
  version "0.2.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@emotion/weak-memoize/download/@emotion/weak-memoize-0.2.5.tgz#8eed982e2ee6f7f4e44c253e12962980791efd46"
  integrity sha1-ju2YLi7m9/TkTCU+EpYpgHke/UY=

"@evocateur/libnpmaccess@^3.1.2":
  version "3.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/@evocateur/libnpmaccess/download/@evocateur/libnpmaccess-3.1.2.tgz#ecf7f6ce6b004e9f942b098d92200be4a4b1c845"
  integrity sha1-7Pf2zmsATp+UKwmNkiAL5KSxyEU=
  dependencies:
    "@evocateur/npm-registry-fetch" "^4.0.0"
    aproba "^2.0.0"
    figgy-pudding "^3.5.1"
    get-stream "^4.0.0"
    npm-package-arg "^6.1.0"

"@evocateur/libnpmpublish@^1.2.2":
  version "1.2.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/@evocateur/libnpmpublish/download/@evocateur/libnpmpublish-1.2.2.tgz#55df09d2dca136afba9c88c759ca272198db9f1a"
  integrity sha1-Vd8J0tyhNq+6nIjHWconIZjbnxo=
  dependencies:
    "@evocateur/npm-registry-fetch" "^4.0.0"
    aproba "^2.0.0"
    figgy-pudding "^3.5.1"
    get-stream "^4.0.0"
    lodash.clonedeep "^4.5.0"
    normalize-package-data "^2.4.0"
    npm-package-arg "^6.1.0"
    semver "^5.5.1"
    ssri "^6.0.1"

"@evocateur/npm-registry-fetch@^4.0.0":
  version "4.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@evocateur/npm-registry-fetch/download/@evocateur/npm-registry-fetch-4.0.0.tgz#8c4c38766d8d32d3200fcb0a83f064b57365ed66"
  integrity sha1-jEw4dm2NMtMgD8sKg/BktXNl7WY=
  dependencies:
    JSONStream "^1.3.4"
    bluebird "^3.5.1"
    figgy-pudding "^3.4.1"
    lru-cache "^5.1.1"
    make-fetch-happen "^5.0.0"
    npm-package-arg "^6.1.0"
    safe-buffer "^5.1.2"

"@evocateur/pacote@^9.6.3":
  version "9.6.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@evocateur/pacote/download/@evocateur/pacote-9.6.5.tgz#33de32ba210b6f17c20ebab4d497efc6755f4ae5"
  integrity sha1-M94yuiELbxfCDrq01JfvxnVfSuU=
  dependencies:
    "@evocateur/npm-registry-fetch" "^4.0.0"
    bluebird "^3.5.3"
    cacache "^12.0.3"
    chownr "^1.1.2"
    figgy-pudding "^3.5.1"
    get-stream "^4.1.0"
    glob "^7.1.4"
    infer-owner "^1.0.4"
    lru-cache "^5.1.1"
    make-fetch-happen "^5.0.0"
    minimatch "^3.0.4"
    minipass "^2.3.5"
    mississippi "^3.0.0"
    mkdirp "^0.5.1"
    normalize-package-data "^2.5.0"
    npm-package-arg "^6.1.0"
    npm-packlist "^1.4.4"
    npm-pick-manifest "^3.0.0"
    osenv "^0.1.5"
    promise-inflight "^1.0.1"
    promise-retry "^1.1.1"
    protoduck "^5.0.1"
    rimraf "^2.6.3"
    safe-buffer "^5.2.0"
    semver "^5.7.0"
    ssri "^6.0.1"
    tar "^4.4.10"
    unique-filename "^1.1.1"
    which "^1.3.1"

"@floating-ui/core@^0.3.0":
  version "0.3.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/@floating-ui/core/download/@floating-ui/core-0.3.1.tgz#3dde0ad0724d4b730567c92f49f0950910e18871"
  integrity sha1-Pd4K0HJNS3MFZ8kvSfCVCRDhiHE=

"@floating-ui/dom@^0.1.10":
  version "0.1.10"
  resolved "http://npm.devops.xiaohongshu.com:7001/@floating-ui/dom/download/@floating-ui/dom-0.1.10.tgz#ce304136a52c71ef157826d2ebf52d68fa2deed5"
  integrity sha1-zjBBNqUsce8VeCbS6/UtaPot7tU=
  dependencies:
    "@floating-ui/core" "^0.3.0"

"@formily/core@2.2.7":
  version "2.2.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/@formily/core/download/@formily/core-2.2.7.tgz#3957f96c51215b97de9f8a3f9ac7a41421a91f6f"
  integrity sha1-OVf5bFEhW5fen4o/msekFCGpH28=
  dependencies:
    "@formily/reactive" "2.2.7"
    "@formily/shared" "2.2.7"
    "@formily/validator" "2.2.7"

"@formily/grid@2.2.7":
  version "2.2.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/@formily/grid/download/@formily/grid-2.2.7.tgz#ab18cfb1ad45f9b462ac72afc153c515c372de51"
  integrity sha1-qxjPsa1F+bRirHKvwVPFFcNy3lE=
  dependencies:
    "@formily/reactive" "2.2.7"
    "@juggle/resize-observer" "^3.3.1"

"@formily/json-schema@2.2.7":
  version "2.2.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/@formily/json-schema/download/@formily/json-schema-2.2.7.tgz#a4212b8ba0751b3c45fce7b3a1393dee87708b27"
  integrity sha1-pCEri6B1GzxF/OezoTk97odwiyc=
  dependencies:
    "@formily/core" "2.2.7"
    "@formily/reactive" "2.2.7"
    "@formily/shared" "2.2.7"

"@formily/path@2.2.7":
  version "2.2.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/@formily/path/download/@formily/path-2.2.7.tgz#fab951658bc7dce6d5b373e3d89556f2c3a3d134"
  integrity sha1-+rlRZYvH3ObVs3Pj2JVW8sOj0TQ=

"@formily/reactive-vue@2.2.7":
  version "2.2.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/@formily/reactive-vue/download/@formily/reactive-vue-2.2.7.tgz#5c769124bb901c052e77aafd4fbf8fbb81d13625"
  integrity sha1-XHaRJLuQHAUud6r9T7+Pu4HRNiU=
  dependencies:
    "@formily/reactive" "2.2.7"
    vue-demi "^0.13.6"

"@formily/reactive@2.2.7":
  version "2.2.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/@formily/reactive/download/@formily/reactive-2.2.7.tgz#e803317abeb05802d985c388129b9e7584570717"
  integrity sha1-6AMxer6wWALZhcOIEpuedYRXBxc=

"@formily/shared@2.2.7":
  version "2.2.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/@formily/shared/download/@formily/shared-2.2.7.tgz#012985f6894471a9ac3fbd0451ca2efd04e69527"
  integrity sha1-ASmF9olEcamsP70EUcou/QTmlSc=
  dependencies:
    "@formily/path" "2.2.7"
    camel-case "^4.1.1"
    lower-case "^2.0.1"
    no-case "^3.0.4"
    param-case "^3.0.4"
    pascal-case "^3.1.1"
    upper-case "^2.0.1"

"@formily/validator@2.2.7":
  version "2.2.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/@formily/validator/download/@formily/validator-2.2.7.tgz#e8a8d13f373dc957e85828cc64158b07f258acdc"
  integrity sha1-6KjRPzc9yVfoWCjMZBWLB/JYrNw=
  dependencies:
    "@formily/shared" "2.2.7"

"@formily/vue@2.2.7":
  version "2.2.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/@formily/vue/download/@formily/vue-2.2.7.tgz#01f4aea3a6e80aa7e395d7bf21124b08e64b3398"
  integrity sha1-AfSuo6boCqfjlde/IRJLCOZLM5g=
  dependencies:
    "@formily/core" "2.2.7"
    "@formily/json-schema" "2.2.7"
    "@formily/reactive" "2.2.7"
    "@formily/reactive-vue" "2.2.7"
    "@formily/shared" "2.2.7"
    "@formily/validator" "2.2.7"
    fs-extra "^10.0.0"
    vue-demi "^0.13.6"
    vue-frag "^1.1.4"

"@icon-park/svg@1.4.1", "@icon-park/svg@^1.3.5":
  version "1.4.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/@icon-park/svg/download/@icon-park/svg-1.4.1.tgz#c73add6580136167afe45b23f3d90abd118a1749"
  integrity sha1-xzrdZYATYWev5Fsj89kKvRGKF0k=

"@juggle/resize-observer@^3.3.1":
  version "3.4.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@juggle/resize-observer/download/@juggle/resize-observer-3.4.0.tgz#08d6c5e20cf7e4cc02fd181c4b0c225cd31dbb60"
  integrity sha1-CNbF4gz35MwC/RgcSwwiXNMdu2A=

"@lerna/add@3.21.0":
  version "3.21.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/add/download/@lerna/add-3.21.0.tgz#27007bde71cc7b0a2969ab3c2f0ae41578b4577b"
  integrity sha1-JwB73nHMewopaas8LwrkFXi0V3s=
  dependencies:
    "@evocateur/pacote" "^9.6.3"
    "@lerna/bootstrap" "3.21.0"
    "@lerna/command" "3.21.0"
    "@lerna/filter-options" "3.20.0"
    "@lerna/npm-conf" "3.16.0"
    "@lerna/validation-error" "3.13.0"
    dedent "^0.7.0"
    npm-package-arg "^6.1.0"
    p-map "^2.1.0"
    semver "^6.2.0"

"@lerna/bootstrap@3.21.0":
  version "3.21.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/bootstrap/download/@lerna/bootstrap-3.21.0.tgz#bcd1b651be5b0970b20d8fae04c864548123aed6"
  integrity sha1-vNG2Ub5bCXCyDY+uBMhkVIEjrtY=
  dependencies:
    "@lerna/command" "3.21.0"
    "@lerna/filter-options" "3.20.0"
    "@lerna/has-npm-version" "3.16.5"
    "@lerna/npm-install" "3.16.5"
    "@lerna/package-graph" "3.18.5"
    "@lerna/pulse-till-done" "3.13.0"
    "@lerna/rimraf-dir" "3.16.5"
    "@lerna/run-lifecycle" "3.16.2"
    "@lerna/run-topologically" "3.18.5"
    "@lerna/symlink-binary" "3.17.0"
    "@lerna/symlink-dependencies" "3.17.0"
    "@lerna/validation-error" "3.13.0"
    dedent "^0.7.0"
    get-port "^4.2.0"
    multimatch "^3.0.0"
    npm-package-arg "^6.1.0"
    npmlog "^4.1.2"
    p-finally "^1.0.0"
    p-map "^2.1.0"
    p-map-series "^1.0.0"
    p-waterfall "^1.0.0"
    read-package-tree "^5.1.6"
    semver "^6.2.0"

"@lerna/changed@3.21.0":
  version "3.21.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/changed/download/@lerna/changed-3.21.0.tgz#108e15f679bfe077af500f58248c634f1044ea0b"
  integrity sha1-EI4V9nm/4HevUA9YJIxjTxBE6gs=
  dependencies:
    "@lerna/collect-updates" "3.20.0"
    "@lerna/command" "3.21.0"
    "@lerna/listable" "3.18.5"
    "@lerna/output" "3.13.0"

"@lerna/check-working-tree@3.16.5":
  version "3.16.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/check-working-tree/download/@lerna/check-working-tree-3.16.5.tgz#b4f8ae61bb4523561dfb9f8f8d874dd46bb44baa"
  integrity sha1-tPiuYbtFI1Yd+5+PjYdN1Gu0S6o=
  dependencies:
    "@lerna/collect-uncommitted" "3.16.5"
    "@lerna/describe-ref" "3.16.5"
    "@lerna/validation-error" "3.13.0"

"@lerna/child-process@3.16.5":
  version "3.16.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/child-process/download/@lerna/child-process-3.16.5.tgz#38fa3c18064aa4ac0754ad80114776a7b36a69b2"
  integrity sha1-OPo8GAZKpKwHVK2AEUd2p7NqabI=
  dependencies:
    chalk "^2.3.1"
    execa "^1.0.0"
    strong-log-transformer "^2.0.0"

"@lerna/clean@3.21.0":
  version "3.21.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/clean/download/@lerna/clean-3.21.0.tgz#c0b46b5300cc3dae2cda3bec14b803082da3856d"
  integrity sha1-wLRrUwDMPa4s2jvsFLgDCC2jhW0=
  dependencies:
    "@lerna/command" "3.21.0"
    "@lerna/filter-options" "3.20.0"
    "@lerna/prompt" "3.18.5"
    "@lerna/pulse-till-done" "3.13.0"
    "@lerna/rimraf-dir" "3.16.5"
    p-map "^2.1.0"
    p-map-series "^1.0.0"
    p-waterfall "^1.0.0"

"@lerna/cli@3.18.5":
  version "3.18.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/cli/download/@lerna/cli-3.18.5.tgz#c90c461542fcd35b6d5b015a290fb0dbfb41d242"
  integrity sha1-yQxGFUL801ttWwFaKQ+w2/tB0kI=
  dependencies:
    "@lerna/global-options" "3.13.0"
    dedent "^0.7.0"
    npmlog "^4.1.2"
    yargs "^14.2.2"

"@lerna/collect-uncommitted@3.16.5":
  version "3.16.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/collect-uncommitted/download/@lerna/collect-uncommitted-3.16.5.tgz#a494d61aac31cdc7aec4bbe52c96550274132e63"
  integrity sha1-pJTWGqwxzceuxLvlLJZVAnQTLmM=
  dependencies:
    "@lerna/child-process" "3.16.5"
    chalk "^2.3.1"
    figgy-pudding "^3.5.1"
    npmlog "^4.1.2"

"@lerna/collect-updates@3.20.0":
  version "3.20.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/collect-updates/download/@lerna/collect-updates-3.20.0.tgz#62f9d76ba21a25b7d9fbf31c02de88744a564bd1"
  integrity sha1-YvnXa6IaJbfZ+/McAt6IdEpWS9E=
  dependencies:
    "@lerna/child-process" "3.16.5"
    "@lerna/describe-ref" "3.16.5"
    minimatch "^3.0.4"
    npmlog "^4.1.2"
    slash "^2.0.0"

"@lerna/command@3.21.0":
  version "3.21.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/command/download/@lerna/command-3.21.0.tgz#9a2383759dc7b700dacfa8a22b2f3a6e190121f7"
  integrity sha1-miODdZ3HtwDaz6iiKy86bhkBIfc=
  dependencies:
    "@lerna/child-process" "3.16.5"
    "@lerna/package-graph" "3.18.5"
    "@lerna/project" "3.21.0"
    "@lerna/validation-error" "3.13.0"
    "@lerna/write-log-file" "3.13.0"
    clone-deep "^4.0.1"
    dedent "^0.7.0"
    execa "^1.0.0"
    is-ci "^2.0.0"
    npmlog "^4.1.2"

"@lerna/conventional-commits@3.22.0":
  version "3.22.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/conventional-commits/download/@lerna/conventional-commits-3.22.0.tgz#2798f4881ee2ef457bdae027ab7d0bf0af6f1e09"
  integrity sha1-J5j0iB7i70V72uAnq30L8K9vHgk=
  dependencies:
    "@lerna/validation-error" "3.13.0"
    conventional-changelog-angular "^5.0.3"
    conventional-changelog-core "^3.1.6"
    conventional-recommended-bump "^5.0.0"
    fs-extra "^8.1.0"
    get-stream "^4.0.0"
    lodash.template "^4.5.0"
    npm-package-arg "^6.1.0"
    npmlog "^4.1.2"
    pify "^4.0.1"
    semver "^6.2.0"

"@lerna/create-symlink@3.16.2":
  version "3.16.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/create-symlink/download/@lerna/create-symlink-3.16.2.tgz#412cb8e59a72f5a7d9463e4e4721ad2070149967"
  integrity sha1-QSy45Zpy9afZRj5ORyGtIHAUmWc=
  dependencies:
    "@zkochan/cmd-shim" "^3.1.0"
    fs-extra "^8.1.0"
    npmlog "^4.1.2"

"@lerna/create@3.22.0":
  version "3.22.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/create/download/@lerna/create-3.22.0.tgz#d6bbd037c3dc5b425fe5f6d1b817057c278f7619"
  integrity sha1-1rvQN8PcW0Jf5fbRuBcFfCePdhk=
  dependencies:
    "@evocateur/pacote" "^9.6.3"
    "@lerna/child-process" "3.16.5"
    "@lerna/command" "3.21.0"
    "@lerna/npm-conf" "3.16.0"
    "@lerna/validation-error" "3.13.0"
    camelcase "^5.0.0"
    dedent "^0.7.0"
    fs-extra "^8.1.0"
    globby "^9.2.0"
    init-package-json "^1.10.3"
    npm-package-arg "^6.1.0"
    p-reduce "^1.0.0"
    pify "^4.0.1"
    semver "^6.2.0"
    slash "^2.0.0"
    validate-npm-package-license "^3.0.3"
    validate-npm-package-name "^3.0.0"
    whatwg-url "^7.0.0"

"@lerna/describe-ref@3.16.5":
  version "3.16.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/describe-ref/download/@lerna/describe-ref-3.16.5.tgz#a338c25aaed837d3dc70b8a72c447c5c66346ac0"
  integrity sha1-ozjCWq7YN9PccLinLER8XGY0asA=
  dependencies:
    "@lerna/child-process" "3.16.5"
    npmlog "^4.1.2"

"@lerna/diff@3.21.0":
  version "3.21.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/diff/download/@lerna/diff-3.21.0.tgz#e6df0d8b9916167ff5a49fcb02ac06424280a68d"
  integrity sha1-5t8Ni5kWFn/1pJ/LAqwGQkKApo0=
  dependencies:
    "@lerna/child-process" "3.16.5"
    "@lerna/command" "3.21.0"
    "@lerna/validation-error" "3.13.0"
    npmlog "^4.1.2"

"@lerna/exec@3.21.0":
  version "3.21.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/exec/download/@lerna/exec-3.21.0.tgz#17f07533893cb918a17b41bcc566dc437016db26"
  integrity sha1-F/B1M4k8uRihe0G8xWbcQ3AW2yY=
  dependencies:
    "@lerna/child-process" "3.16.5"
    "@lerna/command" "3.21.0"
    "@lerna/filter-options" "3.20.0"
    "@lerna/profiler" "3.20.0"
    "@lerna/run-topologically" "3.18.5"
    "@lerna/validation-error" "3.13.0"
    p-map "^2.1.0"

"@lerna/filter-options@3.20.0":
  version "3.20.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/filter-options/download/@lerna/filter-options-3.20.0.tgz#0f0f5d5a4783856eece4204708cc902cbc8af59b"
  integrity sha1-Dw9dWkeDhW7s5CBHCMyQLLyK9Zs=
  dependencies:
    "@lerna/collect-updates" "3.20.0"
    "@lerna/filter-packages" "3.18.0"
    dedent "^0.7.0"
    figgy-pudding "^3.5.1"
    npmlog "^4.1.2"

"@lerna/filter-packages@3.18.0":
  version "3.18.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/filter-packages/download/@lerna/filter-packages-3.18.0.tgz#6a7a376d285208db03a82958cfb8172e179b4e70"
  integrity sha1-ano3bShSCNsDqClYz7gXLhebTnA=
  dependencies:
    "@lerna/validation-error" "3.13.0"
    multimatch "^3.0.0"
    npmlog "^4.1.2"

"@lerna/get-npm-exec-opts@3.13.0":
  version "3.13.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/get-npm-exec-opts/download/@lerna/get-npm-exec-opts-3.13.0.tgz#d1b552cb0088199fc3e7e126f914e39a08df9ea5"
  integrity sha1-0bVSywCIGZ/D5+Em+RTjmgjfnqU=
  dependencies:
    npmlog "^4.1.2"

"@lerna/get-packed@3.16.0":
  version "3.16.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/get-packed/download/@lerna/get-packed-3.16.0.tgz#1b316b706dcee86c7baa55e50b087959447852ff"
  integrity sha1-GzFrcG3O6Gx7qlXlCwh5WUR4Uv8=
  dependencies:
    fs-extra "^8.1.0"
    ssri "^6.0.1"
    tar "^4.4.8"

"@lerna/github-client@3.22.0":
  version "3.22.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/github-client/download/@lerna/github-client-3.22.0.tgz#5d816aa4f76747ed736ae64ff962b8f15c354d95"
  integrity sha1-XYFqpPdnR+1zauZP+WK48Vw1TZU=
  dependencies:
    "@lerna/child-process" "3.16.5"
    "@octokit/plugin-enterprise-rest" "^6.0.1"
    "@octokit/rest" "^16.28.4"
    git-url-parse "^11.1.2"
    npmlog "^4.1.2"

"@lerna/gitlab-client@3.15.0":
  version "3.15.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/gitlab-client/download/@lerna/gitlab-client-3.15.0.tgz#91f4ec8c697b5ac57f7f25bd50fe659d24aa96a6"
  integrity sha1-kfTsjGl7WsV/fyW9UP5lnSSqlqY=
  dependencies:
    node-fetch "^2.5.0"
    npmlog "^4.1.2"
    whatwg-url "^7.0.0"

"@lerna/global-options@3.13.0":
  version "3.13.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/global-options/download/@lerna/global-options-3.13.0.tgz#217662290db06ad9cf2c49d8e3100ee28eaebae1"
  integrity sha1-IXZiKQ2watnPLEnY4xAO4o6uuuE=

"@lerna/has-npm-version@3.16.5":
  version "3.16.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/has-npm-version/download/@lerna/has-npm-version-3.16.5.tgz#ab83956f211d8923ea6afe9b979b38cc73b15326"
  integrity sha1-q4OVbyEdiSPqav6bl5s4zHOxUyY=
  dependencies:
    "@lerna/child-process" "3.16.5"
    semver "^6.2.0"

"@lerna/import@3.22.0":
  version "3.22.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/import/download/@lerna/import-3.22.0.tgz#1a5f0394f38e23c4f642a123e5e1517e70d068d2"
  integrity sha1-Gl8DlPOOI8T2QqEj5eFRfnDQaNI=
  dependencies:
    "@lerna/child-process" "3.16.5"
    "@lerna/command" "3.21.0"
    "@lerna/prompt" "3.18.5"
    "@lerna/pulse-till-done" "3.13.0"
    "@lerna/validation-error" "3.13.0"
    dedent "^0.7.0"
    fs-extra "^8.1.0"
    p-map-series "^1.0.0"

"@lerna/info@3.21.0":
  version "3.21.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/info/download/@lerna/info-3.21.0.tgz#76696b676fdb0f35d48c83c63c1e32bb5e37814f"
  integrity sha1-dmlrZ2/bDzXUjIPGPB4yu143gU8=
  dependencies:
    "@lerna/command" "3.21.0"
    "@lerna/output" "3.13.0"
    envinfo "^7.3.1"

"@lerna/init@3.21.0":
  version "3.21.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/init/download/@lerna/init-3.21.0.tgz#1e810934dc8bf4e5386c031041881d3b4096aa5c"
  integrity sha1-HoEJNNyL9OU4bAMQQYgdO0CWqlw=
  dependencies:
    "@lerna/child-process" "3.16.5"
    "@lerna/command" "3.21.0"
    fs-extra "^8.1.0"
    p-map "^2.1.0"
    write-json-file "^3.2.0"

"@lerna/link@3.21.0":
  version "3.21.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/link/download/@lerna/link-3.21.0.tgz#8be68ff0ccee104b174b5bbd606302c2f06e9d9b"
  integrity sha1-i+aP8MzuEEsXS1u9YGMCwvBunZs=
  dependencies:
    "@lerna/command" "3.21.0"
    "@lerna/package-graph" "3.18.5"
    "@lerna/symlink-dependencies" "3.17.0"
    p-map "^2.1.0"
    slash "^2.0.0"

"@lerna/list@3.21.0":
  version "3.21.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/list/download/@lerna/list-3.21.0.tgz#42f76fafa56dea13b691ec8cab13832691d61da2"
  integrity sha1-Qvdvr6Vt6hO2keyMqxODJpHWHaI=
  dependencies:
    "@lerna/command" "3.21.0"
    "@lerna/filter-options" "3.20.0"
    "@lerna/listable" "3.18.5"
    "@lerna/output" "3.13.0"

"@lerna/listable@3.18.5":
  version "3.18.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/listable/download/@lerna/listable-3.18.5.tgz#e82798405b5ed8fc51843c8ef1e7a0e497388a1a"
  integrity sha1-6CeYQFte2PxRhDyO8eeg5Jc4iho=
  dependencies:
    "@lerna/query-graph" "3.18.5"
    chalk "^2.3.1"
    columnify "^1.5.4"

"@lerna/log-packed@3.16.0":
  version "3.16.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/log-packed/download/@lerna/log-packed-3.16.0.tgz#f83991041ee77b2495634e14470b42259fd2bc16"
  integrity sha1-+DmRBB7neySVY04URwtCJZ/SvBY=
  dependencies:
    byte-size "^5.0.1"
    columnify "^1.5.4"
    has-unicode "^2.0.1"
    npmlog "^4.1.2"

"@lerna/npm-conf@3.16.0":
  version "3.16.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/npm-conf/download/@lerna/npm-conf-3.16.0.tgz#1c10a89ae2f6c2ee96962557738685300d376827"
  integrity sha1-HBComuL2wu6WliVXc4aFMA03aCc=
  dependencies:
    config-chain "^1.1.11"
    pify "^4.0.1"

"@lerna/npm-dist-tag@3.18.5":
  version "3.18.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/npm-dist-tag/download/@lerna/npm-dist-tag-3.18.5.tgz#9ef9abb7c104077b31f6fab22cc73b314d54ac55"
  integrity sha1-nvmrt8EEB3sx9vqyLMc7MU1UrFU=
  dependencies:
    "@evocateur/npm-registry-fetch" "^4.0.0"
    "@lerna/otplease" "3.18.5"
    figgy-pudding "^3.5.1"
    npm-package-arg "^6.1.0"
    npmlog "^4.1.2"

"@lerna/npm-install@3.16.5":
  version "3.16.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/npm-install/download/@lerna/npm-install-3.16.5.tgz#d6bfdc16f81285da66515ae47924d6e278d637d3"
  integrity sha1-1r/cFvgShdpmUVrkeSTW4njWN9M=
  dependencies:
    "@lerna/child-process" "3.16.5"
    "@lerna/get-npm-exec-opts" "3.13.0"
    fs-extra "^8.1.0"
    npm-package-arg "^6.1.0"
    npmlog "^4.1.2"
    signal-exit "^3.0.2"
    write-pkg "^3.1.0"

"@lerna/npm-publish@3.18.5":
  version "3.18.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/npm-publish/download/@lerna/npm-publish-3.18.5.tgz#240e4039959fd9816b49c5b07421e11b5cb000af"
  integrity sha1-JA5AOZWf2YFrScWwdCHhG1ywAK8=
  dependencies:
    "@evocateur/libnpmpublish" "^1.2.2"
    "@lerna/otplease" "3.18.5"
    "@lerna/run-lifecycle" "3.16.2"
    figgy-pudding "^3.5.1"
    fs-extra "^8.1.0"
    npm-package-arg "^6.1.0"
    npmlog "^4.1.2"
    pify "^4.0.1"
    read-package-json "^2.0.13"

"@lerna/npm-run-script@3.16.5":
  version "3.16.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/npm-run-script/download/@lerna/npm-run-script-3.16.5.tgz#9c2ec82453a26c0b46edc0bb7c15816c821f5c15"
  integrity sha1-nC7IJFOibAtG7cC7fBWBbIIfXBU=
  dependencies:
    "@lerna/child-process" "3.16.5"
    "@lerna/get-npm-exec-opts" "3.13.0"
    npmlog "^4.1.2"

"@lerna/otplease@3.18.5":
  version "3.18.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/otplease/download/@lerna/otplease-3.18.5.tgz#b77b8e760b40abad9f7658d988f3ea77d4fd0231"
  integrity sha1-t3uOdgtAq62fdljZiPPqd9T9AjE=
  dependencies:
    "@lerna/prompt" "3.18.5"
    figgy-pudding "^3.5.1"

"@lerna/output@3.13.0":
  version "3.13.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/output/download/@lerna/output-3.13.0.tgz#3ded7cc908b27a9872228a630d950aedae7a4989"
  integrity sha1-Pe18yQiyephyIopjDZUK7a56SYk=
  dependencies:
    npmlog "^4.1.2"

"@lerna/pack-directory@3.16.4":
  version "3.16.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/pack-directory/download/@lerna/pack-directory-3.16.4.tgz#3eae5f91bdf5acfe0384510ed53faddc4c074693"
  integrity sha1-Pq5fkb31rP4DhFEO1T+t3EwHRpM=
  dependencies:
    "@lerna/get-packed" "3.16.0"
    "@lerna/package" "3.16.0"
    "@lerna/run-lifecycle" "3.16.2"
    figgy-pudding "^3.5.1"
    npm-packlist "^1.4.4"
    npmlog "^4.1.2"
    tar "^4.4.10"
    temp-write "^3.4.0"

"@lerna/package-graph@3.18.5":
  version "3.18.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/package-graph/download/@lerna/package-graph-3.18.5.tgz#c740e2ea3578d059e551633e950690831b941f6b"
  integrity sha1-x0Di6jV40FnlUWM+lQaQgxuUH2s=
  dependencies:
    "@lerna/prerelease-id-from-version" "3.16.0"
    "@lerna/validation-error" "3.13.0"
    npm-package-arg "^6.1.0"
    npmlog "^4.1.2"
    semver "^6.2.0"

"@lerna/package@3.16.0":
  version "3.16.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/package/download/@lerna/package-3.16.0.tgz#7e0a46e4697ed8b8a9c14d59c7f890e0d38ba13c"
  integrity sha1-fgpG5Gl+2LipwU1Zx/iQ4NOLoTw=
  dependencies:
    load-json-file "^5.3.0"
    npm-package-arg "^6.1.0"
    write-pkg "^3.1.0"

"@lerna/prerelease-id-from-version@3.16.0":
  version "3.16.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/prerelease-id-from-version/download/@lerna/prerelease-id-from-version-3.16.0.tgz#b24bfa789f5e1baab914d7b08baae9b7bd7d83a1"
  integrity sha1-skv6eJ9eG6q5FNewi6rpt719g6E=
  dependencies:
    semver "^6.2.0"

"@lerna/profiler@3.20.0":
  version "3.20.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/profiler/download/@lerna/profiler-3.20.0.tgz#0f6dc236f4ea8f9ea5f358c6703305a4f32ad051"
  integrity sha1-D23CNvTqj56l81jGcDMFpPMq0FE=
  dependencies:
    figgy-pudding "^3.5.1"
    fs-extra "^8.1.0"
    npmlog "^4.1.2"
    upath "^1.2.0"

"@lerna/project@3.21.0":
  version "3.21.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/project/download/@lerna/project-3.21.0.tgz#5d784d2d10c561a00f20320bcdb040997c10502d"
  integrity sha1-XXhNLRDFYaAPIDILzbBAmXwQUC0=
  dependencies:
    "@lerna/package" "3.16.0"
    "@lerna/validation-error" "3.13.0"
    cosmiconfig "^5.1.0"
    dedent "^0.7.0"
    dot-prop "^4.2.0"
    glob-parent "^5.0.0"
    globby "^9.2.0"
    load-json-file "^5.3.0"
    npmlog "^4.1.2"
    p-map "^2.1.0"
    resolve-from "^4.0.0"
    write-json-file "^3.2.0"

"@lerna/prompt@3.18.5":
  version "3.18.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/prompt/download/@lerna/prompt-3.18.5.tgz#628cd545f225887d060491ab95df899cfc5218a1"
  integrity sha1-YozVRfIliH0GBJGrld+JnPxSGKE=
  dependencies:
    inquirer "^6.2.0"
    npmlog "^4.1.2"

"@lerna/publish@3.22.1":
  version "3.22.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/publish/download/@lerna/publish-3.22.1.tgz#b4f7ce3fba1e9afb28be4a1f3d88222269ba9519"
  integrity sha1-tPfOP7oemvsovkofPYgiImm6lRk=
  dependencies:
    "@evocateur/libnpmaccess" "^3.1.2"
    "@evocateur/npm-registry-fetch" "^4.0.0"
    "@evocateur/pacote" "^9.6.3"
    "@lerna/check-working-tree" "3.16.5"
    "@lerna/child-process" "3.16.5"
    "@lerna/collect-updates" "3.20.0"
    "@lerna/command" "3.21.0"
    "@lerna/describe-ref" "3.16.5"
    "@lerna/log-packed" "3.16.0"
    "@lerna/npm-conf" "3.16.0"
    "@lerna/npm-dist-tag" "3.18.5"
    "@lerna/npm-publish" "3.18.5"
    "@lerna/otplease" "3.18.5"
    "@lerna/output" "3.13.0"
    "@lerna/pack-directory" "3.16.4"
    "@lerna/prerelease-id-from-version" "3.16.0"
    "@lerna/prompt" "3.18.5"
    "@lerna/pulse-till-done" "3.13.0"
    "@lerna/run-lifecycle" "3.16.2"
    "@lerna/run-topologically" "3.18.5"
    "@lerna/validation-error" "3.13.0"
    "@lerna/version" "3.22.1"
    figgy-pudding "^3.5.1"
    fs-extra "^8.1.0"
    npm-package-arg "^6.1.0"
    npmlog "^4.1.2"
    p-finally "^1.0.0"
    p-map "^2.1.0"
    p-pipe "^1.2.0"
    semver "^6.2.0"

"@lerna/pulse-till-done@3.13.0":
  version "3.13.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/pulse-till-done/download/@lerna/pulse-till-done-3.13.0.tgz#c8e9ce5bafaf10d930a67d7ed0ccb5d958fe0110"
  integrity sha1-yOnOW6+vENkwpn1+0My12Vj+ARA=
  dependencies:
    npmlog "^4.1.2"

"@lerna/query-graph@3.18.5":
  version "3.18.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/query-graph/download/@lerna/query-graph-3.18.5.tgz#df4830bb5155273003bf35e8dda1c32d0927bd86"
  integrity sha1-30gwu1FVJzADvzXo3aHDLQknvYY=
  dependencies:
    "@lerna/package-graph" "3.18.5"
    figgy-pudding "^3.5.1"

"@lerna/resolve-symlink@3.16.0":
  version "3.16.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/resolve-symlink/download/@lerna/resolve-symlink-3.16.0.tgz#37fc7095fabdbcf317c26eb74e0d0bde8efd2386"
  integrity sha1-N/xwlfq9vPMXwm63Tg0L3o79I4Y=
  dependencies:
    fs-extra "^8.1.0"
    npmlog "^4.1.2"
    read-cmd-shim "^1.0.1"

"@lerna/rimraf-dir@3.16.5":
  version "3.16.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/rimraf-dir/download/@lerna/rimraf-dir-3.16.5.tgz#04316ab5ffd2909657aaf388ea502cb8c2f20a09"
  integrity sha1-BDFqtf/SkJZXqvOI6lAsuMLyCgk=
  dependencies:
    "@lerna/child-process" "3.16.5"
    npmlog "^4.1.2"
    path-exists "^3.0.0"
    rimraf "^2.6.2"

"@lerna/run-lifecycle@3.16.2":
  version "3.16.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/run-lifecycle/download/@lerna/run-lifecycle-3.16.2.tgz#67b288f8ea964db9ea4fb1fbc7715d5bbb0bce00"
  integrity sha1-Z7KI+OqWTbnqT7H7x3FdW7sLzgA=
  dependencies:
    "@lerna/npm-conf" "3.16.0"
    figgy-pudding "^3.5.1"
    npm-lifecycle "^3.1.2"
    npmlog "^4.1.2"

"@lerna/run-topologically@3.18.5":
  version "3.18.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/run-topologically/download/@lerna/run-topologically-3.18.5.tgz#3cd639da20e967d7672cb88db0f756b92f2fdfc3"
  integrity sha1-PNY52iDpZ9dnLLiNsPdWuS8v38M=
  dependencies:
    "@lerna/query-graph" "3.18.5"
    figgy-pudding "^3.5.1"
    p-queue "^4.0.0"

"@lerna/run@3.21.0":
  version "3.21.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/run/download/@lerna/run-3.21.0.tgz#2a35ec84979e4d6e42474fe148d32e5de1cac891"
  integrity sha1-KjXshJeeTW5CR0/hSNMuXeHKyJE=
  dependencies:
    "@lerna/command" "3.21.0"
    "@lerna/filter-options" "3.20.0"
    "@lerna/npm-run-script" "3.16.5"
    "@lerna/output" "3.13.0"
    "@lerna/profiler" "3.20.0"
    "@lerna/run-topologically" "3.18.5"
    "@lerna/timer" "3.13.0"
    "@lerna/validation-error" "3.13.0"
    p-map "^2.1.0"

"@lerna/symlink-binary@3.17.0":
  version "3.17.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/symlink-binary/download/@lerna/symlink-binary-3.17.0.tgz#8f8031b309863814883d3f009877f82e38aef45a"
  integrity sha1-j4AxswmGOBSIPT8AmHf4Ljiu9Fo=
  dependencies:
    "@lerna/create-symlink" "3.16.2"
    "@lerna/package" "3.16.0"
    fs-extra "^8.1.0"
    p-map "^2.1.0"

"@lerna/symlink-dependencies@3.17.0":
  version "3.17.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/symlink-dependencies/download/@lerna/symlink-dependencies-3.17.0.tgz#48d6360e985865a0e56cd8b51b308a526308784a"
  integrity sha1-SNY2DphYZaDlbNi1GzCKUmMIeEo=
  dependencies:
    "@lerna/create-symlink" "3.16.2"
    "@lerna/resolve-symlink" "3.16.0"
    "@lerna/symlink-binary" "3.17.0"
    fs-extra "^8.1.0"
    p-finally "^1.0.0"
    p-map "^2.1.0"
    p-map-series "^1.0.0"

"@lerna/timer@3.13.0":
  version "3.13.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/timer/download/@lerna/timer-3.13.0.tgz#bcd0904551db16e08364d6c18e5e2160fc870781"
  integrity sha1-vNCQRVHbFuCDZNbBjl4hYPyHB4E=

"@lerna/validation-error@3.13.0":
  version "3.13.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/validation-error/download/@lerna/validation-error-3.13.0.tgz#c86b8f07c5ab9539f775bd8a54976e926f3759c3"
  integrity sha1-yGuPB8WrlTn3db2KVJdukm83WcM=
  dependencies:
    npmlog "^4.1.2"

"@lerna/version@3.22.1":
  version "3.22.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/version/download/@lerna/version-3.22.1.tgz#9805a9247a47ee62d6b81bd9fa5fb728b24b59e2"
  integrity sha1-mAWpJHpH7mLWuBvZ+l+3KLJLWeI=
  dependencies:
    "@lerna/check-working-tree" "3.16.5"
    "@lerna/child-process" "3.16.5"
    "@lerna/collect-updates" "3.20.0"
    "@lerna/command" "3.21.0"
    "@lerna/conventional-commits" "3.22.0"
    "@lerna/github-client" "3.22.0"
    "@lerna/gitlab-client" "3.15.0"
    "@lerna/output" "3.13.0"
    "@lerna/prerelease-id-from-version" "3.16.0"
    "@lerna/prompt" "3.18.5"
    "@lerna/run-lifecycle" "3.16.2"
    "@lerna/run-topologically" "3.18.5"
    "@lerna/validation-error" "3.13.0"
    chalk "^2.3.1"
    dedent "^0.7.0"
    load-json-file "^5.3.0"
    minimatch "^3.0.4"
    npmlog "^4.1.2"
    p-map "^2.1.0"
    p-pipe "^1.2.0"
    p-reduce "^1.0.0"
    p-waterfall "^1.0.0"
    semver "^6.2.0"
    slash "^2.0.0"
    temp-write "^3.4.0"
    write-json-file "^3.2.0"

"@lerna/write-log-file@3.13.0":
  version "3.13.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@lerna/write-log-file/download/@lerna/write-log-file-3.13.0.tgz#b78d9e4cfc1349a8be64d91324c4c8199e822a26"
  integrity sha1-t42eTPwTSai+ZNkTJMTIGZ6CKiY=
  dependencies:
    npmlog "^4.1.2"
    write-file-atomic "^2.3.0"

"@mrmlnc/readdir-enhanced@^2.2.1":
  version "2.2.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/@mrmlnc/readdir-enhanced/download/@mrmlnc/readdir-enhanced-2.2.1.tgz#524af240d1a360527b730475ecfa1344aa540dde"
  integrity sha1-UkryQNGjYFJ7cwR17PoTRKpUDd4=
  dependencies:
    call-me-maybe "^1.0.1"
    glob-to-regexp "^0.3.0"

"@nodelib/fs.stat@^1.1.2":
  version "1.1.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/@nodelib/fs.stat/download/@nodelib/fs.stat-1.1.3.tgz#2b5a3ab3f918cca48a8c754c08168e3f03eba61b"
  integrity sha1-K1o6s/kYzKSKjHVMCBaOPwPrphs=

"@octokit/auth-token@^2.4.0":
  version "2.5.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@octokit/auth-token/download/@octokit/auth-token-2.5.0.tgz#27c37ea26c205f28443402477ffd261311f21e36"
  integrity sha1-J8N+omwgXyhENAJHf/0mExHyHjY=
  dependencies:
    "@octokit/types" "^6.0.3"

"@octokit/endpoint@^6.0.1":
  version "6.0.12"
  resolved "http://npm.devops.xiaohongshu.com:7001/@octokit/endpoint/download/@octokit/endpoint-6.0.12.tgz#3b4d47a4b0e79b1027fb8d75d4221928b2d05658"
  integrity sha1-O01HpLDnmxAn+4111CIZKLLQVlg=
  dependencies:
    "@octokit/types" "^6.0.3"
    is-plain-object "^5.0.0"
    universal-user-agent "^6.0.0"

"@octokit/openapi-types@^12.11.0":
  version "12.11.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@octokit/openapi-types/download/@octokit/openapi-types-12.11.0.tgz#da5638d64f2b919bca89ce6602d059f1b52d3ef0"
  integrity sha1-2lY41k8rkZvKic5mAtBZ8bUtPvA=

"@octokit/plugin-enterprise-rest@^6.0.1":
  version "6.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/@octokit/plugin-enterprise-rest/download/@octokit/plugin-enterprise-rest-6.0.1.tgz#e07896739618dab8da7d4077c658003775f95437"
  integrity sha1-4HiWc5YY2rjafUB3xlgAN3X5VDc=

"@octokit/plugin-paginate-rest@^1.1.1":
  version "1.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/@octokit/plugin-paginate-rest/download/@octokit/plugin-paginate-rest-1.1.2.tgz#004170acf8c2be535aba26727867d692f7b488fc"
  integrity sha1-AEFwrPjCvlNauiZyeGfWkve0iPw=
  dependencies:
    "@octokit/types" "^2.0.1"

"@octokit/plugin-request-log@^1.0.0":
  version "1.0.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/@octokit/plugin-request-log/download/@octokit/plugin-request-log-1.0.4.tgz#5e50ed7083a613816b1e4a28aeec5fb7f1462e85"
  integrity sha1-XlDtcIOmE4FrHkooruxft/FGLoU=

"@octokit/plugin-rest-endpoint-methods@2.4.0":
  version "2.4.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@octokit/plugin-rest-endpoint-methods/download/@octokit/plugin-rest-endpoint-methods-2.4.0.tgz#3288ecf5481f68c494dd0602fc15407a59faf61e"
  integrity sha1-Mojs9UgfaMSU3QYC/BVAeln69h4=
  dependencies:
    "@octokit/types" "^2.0.1"
    deprecation "^2.3.1"

"@octokit/request-error@^1.0.2":
  version "1.2.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/@octokit/request-error/download/@octokit/request-error-1.2.1.tgz#ede0714c773f32347576c25649dc013ae6b31801"
  integrity sha1-7eBxTHc/MjR1dsJWSdwBOuazGAE=
  dependencies:
    "@octokit/types" "^2.0.0"
    deprecation "^2.0.0"
    once "^1.4.0"

"@octokit/request-error@^2.1.0":
  version "2.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@octokit/request-error/download/@octokit/request-error-2.1.0.tgz#9e150357831bfc788d13a4fd4b1913d60c74d677"
  integrity sha1-nhUDV4Mb/HiNE6T9SxkT1gx01nc=
  dependencies:
    "@octokit/types" "^6.0.3"
    deprecation "^2.0.0"
    once "^1.4.0"

"@octokit/request@^5.2.0":
  version "5.6.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/@octokit/request/download/@octokit/request-5.6.3.tgz#19a022515a5bba965ac06c9d1334514eb50c48b0"
  integrity sha1-GaAiUVpbupZawGydEzRRTrUMSLA=
  dependencies:
    "@octokit/endpoint" "^6.0.1"
    "@octokit/request-error" "^2.1.0"
    "@octokit/types" "^6.16.1"
    is-plain-object "^5.0.0"
    node-fetch "^2.6.7"
    universal-user-agent "^6.0.0"

"@octokit/rest@^16.28.4":
  version "16.43.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/@octokit/rest/download/@octokit/rest-16.43.2.tgz#c53426f1e1d1044dee967023e3279c50993dd91b"
  integrity sha1-xTQm8eHRBE3ulnAj4yecUJk92Rs=
  dependencies:
    "@octokit/auth-token" "^2.4.0"
    "@octokit/plugin-paginate-rest" "^1.1.1"
    "@octokit/plugin-request-log" "^1.0.0"
    "@octokit/plugin-rest-endpoint-methods" "2.4.0"
    "@octokit/request" "^5.2.0"
    "@octokit/request-error" "^1.0.2"
    atob-lite "^2.0.0"
    before-after-hook "^2.0.0"
    btoa-lite "^1.0.0"
    deprecation "^2.0.0"
    lodash.get "^4.4.2"
    lodash.set "^4.3.2"
    lodash.uniq "^4.5.0"
    octokit-pagination-methods "^1.1.0"
    once "^1.4.0"
    universal-user-agent "^4.0.0"

"@octokit/types@^2.0.0", "@octokit/types@^2.0.1":
  version "2.16.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/@octokit/types/download/@octokit/types-2.16.2.tgz#4c5f8da3c6fecf3da1811aef678fda03edac35d2"
  integrity sha1-TF+No8b+zz2hgRrvZ4/aA+2sNdI=
  dependencies:
    "@types/node" ">= 8"

"@octokit/types@^6.0.3", "@octokit/types@^6.16.1":
  version "6.41.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@octokit/types/download/@octokit/types-6.41.0.tgz#e58ef78d78596d2fb7df9c6259802464b5f84a04"
  integrity sha1-5Y73jXhZbS+335xiWYAkZLX4SgQ=
  dependencies:
    "@octokit/openapi-types" "^12.11.0"

"@popperjs/core@^2.9.2":
  version "2.11.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/@popperjs/core/download/@popperjs/core-2.11.7.tgz#ccab5c8f7dc557a52ca3288c10075c9ccd37fff7"
  integrity sha1-zKtcj33FV6UsoyiMEAdcnM03//c=

"@probe.gl/env@3.6.0":
  version "3.6.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@probe.gl/env/-/env-3.6.0.tgz#33343fd9041a14d21374c1911826d4a2f9d9a35d"
  integrity sha512-4tTZYUg/8BICC3Yyb9rOeoKeijKbZHRXBEKObrfPmX4sQmYB15ZOUpoVBhAyJkOYVAM8EkPci6Uw5dLCwx2BEQ==
  dependencies:
    "@babel/runtime" "^7.0.0"

"@probe.gl/log@3.6.0":
  version "3.6.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@probe.gl/log/-/log-3.6.0.tgz#c645bfd22b4769dc65161caa17f13bd2b231e413"
  integrity sha512-hjpyenpEvOdowgZ1qMeCJxfRD4JkKdlXz0RC14m42Un62NtOT+GpWyKA4LssT0+xyLULCByRAtG2fzZorpIAcA==
  dependencies:
    "@babel/runtime" "^7.0.0"
    "@probe.gl/env" "3.6.0"

"@probe.gl/stats@3.6.0":
  version "3.6.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@probe.gl/stats/-/stats-3.6.0.tgz#a1bb12860fa6f40b9c028f9eb575d7ada0b4dbdd"
  integrity sha512-JdALQXB44OP4kUBN/UrQgzbJe4qokbVF4Y8lkIA8iVCFnjVowWIgkD/z/0QO65yELT54tTrtepw1jScjKB+rhQ==
  dependencies:
    "@babel/runtime" "^7.0.0"

"@sentry/browser@^6.17.2":
  version "6.19.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/@sentry/browser/download/@sentry/browser-6.19.7.tgz#a40b6b72d911b5f1ed70ed3b4e7d4d4e625c0b5f"
  integrity sha1-pAtrctkRtfHtcO07Tn1NTmJcC18=
  dependencies:
    "@sentry/core" "6.19.7"
    "@sentry/types" "6.19.7"
    "@sentry/utils" "6.19.7"
    tslib "^1.9.3"

"@sentry/core@6.19.7":
  version "6.19.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/@sentry/core/download/@sentry/core-6.19.7.tgz#156aaa56dd7fad8c89c145be6ad7a4f7209f9785"
  integrity sha1-FWqqVt1/rYyJwUW+atek9yCfl4U=
  dependencies:
    "@sentry/hub" "6.19.7"
    "@sentry/minimal" "6.19.7"
    "@sentry/types" "6.19.7"
    "@sentry/utils" "6.19.7"
    tslib "^1.9.3"

"@sentry/hub@6.19.7":
  version "6.19.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/@sentry/hub/download/@sentry/hub-6.19.7.tgz#58ad7776bbd31e9596a8ec46365b45cd8b9cfd11"
  integrity sha1-WK13drvTHpWWqOxGNltFzYuc/RE=
  dependencies:
    "@sentry/types" "6.19.7"
    "@sentry/utils" "6.19.7"
    tslib "^1.9.3"

"@sentry/minimal@6.19.7":
  version "6.19.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/@sentry/minimal/download/@sentry/minimal-6.19.7.tgz#b3ee46d6abef9ef3dd4837ebcb6bdfd01b9aa7b4"
  integrity sha1-s+5G1qvvnvPdSDfry2vf0Buap7Q=
  dependencies:
    "@sentry/hub" "6.19.7"
    "@sentry/types" "6.19.7"
    tslib "^1.9.3"

"@sentry/types@6.19.7":
  version "6.19.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/@sentry/types/download/@sentry/types-6.19.7.tgz#c6b337912e588083fc2896eb012526cf7cfec7c7"
  integrity sha1-xrM3kS5YgIP8KJbrASUmz3z+x8c=

"@sentry/utils@6.19.7":
  version "6.19.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/@sentry/utils/download/@sentry/utils-6.19.7.tgz#6edd739f8185fd71afe49cbe351c1bbf5e7b7c79"
  integrity sha1-bt1zn4GF/XGv5Jy+NRwbv157fHk=
  dependencies:
    "@sentry/types" "6.19.7"
    tslib "^1.9.3"

"@tokenizer/token@^0.1.1":
  version "0.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/@tokenizer/token/download/@tokenizer/token-0.1.1.tgz#f0d92c12f87079ddfd1b29f614758b9696bc29e3"
  integrity sha1-8NksEvhwed39Gyn2FHWLlpa8KeM=

"@tokenizer/token@^0.3.0":
  version "0.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@tokenizer/token/download/@tokenizer/token-0.3.0.tgz#fe98a93fe789247e998c75e74e9c7c63217aa276"
  integrity sha1-/pipP+eJJH6ZjHXnTpx8YyF6onY=

"@transloadit/prettier-bytes@0.0.7":
  version "0.0.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/@transloadit/prettier-bytes/download/@transloadit/prettier-bytes-0.0.7.tgz#cdb5399f445fdd606ed833872fa0cabdbc51686b"
  integrity sha1-zbU5n0Rf3WBu2DOHL6DKvbxRaGs=

"@types/accepts@*":
  version "1.3.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/accepts/download/@types/accepts-1.3.5.tgz#c34bec115cfc746e04fe5a059df4ce7e7b391575"
  integrity sha1-w0vsEVz8dG4E/loFnfTOfns5FXU=
  dependencies:
    "@types/node" "*"

"@types/body-parser@*":
  version "1.19.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/body-parser/download/@types/body-parser-1.19.2.tgz#aea2059e28b7658639081347ac4fab3de166e6f0"
  integrity sha1-rqIFnii3ZYY5CBNHrE+rPeFm5vA=
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/body-scroll-lock@^2.6.1":
  version "2.6.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/body-scroll-lock/download/@types/body-scroll-lock-2.6.2.tgz#ce56d17e1bf8383c08a074733c4e9e536a59ae61"
  integrity sha1-zlbRfhv4ODwIoHRzPE6eU2pZrmE=

"@types/connect@*":
  version "3.4.35"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/connect/download/@types/connect-3.4.35.tgz#5fcf6ae445e4021d1fc2219a4873cc73a3bb2ad1"
  integrity sha1-X89q5EXkAh0fwiGaSHPMc6O7KtE=
  dependencies:
    "@types/node" "*"

"@types/content-disposition@*":
  version "0.5.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/content-disposition/download/@types/content-disposition-0.5.5.tgz#650820e95de346e1f84e30667d168c8fd25aa6e3"
  integrity sha1-ZQgg6V3jRuH4TjBmfRaMj9JapuM=

"@types/cookies@*":
  version "0.7.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/cookies/download/@types/cookies-0.7.7.tgz#7a92453d1d16389c05a5301eef566f34946cfd81"
  integrity sha1-epJFPR0WOJwFpTAe71ZvNJRs/YE=
  dependencies:
    "@types/connect" "*"
    "@types/express" "*"
    "@types/keygrip" "*"
    "@types/node" "*"

"@types/d3-timer@^2.0.0":
  version "2.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/d3-timer/-/d3-timer-2.0.1.tgz#ffb6620d290624f3726aa362c0c8a4b44c8d7200"
  integrity sha512-TF8aoF5cHcLO7W7403blM7L1T+6NF3XMyN3fxyUolq2uOcFeicG/khQg/dGxiCJWoAcmYulYN7LYSRKO54IXaA==

"@types/event-emitter@^0.3.3":
  version "0.3.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/event-emitter/download/@types/event-emitter-0.3.3.tgz#727032a9fc67565f96bbd78b2e2809275c97d7e7"
  integrity sha1-cnAyqfxnVl+Wu9eLLigJJ1yX1+c=

"@types/express-serve-static-core@^4.17.18":
  version "4.17.31"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/express-serve-static-core/download/@types/express-serve-static-core-4.17.31.tgz#a1139efeab4e7323834bb0226e62ac019f474b2f"
  integrity sha1-oROe/qtOcyODS7AibmKsAZ9HSy8=
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"

"@types/express@*":
  version "4.17.14"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/express/download/@types/express-4.17.14.tgz#143ea0557249bc1b3b54f15db4c81c3d4eb3569c"
  integrity sha1-FD6gVXJJvBs7VPFdtMgcPU6zVpw=
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.18"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/glob@^7.1.1":
  version "7.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/glob/download/@types/glob-7.2.0.tgz#bc1b5bf3aa92f25bd5dd39f35c57361bdce5b2eb"
  integrity sha1-vBtb86qS8lvV3TnzXFc2G9zlsus=
  dependencies:
    "@types/minimatch" "*"
    "@types/node" "*"

"@types/http-assert@*":
  version "1.5.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/http-assert/download/@types/http-assert-1.5.3.tgz#ef8e3d1a8d46c387f04ab0f2e8ab8cb0c5078661"
  integrity sha1-7449Go1Gw4fwSrDy6KuMsMUHhmE=

"@types/http-errors@*":
  version "1.8.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/http-errors/download/@types/http-errors-1.8.2.tgz#7315b4c4c54f82d13fa61c228ec5c2ea5cc9e0e1"
  integrity sha1-cxW0xMVPgtE/phwijsXC6lzJ4OE=

"@types/keygrip@*":
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/keygrip/download/@types/keygrip-1.0.2.tgz#513abfd256d7ad0bf1ee1873606317b33b1b2a72"
  integrity sha1-UTq/0lbXrQvx7hhzYGMXszsbKnI=

"@types/koa-bodyparser@~4.3.7":
  version "4.3.10"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/koa-bodyparser/download/@types/koa-bodyparser-4.3.10.tgz#02b8d3d57579aa7d491d553f1f4058088bfe127f"
  integrity sha1-ArjT1XV5qn1JHVU/H0BYCIv+En8=
  dependencies:
    "@types/koa" "*"

"@types/koa-compose@*", "@types/koa-compose@~3.2.5":
  version "3.2.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/koa-compose/download/@types/koa-compose-3.2.5.tgz#85eb2e80ac50be95f37ccf8c407c09bbe3468e9d"
  integrity sha1-hesugKxQvpXzfM+MQHwJu+NGjp0=
  dependencies:
    "@types/koa" "*"

"@types/koa-send@*":
  version "4.1.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/koa-send/download/@types/koa-send-4.1.3.tgz#17193c6472ae9e5d1b99ae8086949cc4fd69179d"
  integrity sha1-Fxk8ZHKunl0bma6AhpScxP1pF50=
  dependencies:
    "@types/koa" "*"

"@types/koa-static@~4.0.2":
  version "4.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/koa-static/download/@types/koa-static-4.0.2.tgz#a199d2d64d2930755eb3ea370aeaf2cb6f501d67"
  integrity sha1-oZnS1k0pMHVes+o3Curyy29QHWc=
  dependencies:
    "@types/koa" "*"
    "@types/koa-send" "*"

"@types/koa@*":
  version "2.13.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/koa/download/@types/koa-2.13.5.tgz#64b3ca4d54e08c0062e89ec666c9f45443b21a61"
  integrity sha1-ZLPKTVTgjABi6J7GZsn0VEOyGmE=
  dependencies:
    "@types/accepts" "*"
    "@types/content-disposition" "*"
    "@types/cookies" "*"
    "@types/http-assert" "*"
    "@types/http-errors" "*"
    "@types/keygrip" "*"
    "@types/koa-compose" "*"
    "@types/node" "*"

"@types/koa@~2.13.6":
  version "2.13.6"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/koa/download/@types/koa-2.13.6.tgz#6dc14e727baf397310aa6f414ebe5d144983af42"
  integrity sha1-bcFOcnuvOXMQqm9BTr5dFEmDr0I=
  dependencies:
    "@types/accepts" "*"
    "@types/content-disposition" "*"
    "@types/cookies" "*"
    "@types/http-assert" "*"
    "@types/http-errors" "*"
    "@types/keygrip" "*"
    "@types/koa-compose" "*"
    "@types/node" "*"

"@types/lodash-es@^4.17.6":
  version "4.17.6"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/lodash-es/download/@types/lodash-es-4.17.6.tgz#c2ed4c8320ffa6f11b43eb89e9eaeec65966a0a0"
  integrity sha1-wu1MgyD/pvEbQ+uJ6eruxllmoKA=
  dependencies:
    "@types/lodash" "*"

"@types/lodash@*":
  version "4.14.185"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/lodash/download/@types/lodash-4.14.185.tgz#c9843f5a40703a8f5edfd53358a58ae729816908"
  integrity sha1-yYQ/WkBwOo9e39UzWKWK5ymBaQg=

"@types/lodash@^4.14.165":
  version "4.14.194"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/lodash/download/@types/lodash-4.14.194.tgz#b71eb6f7a0ff11bff59fc987134a093029258a76"
  integrity sha1-tx6296D/Eb/1n8mHE0oJMCklinY=

"@types/mime@*":
  version "3.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/mime/download/@types/mime-3.0.1.tgz#5f8f2bca0a5863cb69bc0b0acd88c96cb1d4ae10"
  integrity sha1-X48rygpYY8tpvAsKzYjJbLHUrhA=

"@types/minimatch@*":
  version "5.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/minimatch/download/@types/minimatch-5.1.2.tgz#07508b45797cb81ec3f273011b054cd0755eddca"
  integrity sha1-B1CLRXl8uB7D8nMBGwVM0HVe3co=

"@types/minimist@^1.2.0":
  version "1.2.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/minimist/download/@types/minimist-1.2.2.tgz#ee771e2ba4b3dc5b372935d549fd9617bf345b8c"
  integrity sha1-7nceK6Sz3Fs3KTXVSf2WF780W4w=

"@types/node-fetch@2.5.12":
  version "2.5.12"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/node-fetch/download/@types/node-fetch-2.5.12.tgz#8a6f779b1d4e60b7a57fb6fd48d84fb545b9cc66"
  integrity sha1-im93mx1OYLelf7b9SNhPtUW5zGY=
  dependencies:
    "@types/node" "*"
    form-data "^3.0.0"

"@types/node-int64@*", "@types/node-int64@~0.4.29":
  version "0.4.29"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/node-int64/download/@types/node-int64-0.4.29.tgz#8c7c16a7c1195ae4f8beaa903b0018ac66291d16"
  integrity sha1-jHwWp8EZWuT4vqqQOwAYrGYpHRY=
  dependencies:
    "@types/node" "*"

"@types/node@*", "@types/node@>= 8":
  version "18.8.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/node/download/@types/node-18.8.3.tgz#ce750ab4017effa51aed6a7230651778d54e327c"
  integrity sha1-znUKtAF+/6Ua7WpyMGUXeNVOMnw=

"@types/node@^14.17.1":
  version "14.18.42"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/node/download/@types/node-14.18.42.tgz#fa39b2dc8e0eba61bdf51c66502f84e23b66e114"
  integrity sha1-+jmy3I4OumG99RxmUC+E4jtm4RQ=

"@types/normalize-package-data@^2.4.0":
  version "2.4.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/normalize-package-data/download/@types/normalize-package-data-2.4.1.tgz#d3357479a0fdfdd5907fe67e17e0a85c906e1301"
  integrity sha1-0zV0eaD9/dWQf+Z+F+CoXJBuEwE=

"@types/q@*":
  version "1.5.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/q/download/@types/q-1.5.5.tgz#75a2a8e7d8ab4b230414505d92335d1dcb53a6df"
  integrity sha1-daKo59irSyMEFFBdkjNdHctTpt8=

"@types/qs@*":
  version "6.9.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/qs/download/@types/qs-6.9.7.tgz#63bb7d067db107cc1e457c303bc25d511febf6cb"
  integrity sha1-Y7t9Bn2xB8weRXwwO8JdUR/r9ss=

"@types/range-parser@*":
  version "1.2.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/range-parser/download/@types/range-parser-1.2.4.tgz#cd667bcfdd025213aafb7ca5915a932590acdcdc"
  integrity sha1-zWZ7z90CUhOq+3ylkVqTJZCs3Nw=

"@types/serve-static@*":
  version "1.15.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/serve-static/download/@types/serve-static-1.15.0.tgz#c7930ff61afb334e121a9da780aac0d9b8f34155"
  integrity sha1-x5MP9hr7M04SGp2ngKrA2bjzQVU=
  dependencies:
    "@types/mime" "*"
    "@types/node" "*"

"@types/thrift@~0.10.12":
  version "0.10.12"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/thrift/download/@types/thrift-0.10.12.tgz#523426dcc1eef8ff229eac9fa5175f90b879e700"
  integrity sha1-UjQm3MHu+P8inqyfpRdfkLh55wA=
  dependencies:
    "@types/node" "*"
    "@types/node-int64" "*"
    "@types/q" "*"

"@types/trusted-types@^2.0.7":
  version "2.0.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/trusted-types/-/trusted-types-2.0.7.tgz#baccb07a970b91707df3a3e8ba6896c57ead2d11"
  integrity sha512-ScaPdn1dQczgbl0QFTeTOmVHFULt394XJgOQNoyVhZ6r2vLnMLJfBPd53SB52T/3G36VI1/g2MZaX0cwDuXsfw==

"@types/uuid@^8.3.0":
  version "8.3.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/uuid/download/@types/uuid-8.3.4.tgz#bd86a43617df0594787d38b735f55c805becf1bc"
  integrity sha1-vYakNhffBZR4fTi3NfVcgFvs8bw=

"@types/web-bluetooth@^0.0.15":
  version "0.0.15"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/web-bluetooth/download/@types/web-bluetooth-0.0.15.tgz#d60330046a6ed8a13b4a53df3813c44942ebdf72"
  integrity sha1-1gMwBGpu2KE7SlPfOBPESULr33I=

"@types/web-bluetooth@^0.0.16":
  version "0.0.16"
  resolved "http://npm.devops.xiaohongshu.com:7001/@types/web-bluetooth/download/@types/web-bluetooth-0.0.16.tgz#1d12873a8e49567371f2a75fe3e7f7edca6662d8"
  integrity sha1-HRKHOo5JVnNx8qdf4+f37cpmYtg=

"@typescript/vfs@^1.4.0":
  version "1.4.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@typescript/vfs/download/@typescript/vfs-1.4.0.tgz#2d22985c7666c9d4ce26eb025405e6f156aa32b0"
  integrity sha1-LSKYXHZmydTOJusCVAXm8VaqMrA=
  dependencies:
    debug "^4.1.1"

"@uppy/companion-client@^2.2.2":
  version "2.2.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/@uppy/companion-client/download/@uppy/companion-client-2.2.2.tgz#c70b42fdcca728ef88b3eebf7ee3e2fa04b4923b"
  integrity sha1-xwtC/cynKO+Is+6/fuPi+gS0kjs=
  dependencies:
    "@uppy/utils" "^4.1.2"
    namespace-emitter "^2.0.1"

"@uppy/core@^2.1.1":
  version "2.3.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/@uppy/core/download/@uppy/core-2.3.4.tgz#260b85b6bf3aa03cdc67da231f8c69cfbfdcc84a"
  integrity sha1-JguFtr86oDzcZ9ojH4xpz7/cyEo=
  dependencies:
    "@transloadit/prettier-bytes" "0.0.7"
    "@uppy/store-default" "^2.1.1"
    "@uppy/utils" "^4.1.3"
    lodash.throttle "^4.1.1"
    mime-match "^1.0.2"
    namespace-emitter "^2.0.1"
    nanoid "^3.1.25"
    preact "^10.5.13"

"@uppy/store-default@^2.1.1":
  version "2.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/@uppy/store-default/download/@uppy/store-default-2.1.1.tgz#62a656a099bdaa012306e054d093754cb2d36e3e"
  integrity sha1-YqZWoJm9qgEjBuBU0JN1TLLTbj4=

"@uppy/utils@^4.1.2", "@uppy/utils@^4.1.3":
  version "4.1.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/@uppy/utils/download/@uppy/utils-4.1.3.tgz#9d0be6ece4df25f228d30ef40be0f14208258ce3"
  integrity sha1-nQvm7OTfJfIo0w70C+DxQggljOM=
  dependencies:
    lodash.throttle "^4.1.1"

"@uppy/xhr-upload@^2.0.3":
  version "2.1.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/@uppy/xhr-upload/download/@uppy/xhr-upload-2.1.3.tgz#0d4e355332fe0c6eb372d7731315e04d02aeeb18"
  integrity sha1-DU41UzL+DG6zctdzExXgTQKu6xg=
  dependencies:
    "@uppy/companion-client" "^2.2.2"
    "@uppy/utils" "^4.1.2"
    nanoid "^3.1.25"

"@vue/compat@^3.2.31":
  version "3.2.37"
  resolved "http://npm.devops.xiaohongshu.com:7001/@vue/compat/download/@vue/compat-3.2.37.tgz#bd2062a2be88d06046311d5c058aefb17a1d438a"
  integrity sha1-vSBior6I0GBGMR1cBYrvsXodQ4o=

"@vue/compiler-core@3.2.40":
  version "3.2.40"
  resolved "http://npm.devops.xiaohongshu.com:7001/@vue/compiler-core/download/@vue/compiler-core-3.2.40.tgz#c785501f09536748121e937fb87605bbb1ada8e5"
  integrity sha1-x4VQHwlTZ0gSHpN/uHYFu7GtqOU=
  dependencies:
    "@babel/parser" "^7.16.4"
    "@vue/shared" "3.2.40"
    estree-walker "^2.0.2"
    source-map "^0.6.1"

"@vue/compiler-dom@3.2.40":
  version "3.2.40"
  resolved "http://npm.devops.xiaohongshu.com:7001/@vue/compiler-dom/download/@vue/compiler-dom-3.2.40.tgz#c225418773774db536174d30d3f25ba42a33e7e4"
  integrity sha1-wiVBh3N3TbU2F00w0/JbpCoz5+Q=
  dependencies:
    "@vue/compiler-core" "3.2.40"
    "@vue/shared" "3.2.40"

"@vue/compiler-sfc@3.2.40", "@vue/compiler-sfc@^3.2.31":
  version "3.2.40"
  resolved "http://npm.devops.xiaohongshu.com:7001/@vue/compiler-sfc/download/@vue/compiler-sfc-3.2.40.tgz#61823283efc84d25d9d2989458f305d32a2ed141"
  integrity sha1-YYIyg+/ITSXZ0piUWPMF0you0UE=
  dependencies:
    "@babel/parser" "^7.16.4"
    "@vue/compiler-core" "3.2.40"
    "@vue/compiler-dom" "3.2.40"
    "@vue/compiler-ssr" "3.2.40"
    "@vue/reactivity-transform" "3.2.40"
    "@vue/shared" "3.2.40"
    estree-walker "^2.0.2"
    magic-string "^0.25.7"
    postcss "^8.1.10"
    source-map "^0.6.1"

"@vue/compiler-ssr@3.2.40":
  version "3.2.40"
  resolved "http://npm.devops.xiaohongshu.com:7001/@vue/compiler-ssr/download/@vue/compiler-ssr-3.2.40.tgz#67df95a096c63e9ec4b50b84cc6f05816793629c"
  integrity sha1-Z9+VoJbGPp7EtQuEzG8FgWeTYpw=
  dependencies:
    "@vue/compiler-dom" "3.2.40"
    "@vue/shared" "3.2.40"

"@vue/composition-api@^0.4.0":
  version "0.4.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@vue/composition-api/download/@vue/composition-api-0.4.0.tgz#04e600106687de719d587c704296aa4a5b37ca28"
  integrity sha1-BOYAEGaH3nGdWHxwQpaqSls3yig=
  dependencies:
    tslib "^1.9.3"

"@vue/devtools-api@^6.0.0-beta.11", "@vue/devtools-api@^6.1.4":
  version "6.4.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/@vue/devtools-api/download/@vue/devtools-api-6.4.3.tgz#784aa9e397adde727ca892c3574f5f1cfb2bc1c2"
  integrity sha1-eEqp45et3nJ8qJLDV09fHPsrwcI=

"@vue/reactivity-transform@3.2.40":
  version "3.2.40"
  resolved "http://npm.devops.xiaohongshu.com:7001/@vue/reactivity-transform/download/@vue/reactivity-transform-3.2.40.tgz#dc24b9074b26f0d9dd2034c6349f5bb2a51c86ac"
  integrity sha1-3CS5B0sm8NndIDTGNJ9bsqUchqw=
  dependencies:
    "@babel/parser" "^7.16.4"
    "@vue/compiler-core" "3.2.40"
    "@vue/shared" "3.2.40"
    estree-walker "^2.0.2"
    magic-string "^0.25.7"

"@vue/reactivity@3.2.40":
  version "3.2.40"
  resolved "http://npm.devops.xiaohongshu.com:7001/@vue/reactivity/download/@vue/reactivity-3.2.40.tgz#ae65496f5b364e4e481c426f391568ed7d133cca"
  integrity sha1-rmVJb1s2Tk5IHEJvORVo7X0TPMo=
  dependencies:
    "@vue/shared" "3.2.40"

"@vue/runtime-core@3.2.40":
  version "3.2.40"
  resolved "http://npm.devops.xiaohongshu.com:7001/@vue/runtime-core/download/@vue/runtime-core-3.2.40.tgz#e814358bf1b0ff6d4a6b4f8f62d9f341964fb275"
  integrity sha1-6BQ1i/Gw/21Ka0+PYtnzQZZPsnU=
  dependencies:
    "@vue/reactivity" "3.2.40"
    "@vue/shared" "3.2.40"

"@vue/runtime-dom@3.2.40":
  version "3.2.40"
  resolved "http://npm.devops.xiaohongshu.com:7001/@vue/runtime-dom/download/@vue/runtime-dom-3.2.40.tgz#975119feac5ab703aa9bbbf37c9cc966602c8eab"
  integrity sha1-l1EZ/qxatwOqm7vzfJzJZmAsjqs=
  dependencies:
    "@vue/runtime-core" "3.2.40"
    "@vue/shared" "3.2.40"
    csstype "^2.6.8"

"@vue/server-renderer@3.2.40":
  version "3.2.40"
  resolved "http://npm.devops.xiaohongshu.com:7001/@vue/server-renderer/download/@vue/server-renderer-3.2.40.tgz#55eaac31f7105c3907e1895129bf4efb6b0ce393"
  integrity sha1-VeqsMfcQXDkH4YlRKb9O+2sM45M=
  dependencies:
    "@vue/compiler-ssr" "3.2.40"
    "@vue/shared" "3.2.40"

"@vue/shared@3.2.40":
  version "3.2.40"
  resolved "http://npm.devops.xiaohongshu.com:7001/@vue/shared/download/@vue/shared-3.2.40.tgz#e57799da2a930b975321981fcee3d1e90ed257ae"
  integrity sha1-5XeZ2iqTC5dTIZgfzuPR6Q7SV64=

"@vueuse/core@^10.0.0":
  version "10.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@vueuse/core/download/@vueuse/core-10.0.0.tgz#b2ad4d110994c878e01857e77ef8b038a2b46aea"
  integrity sha1-sq1NEQmUyHjgGFfnfviwOKK0auo=
  dependencies:
    "@types/web-bluetooth" "^0.0.16"
    "@vueuse/metadata" "10.0.0"
    "@vueuse/shared" "10.0.0"
    vue-demi ">=0.14.0"

"@vueuse/core@^9.0.0":
  version "9.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@vueuse/core/download/@vueuse/core-9.3.0.tgz#74d855bd19cb5eadd2edb30c871918fac881e8b8"
  integrity sha1-dNhVvRnLXq3S7bMMhxkY+siB6Lg=
  dependencies:
    "@types/web-bluetooth" "^0.0.15"
    "@vueuse/metadata" "9.3.0"
    "@vueuse/shared" "9.3.0"
    vue-demi "*"

"@vueuse/core@^9.3.1":
  version "9.13.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@vueuse/core/download/@vueuse/core-9.13.0.tgz#2f69e66d1905c1e4eebc249a01759cf88ea00cf4"
  integrity sha1-L2nmbRkFweTuvCSaAXWc+I6gDPQ=
  dependencies:
    "@types/web-bluetooth" "^0.0.16"
    "@vueuse/metadata" "9.13.0"
    "@vueuse/shared" "9.13.0"
    vue-demi "*"

"@vueuse/head@^0.7.5":
  version "0.7.6"
  resolved "http://npm.devops.xiaohongshu.com:7001/@vueuse/head/download/@vueuse/head-0.7.6.tgz#39eb2aa593db6f02d10ba469b33d8cfbce038183"
  integrity sha1-OesqpZPbbwLRC6Rpsz2M+84DgYM=

"@vueuse/metadata@10.0.0":
  version "10.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@vueuse/metadata/download/@vueuse/metadata-10.0.0.tgz#272b92bb1eb171882cb89bcb5d05daf2db702374"
  integrity sha1-JyuSux6xcYgsuJvLXQXa8ttwI3Q=

"@vueuse/metadata@9.13.0":
  version "9.13.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@vueuse/metadata/download/@vueuse/metadata-9.13.0.tgz#bc25a6cdad1b1a93c36ce30191124da6520539ff"
  integrity sha1-vCWmza0bGpPDbOMBkRJNplIFOf8=

"@vueuse/metadata@9.3.0":
  version "9.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@vueuse/metadata/download/@vueuse/metadata-9.3.0.tgz#c107fe77a577e1f221536cd1b291039c0c7c4bce"
  integrity sha1-wQf+d6V34fIhU2zRspEDnAx8S84=

"@vueuse/router@^9.13.0":
  version "9.13.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@vueuse/router/download/@vueuse/router-9.13.0.tgz#cfc757fa89c654ab749c60bc2445f945cbb86b32"
  integrity sha1-z8dX+onGVKt0nGC8JEX5Rcu4azI=
  dependencies:
    "@vueuse/shared" "9.13.0"
    vue-demi "*"

"@vueuse/shared@10.0.0":
  version "10.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@vueuse/shared/download/@vueuse/shared-10.0.0.tgz#fe3ba8930a1a9de7cb4fd8dbc97de80da5782069"
  integrity sha1-/juokwoanefLT9jbyX3oDaV4IGk=
  dependencies:
    vue-demi ">=0.14.0"

"@vueuse/shared@9.13.0":
  version "9.13.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@vueuse/shared/download/@vueuse/shared-9.13.0.tgz#089ff4cc4e2e7a4015e57a8f32e4b39d096353b9"
  integrity sha1-CJ/0zE4uekAV5XqPMuSznQljU7k=
  dependencies:
    vue-demi "*"

"@vueuse/shared@9.3.0":
  version "9.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@vueuse/shared/download/@vueuse/shared-9.3.0.tgz#40fc138ba4e379c894075830aa2e15404aaa8a5b"
  integrity sha1-QPwTi6TjeciUB1gwqi4VQEqqils=
  dependencies:
    vue-demi "*"

"@wangeditor/basic-modules@^1.1.7":
  version "1.1.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/@wangeditor/basic-modules/download/@wangeditor/basic-modules-1.1.7.tgz#a9c3ccf4ef53332f29550d59d3676e15f395946f"
  integrity sha1-qcPM9O9TMy8pVQ1Z02duFfOVlG8=
  dependencies:
    is-url "^1.2.4"

"@wangeditor/code-highlight@^1.0.3":
  version "1.0.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/@wangeditor/code-highlight/download/@wangeditor/code-highlight-1.0.3.tgz#90256857714d5c0cf83ac475aea64db7bf29a7cd"
  integrity sha1-kCVoV3FNXAz4OsR1rqZNt78pp80=
  dependencies:
    prismjs "^1.23.0"

"@wangeditor/core@^1.1.19":
  version "1.1.19"
  resolved "http://npm.devops.xiaohongshu.com:7001/@wangeditor/core/download/@wangeditor/core-1.1.19.tgz#f9155f7fd92d03cb1982405b3b82e54c31f1c2b0"
  integrity sha1-+RVff9ktA8sZgkBbO4LlTDHxwrA=
  dependencies:
    "@types/event-emitter" "^0.3.3"
    event-emitter "^0.3.5"
    html-void-elements "^2.0.0"
    i18next "^20.4.0"
    scroll-into-view-if-needed "^2.2.28"
    slate-history "^0.66.0"

"@wangeditor/editor-for-vue@next":
  version "5.1.12"
  resolved "http://npm.devops.xiaohongshu.com:7001/@wangeditor/editor-for-vue/download/@wangeditor/editor-for-vue-5.1.12.tgz#f7d5f239b39cdfc01d31151488de8443fe6edc64"
  integrity sha1-99XyObOc38AdMRUUiN6EQ/5u3GQ=

"@wangeditor/editor@^5.1.23":
  version "5.1.23"
  resolved "http://npm.devops.xiaohongshu.com:7001/@wangeditor/editor/download/@wangeditor/editor-5.1.23.tgz#c9d2007b7cb0ceef6b72692b4ee87b01ee2367b3"
  integrity sha1-ydIAe3ywzu9rcmkrTuh7Ae4jZ7M=
  dependencies:
    "@uppy/core" "^2.1.1"
    "@uppy/xhr-upload" "^2.0.3"
    "@wangeditor/basic-modules" "^1.1.7"
    "@wangeditor/code-highlight" "^1.0.3"
    "@wangeditor/core" "^1.1.19"
    "@wangeditor/list-module" "^1.0.5"
    "@wangeditor/table-module" "^1.1.4"
    "@wangeditor/upload-image-module" "^1.0.2"
    "@wangeditor/video-module" "^1.1.4"
    dom7 "^3.0.0"
    is-hotkey "^0.2.0"
    lodash.camelcase "^4.3.0"
    lodash.clonedeep "^4.5.0"
    lodash.debounce "^4.0.8"
    lodash.foreach "^4.5.0"
    lodash.isequal "^4.5.0"
    lodash.throttle "^4.1.1"
    lodash.toarray "^4.4.0"
    nanoid "^3.2.0"
    slate "^0.72.0"
    snabbdom "^3.1.0"

"@wangeditor/list-module@^1.0.5":
  version "1.0.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@wangeditor/list-module/download/@wangeditor/list-module-1.0.5.tgz#3fc0b167acddf885536b45fa0c127f9c6adaea33"
  integrity sha1-P8CxZ6zd+IVTa0X6DBJ/nGra6jM=

"@wangeditor/table-module@^1.1.4":
  version "1.1.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/@wangeditor/table-module/download/@wangeditor/table-module-1.1.4.tgz#757d4a5868b2b658041cd323854a4d707c8347e9"
  integrity sha1-dX1KWGiytlgEHNMjhUpNcHyDR+k=

"@wangeditor/upload-image-module@^1.0.2":
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/@wangeditor/upload-image-module/download/@wangeditor/upload-image-module-1.0.2.tgz#89e9b9467e10cbc6b11dc5748e08dd23aaebee30"
  integrity sha1-iem5Rn4Qy8axHcV0jgjdI6rr7jA=

"@wangeditor/video-module@^1.1.4":
  version "1.1.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/@wangeditor/video-module/download/@wangeditor/video-module-1.1.4.tgz#b9df1b3ab2cd53f678b19b4d927e200774a6f532"
  integrity sha1-ud8bOrLNU/Z4sZtNkn4gB3Sm9TI=

"@xhs/abtest@^0.6.0":
  version "0.6.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/abtest/download/@xhs/abtest-0.6.1.tgz#c2f4d3d806efe4f0518aeeea190007fde20c2dec"
  integrity sha1-wvTT2Abv5PBRiu7qGQAH/eIMLew=
  dependencies:
    "@xhs/ozone-bridge" "^2.17.6"

"@xhs/ark-datacenter@^0.1.10", "@xhs/ark-datacenter@^0.1.9":
  version "0.1.14"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/ark-datacenter/-/@xhs/ark-datacenter-0.1.14.tgz#69eda9e0be046052ac35aa1d3a06251da3cb2fd5"
  integrity sha512-jkHg2YF8cqomNDNjEThH4/pf5UVqGpuWmhVu1LvvZe421lS7mgMWTYXBrT3HWskmh1L9vlx9gqQfFewN8GpMVQ==
  dependencies:
    "@xhs/delight-official-date-select" "^1.2.11"
    "@xhs/meepo-delight-chart" "1.1.0"
    dayjs "^1.11.7"
    echarts "5.2.1"
    lodash-es "^4.17.21"

"@xhs/bridge-shared@^1.1.0":
  version "1.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/bridge-shared/download/@xhs/bridge-shared-1.1.0.tgz#c440ec5a45ce82e245a4e4c0a4dda6d03e162066"
  integrity sha1-xEDsWkXOguJFpOTApN2m0D4WIGY=

"@xhs/cube-canvas@^1.5.5":
  version "1.5.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/cube-canvas/download/@xhs/cube-canvas-1.5.5.tgz#29bcdcb05c82bed6af7379b017f8e9a4a2b1cd03"
  integrity sha1-KbzcsFyCvtavc3mwF/jppKKxzQM=
  dependencies:
    "@vue/composition-api" "^0.4.0"

"@xhs/cyan-bridge@^3.21.1":
  version "3.38.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/cyan-bridge/-/@xhs/cyan-bridge-3.38.1.tgz#d0f23a1854fa986bf55f42fc6b837278aa58cfcc"
  integrity sha1-0PI6GFT6mGv1X0L8a4NyeKpYz8w=
  dependencies:
    "@xhs/bridge-shared" "^1.1.0"
    "@xhs/data-transform" "^0.3.1"
    "@xhs/logger" "^2.2.4"
    "@xhs/ozone-schema" "^1.8.3"
    "@xhs/prop-types" "^15.7.5-0"

"@xhs/data-transform@^0.3.1", "@xhs/data-transform@^0.3.3":
  version "0.3.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/data-transform/download/@xhs/data-transform-0.3.3.tgz#9bc57313156ad0f638d65fa3799215bec08e8b15"
  integrity sha1-m8VzExVq0PY41l+jeZIVvsCOixU=

"@xhs/delight-ark-cascader@^1.6.2":
  version "1.7.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/delight-ark-cascader/download/@xhs/delight-ark-cascader-1.7.1.tgz#d891bd32f13e3af30bd0a61babd8edc76b92e508"
  integrity sha1-2JG9MvE+OvML0KYbq9jtx2uS5Qg=
  dependencies:
    "@xhs/delight" "^0.1.22"
    lodash.isequal "^4.5.0"

"@xhs/delight-formily@^0.6.6":
  version "0.6.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-formily/-/@xhs/delight-formily-0.6.6.tgz#8bfcdb4e82639d2cbb15ea14be5e35664edf38dc"
  integrity sha1-i/zbToJjnSy7FeoUvl41Zk7fONw=
  dependencies:
    "@babel/types" "^7.16.8"
    "@floating-ui/dom" "^0.1.10"
    "@formily/core" "2.2.7"
    "@formily/grid" "2.2.7"
    "@formily/json-schema" "2.2.7"
    "@formily/reactive" "2.2.7"
    "@formily/reactive-vue" "2.2.7"
    "@formily/shared" "2.2.7"
    "@formily/validator" "2.2.7"
    "@formily/vue" "2.2.7"
    "@icon-park/svg" "^1.3.5"
    "@types/node" "^14.17.1"
    "@vueuse/core" "^9.0.0"
    "@xhs/delight" "1.0.5"
    dayjs "^1.10.7"
    lodash "^4.17.21"
    lodash-unified "^1.0.2"
    lodash.clonedeep "^4.5.0"
    lodash.mergewith "^4.6.2"
    lodash.uniq "^4.5.0"
    resize-observer-polyfill "^1.5.1"
    vue-slicksort "^2.0.0-alpha.5"

"@xhs/delight-material-ark-cascader@^0.0.11":
  version "0.0.11"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/delight-material-ark-cascader/download/@xhs/delight-material-ark-cascader-0.0.11.tgz#21ef1e4f8a9224c97150d6b1cfb9db51c47896a8"
  integrity sha1-Ie8eT4qSJMlxUNaxz7nbUcR4lqg=
  dependencies:
    lodash.isequal "^4.5.0"

"@xhs/delight-material-ark-task-modal@^0.1.12":
  version "0.1.12"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-material-ark-task-modal/-/@xhs/delight-material-ark-task-modal-0.1.12.tgz#af619446a33e09da51a9503e6c4d0cef1309056a"
  integrity sha512-2ikQrWwmbNGyGwizDIQTmHlrM3L7iIvrBW8GRgXKUb90Pxix7wnpoxLRmO+yn/WKfvfcd27R8SXB3weG4Apxig==
  dependencies:
    "@xhs/uploader" "^3.0.4"

"@xhs/delight-material-ultra-custom-column@^0.1.8":
  version "0.1.8"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/delight-material-ultra-custom-column/download/@xhs/delight-material-ultra-custom-column-0.1.8.tgz#b59e26c3939909f51667aaf9eea01f62596aa5b4"
  integrity sha1-tZ4mw5OZCfUWZ6r57qAfYllqpbQ=

"@xhs/delight-material-ultra-delight-form@^0.1.9":
  version "0.1.9"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-material-ultra-delight-form/-/@xhs/delight-material-ultra-delight-form-0.1.9.tgz#71f6706eaf755038ed940fc9e46e918745bbcd88"
  integrity sha512-a2dG3/Jh2jHRZo2KVLYUSpaf4Tyrj8FuD2SEv1pY4Y5RaWrVPaI+pbmjiuGX3ZNL4B4TJlCbk6lPQ3Y5h/WwUg==

"@xhs/delight-material-ultra-filter@^0.1.6":
  version "0.1.14"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/delight-material-ultra-filter/download/@xhs/delight-material-ultra-filter-0.1.14.tgz#90d8cc7b98ddbff59d88f9ce839be27628723e85"
  integrity sha1-kNjMe5jdv/WdiPnOg5vidihyPoU=

"@xhs/delight-material-ultra-filter@^0.1.8":
  version "0.1.8"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/delight-material-ultra-filter/download/@xhs/delight-material-ultra-filter-0.1.8.tgz#73b6d5e435000c81675c367df6ca8a2f34bb6929"
  integrity sha1-c7bV5DUADIFnXDZ99sqKLzS7aSk=

"@xhs/delight-material-ultra-media-card@^0.1.7":
  version "0.1.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-material-ultra-media-card/-/@xhs/delight-material-ultra-media-card-0.1.7.tgz#71b2ea467c49b9f3d68ddeedf1070f6c7ffe4b6e"
  integrity sha1-cbLqRnxJufPWjd7t8QcPbH/+S24=

"@xhs/delight-material-ultra-mini-custom-column@^0.1.7":
  version "0.1.8"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/delight-material-ultra-mini-custom-column/download/@xhs/delight-material-ultra-mini-custom-column-0.1.8.tgz#7b8fe59fe688658e04b5b098557b5ddbbed54494"
  integrity sha1-e4/ln+aIZY4EtbCYVXtd277VRJQ=
  dependencies:
    sortablejs "1.14.0"

"@xhs/delight-material-ultra-outline-filter@^0.1.12":
  version "0.1.12"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/delight-material-ultra-outline-filter/download/@xhs/delight-material-ultra-outline-filter-0.1.12.tgz#93134fd8c5715548ee92479af1269b5ceda09bb3"
  integrity sha1-kxNP2MVxVUjukkea8SabXO2gm7M=
  dependencies:
    vuedraggable "4.1.0"

"@xhs/delight-material-ultra-page-header@^0.2.3":
  version "0.2.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/delight-material-ultra-page-header/download/@xhs/delight-material-ultra-page-header-0.2.3.tgz#73d190fa65b6be14dcfe0b5827dd9c81672ec76a"
  integrity sha1-c9GQ+mW2vhTc/gtYJ92cgWcux2o=

"@xhs/delight-material-ultra-page-header@^0.2.6":
  version "0.2.10"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/delight-material-ultra-page-header/download/@xhs/delight-material-ultra-page-header-0.2.10.tgz#7a39c07386ed25811e0e8cdf00c780d72bbd4c01"
  integrity sha1-ejnAc4btJYEeDozfAMeA1yu9TAE=

"@xhs/delight-material-ultra-table-action@0.1.10":
  version "0.1.10"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/delight-material-ultra-table-action/download/@xhs/delight-material-ultra-table-action-0.1.10.tgz#698002d07651e03e64b4b2d2718c45bb2d71f8a3"
  integrity sha1-aYAC0HZR4D5ktLLScYxFuy1x+KM=

"@xhs/delight-material-ultra-table-cell@^0.1.11":
  version "0.1.21"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/delight-material-ultra-table-cell/download/@xhs/delight-material-ultra-table-cell-0.1.21.tgz#65db941bf0f880f19375000ff63c23b05cd8deb1"
  integrity sha1-ZduUG/D4gPGTdQAP9jwjsFzY3rE=

"@xhs/delight-material-ultra-table-cell@^0.1.29":
  version "0.1.29"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-material-ultra-table-cell/-/@xhs/delight-material-ultra-table-cell-0.1.29.tgz#064cbfd06950576fce961390e6b2be891c7d1911"
  integrity sha512-V7rQBxX1i7TmDEkz+KwSZaKXKIMtW3GCrkPwRvngZXGsCCHRWDt4CQO0tb2XNmSdZnpkuXlekGit6EOEiwFmJQ==

"@xhs/delight-material-ultra-toolbar@^0.1.20":
  version "0.1.28"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/delight-material-ultra-toolbar/download/@xhs/delight-material-ultra-toolbar-0.1.28.tgz#16ad9797780a6e03de66fa946320951a362c6062"
  integrity sha1-Fq2Xl3gKbgPeZvqUYyCVGjYsYGI=
  dependencies:
    resize-observer-polyfill "^1.5.1"

"@xhs/delight-material-ultra-toolbar@^0.1.29":
  version "0.1.29"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-material-ultra-toolbar/-/@xhs/delight-material-ultra-toolbar-0.1.29.tgz#c5ec614a4e18a007471163f8d6dac4c600ec0d5b"
  integrity sha1-xexhSk4YoAdHEWP41trExgDsDVs=
  dependencies:
    resize-observer-polyfill "^1.5.1"

"@xhs/delight-official-date-select@^1.2.11":
  version "1.2.11"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-official-date-select/-/@xhs/delight-official-date-select-1.2.11.tgz#8b6e6ab66f918f688286372154b51afb212eac90"
  integrity sha1-i25qtm+Rj2iChjchVLUa+yEurJA=
  dependencies:
    "@xhs/yam-beer" "^5.9.4"
    dayjs "^1.11.0"

"@xhs/delight@1.0.16":
  version "1.0.16"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight/-/@xhs/delight-1.0.16.tgz#2d70321306049684d406a67f94d5c864e24f9cbe"
  integrity sha512-yziw4Voda7wgX6g7Wje/8SOkS1N+/CSsGOfgzFquOEyRDtcx19uwm2t2AwbL7l9Daheqa6ikfmeXceQ97oUxAQ==
  dependencies:
    "@babel/types" "^7.16.8"
    "@floating-ui/dom" "^0.1.10"
    "@icon-park/svg" "1.4.1"
    "@types/node" "^14.17.1"
    "@vueuse/core" "^9.3.1"
    async-validator "^4.2.5"
    dayjs "^1.10.7"
    lodash.clonedeep "^4.5.0"
    lodash.get "^4.4.2"
    lodash.mergewith "^4.6.2"
    lodash.set "^4.3.2"
    lodash.uniq "^4.5.0"
    resize-observer-polyfill "^1.5.1"
    sortablejs "^1.15.0"
    vue-virtual-scroller "^2.0.0-beta.3"

"@xhs/delight@1.0.5":
  version "1.0.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/delight/download/@xhs/delight-1.0.5.tgz#180f2194791c23d1f39ed74dfa316a69f565f9e6"
  integrity sha1-GA8hlHkcI9HzntdN+jFqafVl+eY=
  dependencies:
    "@babel/types" "^7.16.8"
    "@floating-ui/dom" "^0.1.10"
    "@icon-park/svg" "1.4.1"
    "@types/node" "^14.17.1"
    "@vueuse/core" "^9.3.1"
    async-validator "^4.2.5"
    dayjs "^1.10.7"
    lodash.clonedeep "^4.5.0"
    lodash.get "^4.4.2"
    lodash.mergewith "^4.6.2"
    lodash.set "^4.3.2"
    lodash.uniq "^4.5.0"
    vue-virtual-scroller "^2.0.0-beta.3"

"@xhs/delight@^0.1.22", "@xhs/delight@^0.1.42":
  version "0.1.42"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/delight/download/@xhs/delight-0.1.42.tgz#cd03d5b90454516e6c7cf9147b9f3113a9f8ae2a"
  integrity sha1-zQPVuQRUUW5sfPkUe58xE6n4rio=
  dependencies:
    "@babel/types" "^7.16.8"
    "@floating-ui/dom" "^0.1.10"
    "@icon-park/svg" "1.4.1"
    "@types/node" "^14.17.1"
    "@vueuse/core" "^9.3.1"
    async-validator "^4.2.5"
    dayjs "^1.10.7"
    lodash.clonedeep "^4.5.0"
    lodash.get "^4.4.2"
    lodash.mergewith "^4.6.2"
    lodash.set "^4.3.2"
    lodash.uniq "^4.5.0"
    sortablejs "^1.15.0"
    vue-virtual-scroller "^2.0.0-beta.3"

"@xhs/eaglet-emitter-base@^0.1.3":
  version "0.1.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/eaglet-emitter-base/download/@xhs/eaglet-emitter-base-0.1.3.tgz#b86746f0272909eca0f462351e6f7c7edafb220f"
  integrity sha1-uGdG8CcpCeyg9GI1Hm98ftr7Ig8=

"@xhs/eaglet@1.1.1-1":
  version "1.1.1-1"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/eaglet/download/@xhs/eaglet-1.1.1-1.tgz#eb22cb012a1bdd9610e948ad1ca2a38abf3e4c76"
  integrity sha1-6yLLASob3ZYQ6UitHKKjir8+THY=

"@xhs/ejs-loader@^0.0.3":
  version "0.0.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/ejs-loader/download/@xhs/ejs-loader-0.0.3.tgz#143e00d6ea497f72e5a03c1a3f41ba60e5d7cf28"
  integrity sha1-FD4A1upJf3LloDwaP0G6YOXXzyg=

"@xhs/fe-api-sign@^0.2.0":
  version "0.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/fe-api-sign/download/@xhs/fe-api-sign-0.2.0.tgz#a97e0b767add2243a84bd41c2c9e2f8f89d25a32"
  integrity sha1-qX4LdnrdIkOoS9QcLJ4vj4nSWjI=
  dependencies:
    md5 "^2.2.1"

"@xhs/file-type@^1.0.3":
  version "1.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/file-type/download/@xhs/file-type-1.1.0.tgz#158cb36f4d10a02c068490339c21ce31c99a56bb"
  integrity sha1-FYyzb00QoCwGhJAznCHOMcmaVrs=
  dependencies:
    readable-web-to-node-stream "^2.0.0"
    strtok3 "^6.0.3"
    token-types "^2.0.0"
    typedarray-to-buffer "^3.1.5"

"@xhs/formula-utils@^1.4.1":
  version "1.4.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/formula-utils/download/@xhs/formula-utils-1.4.1.tgz#6fbc76b12dfae8c16fc8473841dbbd888cef2435"
  integrity sha1-b7x2sS366MFvyEc4Qdu9iIzvJDU=

"@xhs/hawk-change-control@0.0.2-change-version.30":
  version "0.0.2-change-version.30"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/hawk-change-control/-/@xhs/hawk-change-control-0.0.2-change-version.30.tgz#e79edbcf592b018448fa3213bc7b751bba4512c4"
  integrity sha512-xxo0NcGyL4XQWyqLFprKA829TuIVSGz5zEo6agSzrfXlOOVAfba6lja7JZmFK4l9vQQ/ooYN3ecLwPY28Y23ig==

"@xhs/hawk@0.1.7-beta.18":
  version "0.1.7-beta.18"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/hawk/-/@xhs/hawk-0.1.7-beta.18.tgz#2c5e60720a2c571276a4529a8a18806a8787e4a3"
  integrity sha512-okUeaJxIhWWXcIpg68btTXL3ePN5w6H2nkoklW/TIPStv7NYgbZkPyz8ln+XudXyWWoLt57o/LTbpiQB4kTbjA==
  dependencies:
    "@xhs/ark-datacenter" "^0.1.9"
    "@xhs/delight-material-ultra-filter" "^0.1.6"
    "@xhs/delight-material-ultra-media-card" "^0.1.7"
    "@xhs/delight-material-ultra-mini-custom-column" "^0.1.7"
    "@xhs/delight-material-ultra-outline-filter" "^0.1.12"
    "@xhs/delight-material-ultra-page-header" "^0.2.6"
    "@xhs/delight-material-ultra-table-cell" "^0.1.11"
    "@xhs/delight-material-ultra-toolbar" "^0.1.20"
    "@xhs/logan-services-barley-iron" "^0.1.1"
    "@xhs/logan-services-barley-iron-edith" "^0.1.5"
    "@xhs/m2" "^0.0.8"
    "@xhs/ozone-opener" "^1.3.3"
    lodash-es "^4.17.21"
    url-parse "^1.5.1"
    vuedraggable "^4.1.0"

"@xhs/http@1.10.1":
  version "1.10.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/http/-/@xhs/http-1.10.1.tgz#d6ffba00b8bdf88db0212a49299089fb7ec0799b"
  integrity sha1-1v+6ALi9+I2wISpJKZCJ+37AeZs=
  dependencies:
    "@xhs/data-transform" "^0.3.3"
    "@xhs/logger" "^2.3.0"
    axios "^0.19.2"

"@xhs/http@^1.11.0", "@xhs/http@^1.2.3":
  version "1.11.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/http/download/@xhs/http-1.11.0.tgz#8e0ded96d2cc76f85f54c06f5f8b228504b3111d"
  integrity sha1-jg3tltLMdvhfVMBvX4sihQSzER0=
  dependencies:
    "@xhs/data-transform" "^0.3.3"
    "@xhs/logger" "^2.3.0"
    axios "^0.19.2"

"@xhs/http@^1.2.2":
  version "1.13.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/http/-/@xhs/http-1.13.1.tgz#92f28447fb3a2c8d80129e241146fa2e4a6df9d0"
  integrity sha512-PJRzhS/kMDSCJ9HNliPE4c3GwpHC+r14mJgHFgQ7cBiGv6jgfzJjMB29EytrcDgOhU5yQvUvp9+KVMgc+b5Kxg==
  dependencies:
    "@xhs/data-transform" "^0.3.3"
    "@xhs/logger" "^3.0.1"
    "@xhs/untrace" "^0.0.1"
    axios "^0.19.2"

"@xhs/hulk-decorators@~1.2.11":
  version "1.2.11"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/hulk-decorators/download/@xhs/hulk-decorators-1.2.11.tgz#0481aea1728ebe5bffafb9b413ca9f20b9520b79"
  integrity sha1-BIGuoXKOvlv/r7m0E8qfILlSC3k=
  dependencies:
    "@types/koa" "~2.13.6"
    "@types/koa-bodyparser" "~4.3.7"
    "@types/koa-compose" "~3.2.5"
    "@types/koa-static" "~4.0.2"
    ajv "~8.12.0"
    find-my-way "~7.6.0"
    fluent-json-schema "~3.0.1"
    klaw-sync "~6.0.0"
    koa "~2.14.1"
    koa-bodyparser "~4.4.0"
    koa-compose "~4.1.0"
    koa-static "~5.0.0"
    lodash.merge "~4.6.2"
    reflect-metadata "~0.1.13"

"@xhs/hulk-env@~1.0.2":
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/hulk-env/download/@xhs/hulk-env-1.0.2.tgz#33e225018d294e44f2673091193f609d2ed134ee"
  integrity sha1-M+IlAY0pTkTyZzCRGT9gnS7RNO4=

"@xhs/hulk-http-service@~2.0.2":
  version "2.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/hulk-http-service/download/@xhs/hulk-http-service-2.0.2.tgz#3cce248f28ed194f1ae1058861f1bc8d549999fc"
  integrity sha1-PM4kjyjtGU8a4QWIYfG8jVSZmfw=
  dependencies:
    "@types/node-fetch" "2.5.12"
    abort-controller "~3.0.0"
    node-fetch "~2.6.9"

"@xhs/hulk-logger-service@~1.0.4":
  version "1.0.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/hulk-logger-service/download/@xhs/hulk-logger-service-1.0.4.tgz#8c0a1e68f956098b39dde5e17cdffedc58baf903"
  integrity sha1-jAoeaPlWCYs53eXhfN/+3Fi6+QM=

"@xhs/hulk-metrics@~2.0.3":
  version "2.0.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/hulk-metrics/download/@xhs/hulk-metrics-2.0.3.tgz#b9a3b887e0c1b604c7104df7121dcd9ff3cda5c6"
  integrity sha1-uaO4h+DBtgTHEE33Eh3Nn/PNpcY=
  dependencies:
    prom-client "~14.1.1"

"@xhs/hulk-process-limit-middleware@~1.3.2":
  version "1.3.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/hulk-process-limit-middleware/download/@xhs/hulk-process-limit-middleware-1.3.2.tgz#b9867bf150e2e5b199eea2e373073d73aebedb52"
  integrity sha1-uYZ78VDi5bGZ7qLjcwc9c66+21I=

"@xhs/hulk-rate-limit-middleware@~1.0.1":
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/hulk-rate-limit-middleware/download/@xhs/hulk-rate-limit-middleware-1.0.1.tgz#feabcdd1d7284975068d44e2c952d0132ea06dda"
  integrity sha1-/qvN0dcoSXUGjUTiyVLQEy6gbdo=

"@xhs/hulk-thrift-agent@~2.0.2":
  version "2.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/hulk-thrift-agent/download/@xhs/hulk-thrift-agent-2.0.2.tgz#b4612a706e7a48fd804d9dc64765255335c31be8"
  integrity sha1-tGEqcG56SP2ATZ3GR2UlUzXDG+g=
  dependencies:
    "@types/node-int64" "~0.4.29"
    "@types/thrift" "~0.10.12"
    node-int64 "~0.4.0"
    thrift "~0.18.1"

"@xhs/hulk-xds-service@~1.0.1":
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/hulk-xds-service/download/@xhs/hulk-xds-service-1.0.1.tgz#995dcf9d48c2eea04823afec70daf5940ab14838"
  integrity sha1-mV3PnUjC7qBII6/scNr1lAqxSDg=

"@xhs/hulk-xds@~1.1.4":
  version "1.1.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/hulk-xds/download/@xhs/hulk-xds-1.1.4.tgz#6a6ff3fbd906fd8b0702cd8668b3c49f067ca8b3"
  integrity sha1-am/z+9kG/YsHAs2GaLPEnwZ8qLM=
  dependencies:
    "@bufbuild/connect" "~0.8.5"
    "@bufbuild/connect-node" "~0.8.5"
    "@bufbuild/protobuf" "~1.2.0"
    "@bufbuild/protoc-gen-connect-es" "~0.8.5"
    "@bufbuild/protoc-gen-es" "~1.2.0"

"@xhs/hulk@^2.0.0":
  version "2.0.11"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/hulk/download/@xhs/hulk-2.0.11.tgz#01ec84d1133b46a4364256230796fb59b79b2bbd"
  integrity sha1-AeyE0RM7RqQ2QlYjB5b7WbebK70=
  dependencies:
    "@xhs/hulk-decorators" "~1.2.11"
    "@xhs/hulk-env" "~1.0.2"
    "@xhs/hulk-http-service" "~2.0.2"
    "@xhs/hulk-logger-service" "~1.0.4"
    "@xhs/hulk-metrics" "~2.0.3"
    "@xhs/hulk-process-limit-middleware" "~1.3.2"
    "@xhs/hulk-rate-limit-middleware" "~1.0.1"
    "@xhs/hulk-thrift-agent" "~2.0.2"
    "@xhs/hulk-xds" "~1.1.4"
    "@xhs/hulk-xds-service" "~1.0.1"

"@xhs/launcher-plugin-auth@^2.1.1":
  version "2.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/launcher-plugin-auth/download/@xhs/launcher-plugin-auth-2.1.1.tgz#723ec0b43d2c856f59f45c1d8973da4060cfdffb"
  integrity sha1-cj7AtD0shW9Z9FwdiXPaQGDP3/s=
  dependencies:
    "@xhs/logger" "^2.6.1"
    "@xhs/ozone-bridge" "^3.19.1"
    "@xhs/ozone-detector" "^3.10.0"

"@xhs/launcher-plugin-eaglet@^5.0.5":
  version "5.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/launcher-plugin-eaglet/download/@xhs/launcher-plugin-eaglet-5.1.0.tgz#20bb23ebc64b27cb8b08a0742c0aab988f6c333f"
  integrity sha1-ILsj68ZLJ8uLCKB0LAqrmI9sMz8=
  dependencies:
    "@xhs/abtest" "^0.6.0"
    "@xhs/eaglet" "1.1.1-1"
    "@xhs/eaglet-emitter-base" "^0.1.3"
    "@xhs/ozone-bridge" "^3.7.5"
    "@xhs/perf-metrics" "1.10.3-22"
    google-protobuf "^3.17.3"
    lodash.uniqueid "^4.0.1"
    uuid "^8.3.2"

"@xhs/launcher-plugin-goods3@^1.0.26":
  version "1.0.26"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/launcher-plugin-goods3/download/@xhs/launcher-plugin-goods3-1.0.26.tgz#e1a0e2c01fc4bd7a1a76352d5bfdb12c45ec784d"
  integrity sha1-4aDiwB/EvXoadjUtW/2xLEXseE0=

"@xhs/launcher-plugin-microapp@^2.0.0-alpha.16":
  version "2.0.0-alpha.16"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/launcher-plugin-microapp/download/@xhs/launcher-plugin-microapp-2.0.0-alpha.16.tgz#14ee02ef5c25be4d72db1e9a22bc41ffef4be8ae"
  integrity sha1-FO4C71wlvk1y2x6aIrxB/+9L6K4=
  dependencies:
    "@sentry/browser" "^6.17.2"
    "@xhs/ejs-loader" "^0.0.3"
    qiankun "^2.7.3"
    webpack-merge "^5.8.0"
    whatwg-fetch "^3.6.2"

"@xhs/launcher-plugin-store@^2.0.1":
  version "2.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/launcher-plugin-store/download/@xhs/launcher-plugin-store-2.0.1.tgz#be654902f3112a024c4bb1f4f7fa4c580bb7ac8d"
  integrity sha1-vmVJAvMRKgJMS7H09/pMWAu3rI0=
  dependencies:
    vuex "^4.0.0"

"@xhs/launcher-plugin-tracker@0.0.13":
  version "0.0.13"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/launcher-plugin-tracker/download/@xhs/launcher-plugin-tracker-0.0.13.tgz#8aa2e75c071b1acafc781ed651576a0cb0f705ba"
  integrity sha1-iqLnXAcbGsr8eB7WUVdqDLD3Bbo=
  dependencies:
    "@xhs/ozone-schema" "^1.15.0"
    axios "^1.1.3"
    lodash.uniqueid "^4.0.1"
    uuid "^8.3.2"

"@xhs/launcher@3.13.5":
  version "3.13.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/launcher/download/@xhs/launcher-3.13.5.tgz#ca59d12e8558c59e9d6857eca3f236a430b8f0a5"
  integrity sha1-ylnRLoVYxZ6daFfso/I2pDC48KU=
  dependencies:
    "@vue/compat" "^3.2.31"
    "@vue/compiler-sfc" "^3.2.31"
    "@vueuse/head" "^0.7.5"
    "@xhs/fe-api-sign" "^0.2.0"
    "@xhs/http" "^1.11.0"
    "@xhs/hulk" "^2.0.0"
    "@xhs/logger" "^2.6.2"
    "@xhs/ozone-bridge" "^3.18.0"
    escape-html "^1.0.3"
    serialize-javascript "^6.0.0"
    vue "^3.2.22"
    vue-router "^4.0.14"

"@xhs/logan-services-barley-iron-edith@^0.1.5":
  version "0.1.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/logan-services-barley-iron-edith/download/@xhs/logan-services-barley-iron-edith-0.1.5.tgz#d84e87127cac0a51a5170b3923859cfe8747032f"
  integrity sha1-2E6HEnysClGlFws5I4Wc/odHAy8=

"@xhs/logan-services-barley-iron@^0.1.1":
  version "0.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/logan-services-barley-iron/download/@xhs/logan-services-barley-iron-0.1.1.tgz#32f3e563649846a9d4282c73b6e2f96db72d97a5"
  integrity sha1-MvPlY2SYRqnUKCxztuL5bbctl6U=

"@xhs/logan-services-vincent-admin@^1.3.30":
  version "1.3.35"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/logan-services-vincent-admin/-/@xhs/logan-services-vincent-admin-1.3.35.tgz#12ea10f98be11c90238c3c9fbe02d070b6dae8b0"
  integrity sha512-GjuGBpISoTcU9xys5VjW2U+KfQT4pBJ6LoV5oJP9ujtJpgvMQnQo6Vf0QHJR1E/9iP/QZ+zD+LEpPjcFYgRzcQ==

"@xhs/logger@^2.2.2", "@xhs/logger@^2.3.0", "@xhs/logger@^2.5.1", "@xhs/logger@^2.6.1":
  version "2.6.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/logger/download/@xhs/logger-2.6.1.tgz#d363867be9f44f715ba76d5a4929cbb8e8613c1e"
  integrity sha1-02OGe+n0T3Fbp21aSSnLuOhhPB4=

"@xhs/logger@^2.2.4", "@xhs/logger@^2.5.0", "@xhs/logger@^2.6.2":
  version "2.6.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/logger/download/@xhs/logger-2.6.2.tgz#8c1d23b777785f169e1c6c934c42501cf81efb56"
  integrity sha1-jB0jt3d4XxaeHGyTTEJQHPge+1Y=

"@xhs/logger@^3.0.1":
  version "3.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/logger/-/@xhs/logger-3.0.1.tgz#7a8e7504b45c3ba7d8deb89ba55c581d7f7b3dd7"
  integrity sha1-eo51BLRcO6fY3ribpVxYHX97Pdc=

"@xhs/m2@^0.0.8":
  version "0.0.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/m2/-/@xhs/m2-0.0.8.tgz#462d23176e0974301d20e571b1687da9769c5809"
  integrity sha512-XG7dSO0a3TRhgncZwFFjPiqXCc1pT1OpUdEwy7dEz0T2ICgveSLSuonD4ACUzcbUBpxOQsBd/ILfyaGg9JY5Lw==
  dependencies:
    "@xhs/ark-datacenter" "^0.1.10"
    "@xhs/delight-material-ultra-filter" "^0.1.6"
    "@xhs/delight-material-ultra-outline-filter" "^0.1.12"
    "@xhs/delight-material-ultra-table-cell" "^0.1.11"
    "@xhs/delight-material-ultra-toolbar" "^0.1.20"
    copy-to-clipboard "^3.3.3"
    lodash-es "^4.17.21"

"@xhs/meepo-delight-chart@1.1.0":
  version "1.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/meepo-delight-chart/-/@xhs/meepo-delight-chart-1.1.0.tgz#2e621278d3f18884552012b547cc23c0b66d6316"
  integrity sha1-LmISeNPxiIRVIBK1R8wjwLZtYxY=
  dependencies:
    echarts "^5.2.2"

"@xhs/ozone-bridge@^2.17.6":
  version "2.37.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/ozone-bridge/download/@xhs/ozone-bridge-2.37.0.tgz#3f03d27bececd47de175f196cb416e51ac0b7f42"
  integrity sha1-PwPSe+zs1H3hdfGWy0FuUawLf0I=
  dependencies:
    "@xhs/data-transform" "^0.3.3"
    "@xhs/logger" "^2.2.2"
    "@xhs/ozone-detector" "^3.4.4"
    prop-types "^15.7.2"
    uuid "^7.0.2"

"@xhs/ozone-bridge@^3.18.0", "@xhs/ozone-bridge@^3.19.1", "@xhs/ozone-bridge@^3.7.5":
  version "3.25.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/ozone-bridge/download/@xhs/ozone-bridge-3.25.1.tgz#1239d70c0b8f1e4a22ceef054aabf26d922a21ad"
  integrity sha1-EjnXDAuPHkoizu8FSqvybZIqIa0=
  dependencies:
    "@xhs/bridge-shared" "^1.1.0"
    "@xhs/data-transform" "^0.3.3"
    "@xhs/logger" "^2.2.2"
    "@xhs/ozone-detector" "^3.4.4"
    "@xhs/ozone-schema" "^1.8.3"
    prop-types "^15.7.2"
    uuid "^7.0.2"

"@xhs/ozone-deeplink@^2.0.0":
  version "2.6.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/ozone-deeplink/-/@xhs/ozone-deeplink-2.6.0.tgz#d8f12c6cbef55b5c571cd0afcce47d8c452d9d35"
  integrity sha1-2PEsbL71W1xXHNCvzOR9jEUtnTU=
  dependencies:
    "@xhs/cyan-bridge" "^3.21.1"
    "@xhs/http" "1.10.1"
    "@xhs/ozone-detector" "^3.4.3"
    "@xhs/ozone-sdk" "^2.0.0"

"@xhs/ozone-detector@^3.10.0", "@xhs/ozone-detector@^3.11.2", "@xhs/ozone-detector@^3.4.4":
  version "3.11.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/ozone-detector/download/@xhs/ozone-detector-3.11.4.tgz#32a352f227f468cae89e661a9d0dacd03557cd97"
  integrity sha1-MqNS8if0aMronmYanQ2s0DVXzZc=

"@xhs/ozone-detector@^3.13.0", "@xhs/ozone-detector@^3.2.0", "@xhs/ozone-detector@^3.4.3":
  version "3.13.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/ozone-detector/-/@xhs/ozone-detector-3.13.1.tgz#fe60fb82c7240e3b369a6a3ef320bc188d2c46c7"
  integrity sha512-8gwIoL0MFU3Iy/7CHQZL6cY24HC36YuzzeMolEzUgrvLSAscsrmCl8k39jEht739vB1oGDOgIK1aqN9ZsjS3kw==

"@xhs/ozone-download@^2.0.1":
  version "2.10.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/ozone-download/-/@xhs/ozone-download-2.10.1.tgz#8ccb190fa83ce89400fd7dbbdb9185e6345065c1"
  integrity sha512-71sDDXBulo0RrRFTtDBoCfnx21od+bsRB3wPhf1v/EnBYTd66R1rSjWISYD5f3DDZtS3te0Y4l2u5lfq7JSwZg==
  dependencies:
    "@xhs/ozone-deeplink" "^2.0.0"
    "@xhs/ozone-detector" "^3.13.0"
    "@xhs/ozone-sdk" "^2.0.0"
    js-cookie "^3.0.5"

"@xhs/ozone-opener@^1.3.3":
  version "1.3.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/ozone-opener/-/@xhs/ozone-opener-1.3.5.tgz#4f83af4b4bd70f20f317a196a150072a97c858bf"
  integrity sha1-T4OvS0vXDyDzF6GWoVAHKpfIWL8=
  dependencies:
    "@xhs/ozone-detector" "^3.4.3"
    "@xhs/ozone-download" "^2.0.1"
    "@xhs/ozone-sdk" "^2.0.0"

"@xhs/ozone-opener@^2.0.10":
  version "2.0.10"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/ozone-opener/-/@xhs/ozone-opener-2.0.10.tgz#f93f590ea3eeb99004a5dae4b63f493baa67f860"
  integrity sha1-+T9ZDqPuuZAEpdrktj9JO6pn+GA=
  dependencies:
    "@xhs/ozone-detector" "^3.4.3"
    "@xhs/ozone-download" "^2.0.1"
    "@xhs/ozone-sdk" "^2.0.0"

"@xhs/ozone-schema@^1.15.0", "@xhs/ozone-schema@^1.8.3":
  version "1.15.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/ozone-schema/download/@xhs/ozone-schema-1.15.0.tgz#05b5f79585dfd035a5241d49bc850822089c6f69"
  integrity sha1-BbX3lYXf0DWlJB1JvIUIIgicb2k=
  dependencies:
    "@types/uuid" "^8.3.0"
    "@xhs/data-transform" "^0.3.3"
    "@xhs/logger" "^2.5.1"
    "@xhs/ozone-detector" "^3.11.2"
    jsonschema "^1.2.6"
    prop-types "^15.8.1"
    url "^0.11.0"
    uuid "^8.1.0"

"@xhs/ozone-sdk@^2.0.0":
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/ozone-sdk/-/@xhs/ozone-sdk-2.0.0.tgz#1edb02a77c6a26ded6083bbe88ec4ab9d86986a4"
  integrity sha1-HtsCp3xqJt7WCDu+iOxKudhphqQ=
  dependencies:
    "@xhs/http" "^1.2.2"
    "@xhs/ozone-detector" "^3.2.0"

"@xhs/paint-timing@^0.3.0":
  version "0.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/paint-timing/download/@xhs/paint-timing-0.3.0.tgz#6704b742f3237941129741e73a33e611785e2013"
  integrity sha1-ZwS3QvMjeUESl0HnOjPmEXheIBM=

"@xhs/perf-metrics@1.10.3-22":
  version "1.10.3-22"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/perf-metrics/download/@xhs/perf-metrics-1.10.3-22.tgz#7713b87e5777d823ccb3d0563f108db8e2b8d43a"
  integrity sha1-dxO4fld32CPMs9BWPxCNuOK41Do=
  dependencies:
    "@xhs/paint-timing" "^0.3.0"
    uuid "^8.3.1"

"@xhs/promotion-activity@0.0.15-beta.0":
  version "0.0.15-beta.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/promotion-activity/-/@xhs/promotion-activity-0.0.15-beta.0.tgz#7fa260e74ae9a5825f81753b063236c135e1387e"
  integrity sha512-ZDWcReiapZgSP+hp/OUmtAYO7BwSAxSwonisjNDvNpMvNAQ3MAbHaGfzzzjJbT+Hi1EuXPVZgQ1Ti4o+mU2KkA==
  dependencies:
    "@formily/core" "2.2.7"
    "@formily/reactive-vue" "2.2.7"
    "@formily/vue" "2.2.7"

"@xhs/prop-types@^15.7.5-0":
  version "15.7.5-0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/prop-types/-/@xhs/prop-types-15.7.5-0.tgz#dc58b48f8a0020bb8be532960fef720201063a48"
  integrity sha1-3Fi0j4oAILuL5TKWD+9yAgEGOkg=
  dependencies:
    loose-envify "^1.3.1"
    object-assign "^4.1.1"

"@xhs/untrace@^0.0.1":
  version "0.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/untrace/-/@xhs/untrace-0.0.1.tgz#4da50cf8c63e528b94ed52f8d8665cf03c6ff339"
  integrity sha1-TaUM+MY+UouU7VL42GZc8Dxv8zk=
  dependencies:
    long "^5.2.3"

"@xhs/uploader@^1.2.0":
  version "1.3.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/uploader/download/@xhs/uploader-1.3.4.tgz#1783ec6da26681b1905d4ab0e6212797a8dc2121"
  integrity sha1-F4PsbaJmgbGQXUqw5iEnl6jcISE=
  dependencies:
    "@xhs/http" "^1.2.3"
    "@xhs/logger" "^2.5.0"
    qiniu-js "^3.1.0"

"@xhs/uploader@^3.0.2":
  version "3.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/uploader/download/@xhs/uploader-3.0.2.tgz#189dcf240a78c27700c0a18d6c9229d1f965a4d2"
  integrity sha1-GJ3PJAp4wncAwKGNbJIp0fllpNI=
  dependencies:
    cos-js-sdk-v5 "^1.3.8"

"@xhs/uploader@^3.0.4":
  version "3.0.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/uploader/download/@xhs/uploader-3.0.4.tgz#d4de3123080ae2a6eae89c2dcb03e845aa62ee5f"
  integrity sha1-1N4xIwgK4qbq6JwtywPoRapi7l8=
  dependencies:
    cos-js-sdk-v5 "^1.3.8"

"@xhs/use-formily-core@0.0.11", "@xhs/use-formily-core@^0.0.11":
  version "0.0.11"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/use-formily-core/-/@xhs/use-formily-core-0.0.11.tgz#8169532f057bf2ed8afaf8350036fb1a3f58b4a4"
  integrity sha512-VqfbQM6BXE4rXnGlbKtDMBbIRtboiVtQqvsOjzLjnZPfkwvK1MT0B6aACU3cG5Qz/1jWWxIa4bIjqrhmut2f0A==
  dependencies:
    "@formily/core" "2.2.7"
    "@formily/json-schema" "2.2.7"
    "@formily/shared" "2.2.7"
    "@formily/validator" "2.2.7"

"@xhs/use-formily-delight@^0.0.21":
  version "0.0.21"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/use-formily-delight/-/@xhs/use-formily-delight-0.0.21.tgz#26a5615e488b60cdce7dde6640a73215e209f5dd"
  integrity sha512-yXwDMR9kc93hVndeJg9tqXO2uvJWQuD9DauCbWM0hz9S9Uraj89bNGThSRRae3LlDu1WWQDqeIK4HWw8pVwrug==
  dependencies:
    "@formily/core" "2.2.7"
    "@formily/grid" "2.2.7"
    "@formily/json-schema" "2.2.7"
    "@formily/reactive" "2.2.7"
    "@formily/reactive-vue" "2.2.7"
    "@formily/shared" "2.2.7"
    "@formily/validator" "2.2.7"
    "@formily/vue" "2.2.7"
    "@xhs/delight" "1.0.16"
    "@xhs/use-formily-core" "0.0.11"
    dayjs "^1.11.9"
    vxe-table "^4.5.10"
    xe-utils "^3.5.13"

"@xhs/yam-beer@^5.17.0", "@xhs/yam-beer@^5.9.4":
  version "5.17.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xhs/yam-beer/download/@xhs/yam-beer-5.17.0.tgz#746d5ca9ca3cf7cc63e289dfa4614f117eca39ed"
  integrity sha1-dG1cqco898xj4onfpGFPEX7KOe0=
  dependencies:
    "@blueprintjs/icons" "^3.10.0"
    "@emotion/cache" "^10.0.15"
    "@emotion/serialize" "^0.11.9"
    "@emotion/sheet" "^0.9.3"
    "@emotion/utils" "^0.11.2"
    "@popperjs/core" "^2.9.2"
    "@types/body-scroll-lock" "^2.6.1"
    "@types/lodash" "^4.14.165"
    "@xhs/file-type" "^1.0.3"
    "@xhs/uploader" "^1.2.0"
    accounting-js "^1.1.1"
    attr-accept "^2.2.2"
    body-scroll-lock "^3.0.3"
    csstype "^3.0.5"
    dayjs "^1.8.18"
    file-selector "^0.2.4"
    lodash "^4.17.15"
    object-assign "^4.1.1"
    qrious "^4.0.2"
    selectable.js "^0.17.6"
    sortablejs "^1.10.2"
    tinycolor2 "^1.4.1"
    v-click-outside "^2.1.3"
    viewerjs "^1.9.0"
    yup "^0.27.0"

"@xmldom/xmldom@^0.8.2":
  version "0.8.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/@xmldom/xmldom/download/@xmldom/xmldom-0.8.2.tgz#b695ff674e8216efa632a3d36ad51ae9843380c0"
  integrity sha1-tpX/Z06CFu+mMqPTatUa6YQzgMA=

"@yaireo/tagify@^4.35.0":
  version "4.35.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@yaireo/tagify/-/tagify-4.35.0.tgz#b7b0eba66962df16ab3fa8b5312880667a7d7de7"
  integrity sha512-D+o/c2rAhNA/Cmiw1sV1YtuxPCqB0UpVOXdnR9DXPuMHvU0quIEkU3FA6JDXTYMQV6FL7YGwonq2yPMfkc83lQ==

"@zkochan/cmd-shim@^3.1.0":
  version "3.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/@zkochan/cmd-shim/download/@zkochan/cmd-shim-3.1.0.tgz#2ab8ed81f5bb5452a85f25758eb9b8681982fd2e"
  integrity sha1-KrjtgfW7VFKoXyV1jrm4aBmC/S4=
  dependencies:
    is-windows "^1.0.0"
    mkdirp-promise "^5.0.1"
    mz "^2.5.0"

JSONStream@^1.0.4, JSONStream@^1.3.4:
  version "1.3.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/JSONStream/download/JSONStream-1.3.5.tgz#3208c1f08d3a4d99261ab64f92302bc15e111ca0"
  integrity sha1-MgjB8I06TZkmGrZPkjArwV4RHKA=
  dependencies:
    jsonparse "^1.2.0"
    through ">=2.2.7 <3"

abbrev@1:
  version "1.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/abbrev/download/abbrev-1.1.1.tgz#f8f2c887ad10bf67f634f005b6987fed3179aac8"
  integrity sha1-+PLIh60Qv2f2NPAFtph/7TF5qsg=

abort-controller@~3.0.0:
  version "3.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/abort-controller/download/abort-controller-3.0.0.tgz#eaf54d53b62bae4138e809ca225c8439a6efb392"
  integrity sha1-6vVNU7YrrkE46AnKIlyEOabvs5I=
  dependencies:
    event-target-shim "^5.0.0"

accepts@^1.3.5:
  version "1.3.8"
  resolved "http://npm.devops.xiaohongshu.com:7001/accepts/download/accepts-1.3.8.tgz#0bf0be125b67014adcb0b0921e62db7bffe16b2e"
  integrity sha1-C/C+EltnAUrcsLCSHmLbe//hay4=
  dependencies:
    mime-types "~2.1.34"
    negotiator "0.6.3"

accounting-js@^1.1.1:
  version "1.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/accounting-js/download/accounting-js-1.1.1.tgz#7fe4b3f70c01ebe0b85c02c5f107f1393b880c9e"
  integrity sha1-f+Sz9wwB6+C4XALF8QfxOTuIDJ4=
  dependencies:
    is-string "^1.0.4"
    object-assign "^4.0.1"

adler-32@~1.3.0:
  version "1.3.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/adler-32/download/adler-32-1.3.1.tgz#1dbf0b36dda0012189a32b3679061932df1821e2"
  integrity sha1-Hb8LNt2gASGJoys2eQYZMt8YIeI=

agent-base@4, agent-base@^4.3.0:
  version "4.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/agent-base/download/agent-base-4.3.0.tgz#8165f01c436009bccad0b1d122f05ed770efc6ee"
  integrity sha1-gWXwHENgCbzK0LHRIvBe13Dvxu4=
  dependencies:
    es6-promisify "^5.0.0"

agent-base@~4.2.1:
  version "4.2.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/agent-base/download/agent-base-4.2.1.tgz#d89e5999f797875674c07d87f260fc41e83e8ca9"
  integrity sha1-2J5ZmfeXh1Z0wH2H8mD8Qeg+jKk=
  dependencies:
    es6-promisify "^5.0.0"

agentkeepalive@^3.4.1:
  version "3.5.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/agentkeepalive/download/agentkeepalive-3.5.2.tgz#a113924dd3fa24a0bc3b78108c450c2abee00f67"
  integrity sha1-oROSTdP6JKC8O3gQjEUMKr7gD2c=
  dependencies:
    humanize-ms "^1.2.1"

ajv@^6.12.3:
  version "6.12.6"
  resolved "http://npm.devops.xiaohongshu.com:7001/ajv/download/ajv-6.12.6.tgz#baf5a62e802b07d977034586f8c3baf5adf26df4"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@~8.12.0:
  version "8.12.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ajv/-/ajv-8.12.0.tgz#d1a0527323e22f53562c567c00991577dfbe19d1"
  integrity sha512-sRu1kpcO9yLtYxBKvqfTeh9KzZEwO3STyX1HT+4CaDzC6HpTGYhIhPIzj9XuKU7KYDwnaeh5hcOwjy1QuJzBPA==
  dependencies:
    fast-deep-equal "^3.1.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"
    uri-js "^4.2.2"

ansi-escapes@^3.2.0:
  version "3.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/ansi-escapes/download/ansi-escapes-3.2.0.tgz#8780b98ff9dbf5638152d1f1fe5c1d7b4442976b"
  integrity sha1-h4C5j/nb9WOBUtHx/lwde0RCl2s=

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/ansi-regex/download/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df"
  integrity sha1-w7M6te42DYbg5ijwRorn7yfWVN8=

ansi-regex@^3.0.0:
  version "3.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/ansi-regex/download/ansi-regex-3.0.1.tgz#123d6479e92ad45ad897d4054e3c7ca7db4944e1"
  integrity sha1-Ej1keekq1FrYl9QFTjx8p9tJROE=

ansi-regex@^4.1.0:
  version "4.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/ansi-regex/download/ansi-regex-4.1.1.tgz#164daac87ab2d6f6db3a29875e2d1766582dabed"
  integrity sha1-Fk2qyHqy1vbbOimHXi0XZlgtq+0=

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/ansi-regex/download/ansi-regex-5.0.1.tgz#082cb2c89c9fe8659a311a53bd6a4dc5301db304"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-styles@^3.2.0, ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/ansi-styles/download/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

any-promise@^1.0.0:
  version "1.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/any-promise/download/any-promise-1.3.0.tgz#abc6afeedcea52e809cdc0376aed3ce39635d17f"
  integrity sha1-q8av7tzqUugJzcA3au0845Y10X8=

aproba@^1.0.3, aproba@^1.1.1:
  version "1.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/aproba/download/aproba-1.2.0.tgz#6802e6264efd18c790a1b0d517f0f2627bf2c94a"
  integrity sha1-aALmJk79GMeQobDVF/DyYnvyyUo=

aproba@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/aproba/download/aproba-2.0.0.tgz#52520b8ae5b569215b354efc0caa3fe1e45a8adc"
  integrity sha1-UlILiuW1aSFbNU78DKo/4eRaitw=

are-we-there-yet@~1.1.2:
  version "1.1.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/are-we-there-yet/download/are-we-there-yet-1.1.7.tgz#b15474a932adab4ff8a50d9adfa7e4e926f21146"
  integrity sha1-sVR0qTKtq0/4pQ2a36fk6SbyEUY=
  dependencies:
    delegates "^1.0.0"
    readable-stream "^2.0.6"

argparse@^1.0.7:
  version "1.0.10"
  resolved "http://npm.devops.xiaohongshu.com:7001/argparse/download/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

argparse@^2.0.1:
  version "2.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/argparse/download/argparse-2.0.1.tgz#246f50f3ca78a3240f6c997e8a9bd1eac49e4b38"
  integrity sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=

arr-diff@^4.0.0:
  version "4.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/arr-diff/download/arr-diff-4.0.0.tgz#d6461074febfec71e7e15235761a329a5dc7c520"
  integrity sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=

arr-flatten@^1.1.0:
  version "1.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/arr-flatten/download/arr-flatten-1.1.0.tgz#36048bbff4e7b47e136644316c99669ea5ae91f1"
  integrity sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=

arr-union@^3.1.0:
  version "3.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/arr-union/download/arr-union-3.1.0.tgz#e39b09aea9def866a8f206e288af63919bae39c4"
  integrity sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=

array-differ@^2.0.3:
  version "2.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/array-differ/download/array-differ-2.1.0.tgz#4b9c1c3f14b906757082925769e8ab904f4801b1"
  integrity sha1-S5wcPxS5BnVwgpJXaeirkE9IAbE=

array-find-index@^1.0.1:
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/array-find-index/download/array-find-index-1.0.2.tgz#df010aa1287e164bbda6f9723b0a96a1ec4187a1"
  integrity sha1-3wEKoSh+Fku9pvlyOwqWoexBh6E=

array-ify@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/array-ify/download/array-ify-1.0.0.tgz#9e528762b4a9066ad163a6962a364418e9626ece"
  integrity sha1-nlKHYrSpBmrRY6aWKjZEGOlibs4=

array-union@^1.0.2:
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/array-union/download/array-union-1.0.2.tgz#9a34410e4f4e3da23dea375be5be70f24778ec39"
  integrity sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=
  dependencies:
    array-uniq "^1.0.1"

array-uniq@^1.0.1:
  version "1.0.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/array-uniq/download/array-uniq-1.0.3.tgz#af6ac877a25cc7f74e058894753858dfdb24fdb6"
  integrity sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=

array-unique@^0.3.2:
  version "0.3.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/array-unique/download/array-unique-0.3.2.tgz#a894b75d4bc4f6cd679ef3244a9fd8f46ae2d428"
  integrity sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=

array.prototype.reduce@^1.0.4:
  version "1.0.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/array.prototype.reduce/download/array.prototype.reduce-1.0.4.tgz#8167e80089f78bff70a99e20bd4201d4663b0a6f"
  integrity sha1-gWfoAIn3i/9wqZ4gvUIB1GY7Cm8=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.2"
    es-array-method-boxes-properly "^1.0.0"
    is-string "^1.0.7"

arrify@^1.0.1:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/arrify/download/arrify-1.0.1.tgz#898508da2226f380df904728456849c1501a4b0d"
  integrity sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=

asap@^2.0.0:
  version "2.0.6"
  resolved "http://npm.devops.xiaohongshu.com:7001/asap/download/asap-2.0.6.tgz#e50347611d7e690943208bbdafebcbc2fb866d46"
  integrity sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=

asn1@~0.2.3:
  version "0.2.6"
  resolved "http://npm.devops.xiaohongshu.com:7001/asn1/download/asn1-0.2.6.tgz#0d3a7bb6e64e02a90c0303b31f292868ea09a08d"
  integrity sha1-DTp7tuZOAqkMAwOzHykoaOoJoI0=
  dependencies:
    safer-buffer "~2.1.0"

assert-plus@1.0.0, assert-plus@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/assert-plus/download/assert-plus-1.0.0.tgz#f12e0f3c5d77b0b1cdd9146942e4e96c1e4dd525"
  integrity sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=

assign-symbols@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/assign-symbols/download/assign-symbols-1.0.0.tgz#59667f41fadd4f20ccbc2bb96b8d4f7f78ec0367"
  integrity sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=

async-limiter@~1.0.0:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/async-limiter/download/async-limiter-1.0.1.tgz#dd379e94f0db8310b08291f9d64c3209766617fd"
  integrity sha1-3TeelPDbgxCwgpH51kwyCXZmF/0=

async-validator@^4.2.5:
  version "4.2.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/async-validator/download/async-validator-4.2.5.tgz#c96ea3332a521699d0afaaceed510a54656c6339"
  integrity sha1-yW6jMypSFpnQr6rO7VEKVGVsYzk=

asynckit@^0.4.0:
  version "0.4.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/asynckit/download/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

atob-lite@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/atob-lite/download/atob-lite-2.0.0.tgz#0fef5ad46f1bd7a8502c65727f0367d5ee43d696"
  integrity sha1-D+9a1G8b16hQLGVyfwNn1e5D1pY=

atob@^2.1.2:
  version "2.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/atob/download/atob-2.1.2.tgz#6d9517eb9e030d2436666651e86bd9f6f13533c9"
  integrity sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=

attr-accept@^2.2.2:
  version "2.2.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/attr-accept/download/attr-accept-2.2.2.tgz#646613809660110749e92f2c10833b70968d929b"
  integrity sha1-ZGYTgJZgEQdJ6S8sEIM7cJaNkps=

aws-sign2@~0.7.0:
  version "0.7.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/aws-sign2/download/aws-sign2-0.7.0.tgz#b46e890934a9591f2d2f6f86d7e6a9f1b3fe76a8"
  integrity sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=

aws4@^1.8.0:
  version "1.11.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/aws4/download/aws4-1.11.0.tgz#d61f46d83b2519250e2784daf5b09479a8b41c59"
  integrity sha1-1h9G2DslGSUOJ4Ta9bCUeai0HFk=

axios@^0.19.2:
  version "0.19.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/axios/download/axios-0.19.2.tgz#3ea36c5d8818d0d5f8a8a97a6d36b86cdc00cb27"
  integrity sha1-PqNsXYgY0NX4qKl6bTa4bNwAyyc=
  dependencies:
    follow-redirects "1.5.10"

axios@^1.1.3:
  version "1.3.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/axios/download/axios-1.3.3.tgz#e7011384ba839b885007c9c9fae1ff23dceb295b"
  integrity sha1-5wEThLqDm4hQB8nJ+uH/I9zrKVs=
  dependencies:
    follow-redirects "^1.15.0"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/balanced-match/download/balanced-match-1.0.2.tgz#e83e3a7e3f300b34cb9d87f615fa0cbf357690ee"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

base@^0.11.1:
  version "0.11.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/base/download/base-0.11.2.tgz#7bde5ced145b6d551a90db87f83c558b4eb48a8f"
  integrity sha1-e95c7RRbbVUakNuH+DxVi060io8=
  dependencies:
    cache-base "^1.0.1"
    class-utils "^0.3.5"
    component-emitter "^1.2.1"
    define-property "^1.0.0"
    isobject "^3.0.1"
    mixin-deep "^1.2.0"
    pascalcase "^0.1.1"

bcrypt-pbkdf@^1.0.0:
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz#a4301d389b6a43f9b67ff3ca11a3f6637e360e9e"
  integrity sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=
  dependencies:
    tweetnacl "^0.14.3"

before-after-hook@^2.0.0:
  version "2.2.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/before-after-hook/download/before-after-hook-2.2.2.tgz#a6e8ca41028d90ee2c24222f201c90956091613e"
  integrity sha1-pujKQQKNkO4sJCIvIByQlWCRYT4=

big.js@^6.2.1:
  version "6.2.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/big.js/download/big.js-6.2.1.tgz#7205ce763efb17c2e41f26f121c420c6a7c2744f"
  integrity sha1-cgXOdj77F8LkHybxIcQgxqfCdE8=

bintrees@1.0.2:
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/bintrees/download/bintrees-1.0.2.tgz#49f896d6e858a4a499df85c38fb399b9aff840f8"
  integrity sha1-SfiW1uhYpKSZ34XDj7OZua/4QPg=

bluebird@^3.5.1, bluebird@^3.5.3, bluebird@^3.5.5:
  version "3.7.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/bluebird/download/bluebird-3.7.2.tgz#9f229c15be272454ffa973ace0dbee79a1b0c36f"
  integrity sha1-nyKcFb4nJFT/qXOs4NvueaGww28=

body-scroll-lock@^3.0.3:
  version "3.1.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/body-scroll-lock/download/body-scroll-lock-3.1.5.tgz#c1392d9217ed2c3e237fee1e910f6cdd80b7aaec"
  integrity sha1-wTktkhftLD4jf+4ekQ9s3YC3quw=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "http://npm.devops.xiaohongshu.com:7001/brace-expansion/download/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^2.3.1:
  version "2.3.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/braces/download/braces-2.3.2.tgz#5979fd3f14cd531565e5fa2df1abfff1dfaee729"
  integrity sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=
  dependencies:
    arr-flatten "^1.1.0"
    array-unique "^0.3.2"
    extend-shallow "^2.0.1"
    fill-range "^4.0.0"
    isobject "^3.0.1"
    repeat-element "^1.1.2"
    snapdragon "^0.8.1"
    snapdragon-node "^2.0.1"
    split-string "^3.0.2"
    to-regex "^3.0.1"

browser-or-node@^1.2.1:
  version "1.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/browser-or-node/download/browser-or-node-1.3.0.tgz#f2a4e8568f60263050a6714b2cc236bb976647a7"
  integrity sha1-8qToVo9gJjBQpnFLLMI2u5dmR6c=

btoa-lite@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/btoa-lite/download/btoa-lite-1.0.0.tgz#337766da15801210fdd956c22e9c6891ab9d0337"
  integrity sha1-M3dm2hWAEhD92VbCLpxokaudAzc=

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/buffer-from/download/buffer-from-1.1.2.tgz#2b146a6fd72e80b4f55d255f35ed59a3a9a41bd5"
  integrity sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=

builtins@^1.0.3:
  version "1.0.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/builtins/download/builtins-1.0.3.tgz#cb94faeb61c8696451db36534e1422f94f0aee88"
  integrity sha1-y5T662HIaWRR2zZTThQi+U8K7og=

byline@^5.0.0:
  version "5.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/byline/download/byline-5.0.0.tgz#741c5216468eadc457b03410118ad77de8c1ddb1"
  integrity sha1-dBxSFkaOrcRXsDQQEYrXfejB3bE=

byte-size@^5.0.1:
  version "5.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/byte-size/download/byte-size-5.0.1.tgz#4b651039a5ecd96767e71a3d7ed380e48bed4191"
  integrity sha1-S2UQOaXs2Wdn5xo9ftOA5IvtQZE=

bytes@3.1.2:
  version "3.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/bytes/download/bytes-3.1.2.tgz#8b0beeb98605adf1b128fa4386403c009e0221a5"
  integrity sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU=

cacache@^12.0.0, cacache@^12.0.3:
  version "12.0.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/cacache/download/cacache-12.0.4.tgz#668bcbd105aeb5f1d92fe25570ec9525c8faa40c"
  integrity sha1-ZovL0QWutfHZL+JVcOyVJcj6pAw=
  dependencies:
    bluebird "^3.5.5"
    chownr "^1.1.1"
    figgy-pudding "^3.5.1"
    glob "^7.1.4"
    graceful-fs "^4.1.15"
    infer-owner "^1.0.3"
    lru-cache "^5.1.1"
    mississippi "^3.0.0"
    mkdirp "^0.5.1"
    move-concurrently "^1.0.1"
    promise-inflight "^1.0.1"
    rimraf "^2.6.3"
    ssri "^6.0.1"
    unique-filename "^1.1.1"
    y18n "^4.0.0"

cache-base@^1.0.1:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/cache-base/download/cache-base-1.0.1.tgz#0a7f46416831c8b662ee36fe4e7c59d76f666ab2"
  integrity sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=
  dependencies:
    collection-visit "^1.0.0"
    component-emitter "^1.2.1"
    get-value "^2.0.6"
    has-value "^1.0.0"
    isobject "^3.0.1"
    set-value "^2.0.0"
    to-object-path "^0.3.0"
    union-value "^1.0.0"
    unset-value "^1.0.0"

cache-content-type@^1.0.0:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/cache-content-type/download/cache-content-type-1.0.1.tgz#035cde2b08ee2129f4a8315ea8f00a00dba1453c"
  integrity sha1-A1zeKwjuISn0qDFeqPAKANuhRTw=
  dependencies:
    mime-types "^2.1.18"
    ylru "^1.2.0"

call-bind@^1.0.0, call-bind@^1.0.2:
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/call-bind/download/call-bind-1.0.2.tgz#b1d4e89e688119c3c9a903ad30abb2f6a919be3c"
  integrity sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=
  dependencies:
    function-bind "^1.1.1"
    get-intrinsic "^1.0.2"

call-me-maybe@^1.0.1:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/call-me-maybe/download/call-me-maybe-1.0.1.tgz#26d208ea89e37b5cbde60250a15f031c16a4d66b"
  integrity sha1-JtII6onje1y95gJQoV8DHBak1ms=

caller-callsite@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/caller-callsite/download/caller-callsite-2.0.0.tgz#847e0fce0a223750a9a027c54b33731ad3154134"
  integrity sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ=
  dependencies:
    callsites "^2.0.0"

caller-path@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/caller-path/download/caller-path-2.0.0.tgz#468f83044e369ab2010fac5f06ceee15bb2cb1f4"
  integrity sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ=
  dependencies:
    caller-callsite "^2.0.0"

callsites@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/callsites/download/callsites-2.0.0.tgz#06eb84f00eea413da86affefacbffb36093b3c50"
  integrity sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA=

camel-case@^4.1.1:
  version "4.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/camel-case/download/camel-case-4.1.2.tgz#9728072a954f805228225a6deea6b38461e1bd5a"
  integrity sha1-lygHKpVPgFIoIlpt7qazhGHhvVo=
  dependencies:
    pascal-case "^3.1.2"
    tslib "^2.0.3"

camelcase-keys@^2.0.0:
  version "2.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/camelcase-keys/download/camelcase-keys-2.1.0.tgz#308beeaffdf28119051efa1d932213c91b8f92e7"
  integrity sha1-MIvur/3ygRkFHvodkyITyRuPkuc=
  dependencies:
    camelcase "^2.0.0"
    map-obj "^1.0.0"

camelcase-keys@^4.0.0:
  version "4.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/camelcase-keys/download/camelcase-keys-4.2.0.tgz#a2aa5fb1af688758259c32c141426d78923b9b77"
  integrity sha1-oqpfsa9oh1glnDLBQUJteJI7m3c=
  dependencies:
    camelcase "^4.1.0"
    map-obj "^2.0.0"
    quick-lru "^1.0.0"

camelcase-keys@^6.2.2:
  version "6.2.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/camelcase-keys/download/camelcase-keys-6.2.2.tgz#5e755d6ba51aa223ec7d3d52f25778210f9dc3c0"
  integrity sha1-XnVda6UaoiPsfT1S8ld4IQ+dw8A=
  dependencies:
    camelcase "^5.3.1"
    map-obj "^4.0.0"
    quick-lru "^4.0.1"

camelcase@^2.0.0:
  version "2.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/camelcase/download/camelcase-2.1.1.tgz#7c1d16d679a1bbe59ca02cacecfb011e201f5a1f"
  integrity sha1-fB0W1nmhu+WcoCys7PsBHiAfWh8=

camelcase@^4.1.0:
  version "4.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/camelcase/download/camelcase-4.1.0.tgz#d545635be1e33c542649c69173e5de6acfae34dd"
  integrity sha1-1UVjW+HjPFQmScaRc+Xeas+uNN0=

camelcase@^5.0.0, camelcase@^5.3.1:
  version "5.3.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/camelcase/download/camelcase-5.3.1.tgz#e3c9b31569e106811df242f715725a1f4c494320"
  integrity sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=

caseless@~0.12.0:
  version "0.12.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/caseless/download/caseless-0.12.0.tgz#1b681c21ff84033c826543090689420d187151dc"
  integrity sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=

cfb@~1.2.1:
  version "1.2.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/cfb/download/cfb-1.2.2.tgz#94e687628c700e5155436dac05f74e08df23bc44"
  integrity sha1-lOaHYoxwDlFVQ22sBfdOCN8jvEQ=
  dependencies:
    adler-32 "~1.3.0"
    crc-32 "~1.2.0"

chalk@^2.0.0, chalk@^2.3.1, chalk@^2.4.2:
  version "2.4.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/chalk/download/chalk-2.4.2.tgz#cd42541677a54333cf541a49108c1432b44c9424"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chardet@^0.7.0:
  version "0.7.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/chardet/download/chardet-0.7.0.tgz#90094849f0937f2eedc2425d0d28a9e5f0cbad9e"
  integrity sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=

charenc@0.0.2:
  version "0.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/charenc/download/charenc-0.0.2.tgz#c0a1d2f3a7092e03774bfa83f14c0fc5790a8667"
  integrity sha1-wKHS86cJLgN3S/qD8UwPxXkKhmc=

chownr@^1.1.1, chownr@^1.1.2, chownr@^1.1.4:
  version "1.1.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/chownr/download/chownr-1.1.4.tgz#6fc9d7b42d32a583596337666e7d08084da2cc6b"
  integrity sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs=

ci-info@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/ci-info/download/ci-info-2.0.0.tgz#67a9e964be31a51e15e5010d58e6f12834002f46"
  integrity sha1-Z6npZL4xpR4V5QENWObxKDQAL0Y=

class-utils@^0.3.5:
  version "0.3.6"
  resolved "http://npm.devops.xiaohongshu.com:7001/class-utils/download/class-utils-0.3.6.tgz#f93369ae8b9a7ce02fd41faad0ca83033190c463"
  integrity sha1-+TNprouafOAv1B+q0MqDAzGQxGM=
  dependencies:
    arr-union "^3.1.0"
    define-property "^0.2.5"
    isobject "^3.0.0"
    static-extend "^0.1.1"

classnames@^2.2:
  version "2.3.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/classnames/download/classnames-2.3.2.tgz#351d813bf0137fcc6a76a16b88208d2560a0d924"
  integrity sha1-NR2BO/ATf8xqdqFriCCNJWCg2SQ=

cli-cursor@^2.1.0:
  version "2.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/cli-cursor/download/cli-cursor-2.1.0.tgz#b35dac376479facc3e94747d41d0d0f5238ffcb5"
  integrity sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=
  dependencies:
    restore-cursor "^2.0.0"

cli-width@^2.0.0:
  version "2.2.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/cli-width/download/cli-width-2.2.1.tgz#b0433d0b4e9c847ef18868a4ef16fd5fc8271c48"
  integrity sha1-sEM9C06chH7xiGik7xb9X8gnHEg=

cliui@^5.0.0:
  version "5.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/cliui/download/cliui-5.0.0.tgz#deefcfdb2e800784aa34f46fa08e06851c7bbbc5"
  integrity sha1-3u/P2y6AB4SqNPRvoI4GhRx7u8U=
  dependencies:
    string-width "^3.1.0"
    strip-ansi "^5.2.0"
    wrap-ansi "^5.1.0"

clone-deep@^4.0.1:
  version "4.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/clone-deep/download/clone-deep-4.0.1.tgz#c19fd9bdbbf85942b4fd979c84dcf7d5f07c2387"
  integrity sha1-wZ/Zvbv4WUK0/ZechNz31fB8I4c=
  dependencies:
    is-plain-object "^2.0.4"
    kind-of "^6.0.2"
    shallow-clone "^3.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/clone/download/clone-1.0.4.tgz#da309cc263df15994c688ca902179ca3c7cd7c7e"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

co-body@^6.0.0:
  version "6.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/co-body/download/co-body-6.1.0.tgz#d87a8efc3564f9bfe3aced8ef5cd04c7a8766547"
  integrity sha1-2HqO/DVk+b/jrO2O9c0Ex6h2ZUc=
  dependencies:
    inflation "^2.0.0"
    qs "^6.5.2"
    raw-body "^2.3.3"
    type-is "^1.6.16"

co@^4.6.0:
  version "4.6.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/co/download/co-4.6.0.tgz#6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184"
  integrity sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=

code-point-at@^1.0.0:
  version "1.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/code-point-at/download/code-point-at-1.1.0.tgz#0d070b4d043a5bea33a2f1a40e2edb3d9a4ccf77"
  integrity sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=

codepage@~1.15.0:
  version "1.15.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/codepage/download/codepage-1.15.0.tgz#2e00519024b39424ec66eeb3ec07227e692618ab"
  integrity sha1-LgBRkCSzlCTsZu6z7AcifmkmGKs=

collection-visit@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/collection-visit/download/collection-visit-1.0.0.tgz#4bc0373c164bc3291b4d368c829cf1a80a59dca0"
  integrity sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=
  dependencies:
    map-visit "^1.0.0"
    object-visit "^1.0.0"

color-convert@^1.9.0, color-convert@^1.9.3:
  version "1.9.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/color-convert/-/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
  integrity sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==
  dependencies:
    color-name "1.1.3"

color-name@1.1.3:
  version "1.1.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/color-name/download/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@^1.0.0:
  version "1.1.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/color-name/-/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==

color-string@^1.6.0:
  version "1.9.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/color-string/-/color-string-1.9.1.tgz#4467f9146f036f855b764dfb5bf8582bf342c7a4"
  integrity sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

color@^3.1.3:
  version "3.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/color/-/color-3.2.1.tgz#3544dc198caf4490c3ecc9a790b54fe9ff45e164"
  integrity sha512-aBl7dZI9ENN6fUGC7mWpMTPNHmWUSNan9tuWN6ahh5ZLNk9baLJOnSMlrQkHcrfFgz2/RigjUVAjdx36VcemKA==
  dependencies:
    color-convert "^1.9.3"
    color-string "^1.6.0"

columnify@^1.5.4:
  version "1.6.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/columnify/download/columnify-1.6.0.tgz#6989531713c9008bb29735e61e37acf5bd553cf3"
  integrity sha1-aYlTFxPJAIuylzXmHjes9b1VPPM=
  dependencies:
    strip-ansi "^6.0.1"
    wcwidth "^1.0.0"

combined-stream@^1.0.6, combined-stream@^1.0.8, combined-stream@~1.0.6:
  version "1.0.8"
  resolved "http://npm.devops.xiaohongshu.com:7001/combined-stream/download/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

compare-func@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/compare-func/download/compare-func-2.0.0.tgz#fb65e75edbddfd2e568554e8b5b05fff7a51fcb3"
  integrity sha1-+2XnXtvd/S5WhVTotbBf/3pR/LM=
  dependencies:
    array-ify "^1.0.0"
    dot-prop "^5.1.0"

component-emitter@^1.2.1:
  version "1.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/component-emitter/download/component-emitter-1.3.0.tgz#16e4070fba8ae29b679f2215853ee181ab2eabc0"
  integrity sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A=

compute-scroll-into-view@^1.0.20:
  version "1.0.20"
  resolved "http://npm.devops.xiaohongshu.com:7001/compute-scroll-into-view/download/compute-scroll-into-view-1.0.20.tgz#1768b5522d1172754f5d0c9b02de3af6be506a43"
  integrity sha1-F2i1Ui0RcnVPXQybAt469r5QakM=

concat-map@0.0.1:
  version "0.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/concat-map/download/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

concat-stream@^1.5.0:
  version "1.6.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/concat-stream/download/concat-stream-1.6.2.tgz#904bdf194cd3122fc675c77fc4ac3d4ff0fd1a34"
  integrity sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

concat-stream@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/concat-stream/download/concat-stream-2.0.0.tgz#414cf5af790a48c60ab9be4527d56d5e41133cb1"
  integrity sha1-QUz1r3kKSMYKub5FJ9VtXkETPLE=
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^3.0.2"
    typedarray "^0.0.6"

config-chain@^1.1.11:
  version "1.1.13"
  resolved "http://npm.devops.xiaohongshu.com:7001/config-chain/download/config-chain-1.1.13.tgz#fad0795aa6a6cdaff9ed1b68e9dff94372c232f4"
  integrity sha1-+tB5Wqamza/57Rto6d/5Q3LCMvQ=
  dependencies:
    ini "^1.3.4"
    proto-list "~1.2.1"

console-control-strings@^1.0.0, console-control-strings@~1.1.0:
  version "1.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/console-control-strings/download/console-control-strings-1.1.0.tgz#3d7cf4464db6446ea644bf4b39507f9851008e8e"
  integrity sha1-PXz0Rk22RG6mRL9LOVB/mFEAjo4=

content-disposition@~0.5.2:
  version "0.5.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/content-disposition/download/content-disposition-0.5.4.tgz#8b82b4efac82512a02bb0b1dcec9d2c5e8eb5bfe"
  integrity sha1-i4K076yCUSoCuwsdzsnSxejrW/4=
  dependencies:
    safe-buffer "5.2.1"

content-type@^1.0.4:
  version "1.0.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/content-type/download/content-type-1.0.4.tgz#e138cc75e040c727b1966fe5e5f8c9aee256fe3b"
  integrity sha1-4TjMdeBAxyexlm/l5fjJruJW/js=

conventional-changelog-angular@^5.0.3:
  version "5.0.13"
  resolved "http://npm.devops.xiaohongshu.com:7001/conventional-changelog-angular/download/conventional-changelog-angular-5.0.13.tgz#896885d63b914a70d4934b59d2fe7bde1832b28c"
  integrity sha1-iWiF1juRSnDUk0tZ0v573hgysow=
  dependencies:
    compare-func "^2.0.0"
    q "^1.5.1"

conventional-changelog-core@^3.1.6:
  version "3.2.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/conventional-changelog-core/download/conventional-changelog-core-3.2.3.tgz#b31410856f431c847086a7dcb4d2ca184a7d88fb"
  integrity sha1-sxQQhW9DHIRwhqfctNLKGEp9iPs=
  dependencies:
    conventional-changelog-writer "^4.0.6"
    conventional-commits-parser "^3.0.3"
    dateformat "^3.0.0"
    get-pkg-repo "^1.0.0"
    git-raw-commits "2.0.0"
    git-remote-origin-url "^2.0.0"
    git-semver-tags "^2.0.3"
    lodash "^4.2.1"
    normalize-package-data "^2.3.5"
    q "^1.5.1"
    read-pkg "^3.0.0"
    read-pkg-up "^3.0.0"
    through2 "^3.0.0"

conventional-changelog-preset-loader@^2.1.1:
  version "2.3.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/conventional-changelog-preset-loader/download/conventional-changelog-preset-loader-2.3.4.tgz#14a855abbffd59027fd602581f1f34d9862ea44c"
  integrity sha1-FKhVq7/9WQJ/1gJYHx802YYupEw=

conventional-changelog-writer@^4.0.6:
  version "4.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/conventional-changelog-writer/download/conventional-changelog-writer-4.1.0.tgz#1ca7880b75aa28695ad33312a1f2366f4b12659f"
  integrity sha1-HKeIC3WqKGla0zMSofI2b0sSZZ8=
  dependencies:
    compare-func "^2.0.0"
    conventional-commits-filter "^2.0.7"
    dateformat "^3.0.0"
    handlebars "^4.7.6"
    json-stringify-safe "^5.0.1"
    lodash "^4.17.15"
    meow "^8.0.0"
    semver "^6.0.0"
    split "^1.0.0"
    through2 "^4.0.0"

conventional-commits-filter@^2.0.2, conventional-commits-filter@^2.0.7:
  version "2.0.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/conventional-commits-filter/download/conventional-commits-filter-2.0.7.tgz#f8d9b4f182fce00c9af7139da49365b136c8a0b3"
  integrity sha1-+Nm08YL84Aya9xOdpJNlsTbIoLM=
  dependencies:
    lodash.ismatch "^4.4.0"
    modify-values "^1.0.0"

conventional-commits-parser@^3.0.3:
  version "3.2.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/conventional-commits-parser/download/conventional-commits-parser-3.2.4.tgz#a7d3b77758a202a9b2293d2112a8d8052c740972"
  integrity sha1-p9O3d1iiAqmyKT0hEqjYBSx0CXI=
  dependencies:
    JSONStream "^1.0.4"
    is-text-path "^1.0.1"
    lodash "^4.17.15"
    meow "^8.0.0"
    split2 "^3.0.0"
    through2 "^4.0.0"

conventional-recommended-bump@^5.0.0:
  version "5.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/conventional-recommended-bump/download/conventional-recommended-bump-5.0.1.tgz#5af63903947b6e089e77767601cb592cabb106ba"
  integrity sha1-WvY5A5R7bgied3Z2ActZLKuxBro=
  dependencies:
    concat-stream "^2.0.0"
    conventional-changelog-preset-loader "^2.1.1"
    conventional-commits-filter "^2.0.2"
    conventional-commits-parser "^3.0.3"
    git-raw-commits "2.0.0"
    git-semver-tags "^2.0.3"
    meow "^4.0.0"
    q "^1.5.1"

cookies@~0.8.0:
  version "0.8.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/cookies/download/cookies-0.8.0.tgz#1293ce4b391740a8406e3c9870e828c4b54f3f90"
  integrity sha1-EpPOSzkXQKhAbjyYcOgoxLVPP5A=
  dependencies:
    depd "~2.0.0"
    keygrip "~1.1.0"

copy-concurrently@^1.0.0:
  version "1.0.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/copy-concurrently/download/copy-concurrently-1.0.5.tgz#92297398cae34937fcafd6ec8139c18051f0b5e0"
  integrity sha1-kilzmMrjSTf8r9bsgTnBgFHwteA=
  dependencies:
    aproba "^1.1.1"
    fs-write-stream-atomic "^1.0.8"
    iferr "^0.1.5"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"
    run-queue "^1.0.0"

copy-descriptor@^0.1.0:
  version "0.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/copy-descriptor/download/copy-descriptor-0.1.1.tgz#676f6eb3c39997c2ee1ac3a924fd6124748f578d"
  integrity sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=

copy-to-clipboard@^3.3.3:
  version "3.3.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/copy-to-clipboard/download/copy-to-clipboard-3.3.3.tgz#55ac43a1db8ae639a4bd99511c148cdd1b83a1b0"
  integrity sha1-VaxDoduK5jmkvZlRHBSM3RuDobA=
  dependencies:
    toggle-selection "^1.0.6"

copy-to@^2.0.1:
  version "2.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/copy-to/download/copy-to-2.0.1.tgz#2680fbb8068a48d08656b6098092bdafc906f4a5"
  integrity sha1-JoD7uAaKSNCGVrYJgJK9r8kG9KU=

core-js@^2.6.12:
  version "2.6.12"
  resolved "http://npm.devops.xiaohongshu.com:7001/core-js/download/core-js-2.6.12.tgz#d9333dfa7b065e347cc5682219d6f690859cc2ec"
  integrity sha1-2TM9+nsGXjR8xWgiGdb2kIWcwuw=

core-util-is@1.0.2:
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/core-util-is/download/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"
  integrity sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/core-util-is/download/core-util-is-1.0.3.tgz#a6042d3634c2b27e9328f837b965fac83808db85"
  integrity sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=

cos-js-sdk-v5@^1.3.8:
  version "1.3.9"
  resolved "http://npm.devops.xiaohongshu.com:7001/cos-js-sdk-v5/download/cos-js-sdk-v5-1.3.9.tgz#9716722ca0ad55003c03d8187bebe902fcb5cff7"
  integrity sha1-lxZyLKCtVQA8A9gYe+vpAvy1z/c=
  dependencies:
    "@xmldom/xmldom" "^0.8.2"

cosmiconfig@^5.1.0:
  version "5.2.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/cosmiconfig/download/cosmiconfig-5.2.1.tgz#040f726809c591e77a17c0a3626ca45b4f168b1a"
  integrity sha1-BA9yaAnFked6F8CjYmykW08Wixo=
  dependencies:
    import-fresh "^2.0.0"
    is-directory "^0.3.1"
    js-yaml "^3.13.1"
    parse-json "^4.0.0"

crc-32@~1.2.0, crc-32@~1.2.1:
  version "1.2.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/crc-32/download/crc-32-1.2.2.tgz#3cad35a934b8bf71f25ca524b6da51fb7eace2ff"
  integrity sha1-PK01qTS4v3HyXKUkttpR+36s4v8=

cross-spawn@^6.0.0:
  version "6.0.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/cross-spawn/download/cross-spawn-6.0.5.tgz#4a5ec7c64dfae22c3a14124dbacdee846d80cbc4"
  integrity sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

crypt@0.0.2:
  version "0.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/crypt/download/crypt-0.0.2.tgz#88d7ff7ec0dfb86f713dc87bbb42d044d3e6c41b"
  integrity sha1-iNf/fsDfuG9xPch7u0LQRNPmxBs=

csstype@^2.5.7, csstype@^2.6.8:
  version "2.6.21"
  resolved "http://npm.devops.xiaohongshu.com:7001/csstype/download/csstype-2.6.21.tgz#2efb85b7cc55c80017c66a5ad7cbd931fda3a90e"
  integrity sha1-LvuFt8xVyAAXxmpa18vZMf2jqQ4=

csstype@^3.0.5, csstype@^3.0.8:
  version "3.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/csstype/-/csstype-3.1.2.tgz#1d4bf9d572f11c14031f0436e1c10bc1f571f50b"
  integrity sha512-I7K1Uu0MBPzaFKg4nI5Q7Vs2t+3gWWW648spaF+Rg7pI9ds18Ugn+lvg4SHczUdKlHI5LWBXyqfS8+DufyBsgQ==

currently-unhandled@^0.4.1:
  version "0.4.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/currently-unhandled/download/currently-unhandled-0.4.1.tgz#988df33feab191ef799a61369dd76c17adf957ea"
  integrity sha1-mI3zP+qxke95mmE2nddsF635V+o=
  dependencies:
    array-find-index "^1.0.1"

cyclist@^1.0.1:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/cyclist/download/cyclist-1.0.1.tgz#596e9698fd0c80e12038c2b82d6eb1b35b6224d9"
  integrity sha1-WW6WmP0MgOEgOMK4LW6xs1tiJNk=

"d3-color@1 - 3":
  version "3.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/d3-color/-/d3-color-3.1.0.tgz#395b2833dfac71507f12ac2f7af23bf819de24e2"
  integrity sha512-zg/chbXyeBtMQ1LbD/WSoW2DpC3I0mpmPdW+ynRTj/x2DAWYrIY7qeZIHidozwV24m4iavr15lNwIwLxRmOxhA==

"d3-dispatch@1 - 2":
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/d3-dispatch/-/d3-dispatch-2.0.0.tgz#8a18e16f76dd3fcaef42163c97b926aa9b55e7cf"
  integrity sha512-S/m2VsXI7gAti2pBoLClFFTMOO1HTtT0j99AuXLoGFKO6deHDdnv6ZGTxSTTUTgO1zVcv82fCOtDjYK4EECmWA==

d3-ease@^1.0.5:
  version "1.0.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/d3-ease/-/d3-ease-1.0.7.tgz#9a834890ef8b8ae8c558b2fe55bd57f5993b85e2"
  integrity sha512-lx14ZPYkhNx0s/2HX5sLFUI3mbasHjSSpwO/KaaNACweVwxUruKyWVcb293wMv1RqTPZyZ8kSZ2NogUZNcLOFQ==

d3-force@^2.0.1, d3-force@^2.1.1:
  version "2.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/d3-force/-/d3-force-2.1.1.tgz#f20ccbf1e6c9e80add1926f09b51f686a8bc0937"
  integrity sha512-nAuHEzBqMvpFVMf9OX75d00OxvOXdxY+xECIXjW6Gv8BRrXu6gAWbv/9XKrvfJ5i5DCokDW7RYE50LRoK092ew==
  dependencies:
    d3-dispatch "1 - 2"
    d3-quadtree "1 - 2"
    d3-timer "1 - 2"

d3-interpolate@^3.0.1:
  version "3.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/d3-interpolate/-/d3-interpolate-3.0.1.tgz#3c47aa5b32c5b3dfb56ef3fd4342078a632b400d"
  integrity sha512-3bYs1rOD33uo8aqJfKP3JWPAibgw8Zm2+L9vBKEHJ2Rg+viTR7o5Mmv5mZcieN+FRYaAOWX5SJATX6k1PWz72g==
  dependencies:
    d3-color "1 - 3"

"d3-quadtree@1 - 2", d3-quadtree@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/d3-quadtree/-/d3-quadtree-2.0.0.tgz#edbad045cef88701f6fee3aee8e93fb332d30f9d"
  integrity sha512-b0Ed2t1UUalJpc3qXzKi+cPGxeXRr4KU9YSlocN74aTzp6R/Ud43t79yLLqxHRWZfsvWXmbDWPpoENK1K539xw==

"d3-timer@1 - 2":
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/d3-timer/-/d3-timer-2.0.0.tgz#055edb1d170cfe31ab2da8968deee940b56623e6"
  integrity sha512-TO4VLh0/420Y/9dO3+f9abDEFYeCUr2WZRlxJvbp4HPTQcSylXNiL6yZa9FIUvV1yRiFufl1bszTCLDqv9PWNA==

d3-timer@^1.0.9:
  version "1.0.10"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/d3-timer/-/d3-timer-1.0.10.tgz#dfe76b8a91748831b13b6d9c793ffbd508dd9de5"
  integrity sha512-B1JDm0XDaQC+uvo4DT79H0XmBskgS3l6Ve+1SBCfxgmtIb1AVrPIoqd+nPSv+loMX8szQ0sVUhGngL7D5QPiXw==

d@1, d@^1.0.1:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/d/download/d-1.0.1.tgz#8698095372d58dbee346ffd0c7093f99f8f9eb5a"
  integrity sha1-hpgJU3LVjb7jRv/Qxwk/mfj561o=
  dependencies:
    es5-ext "^0.10.50"
    type "^1.0.1"

dagre-compound@^0.0.11:
  version "0.0.11"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/dagre-compound/-/dagre-compound-0.0.11.tgz#8d3d1004d756f420582d29f28c92045375018987"
  integrity sha512-UrSgRP9LtOZCYb9e5doolZXpc7xayyszgyOs7uakTK4n4KsLegLVTRRtq01GpQd/iZjYw5fWMapx9ed+c80MAQ==

dagre@^0.8.5:
  version "0.8.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/dagre/-/dagre-0.8.5.tgz#ba30b0055dac12b6c1fcc247817442777d06afee"
  integrity sha512-/aTqmnRta7x7MCCpExk7HQL2O4owCT2h8NT//9I1OQ9vt29Pa0BzSAkR5lwFUcQ7491yVi/3CXU9jQ5o0Mn2Sw==
  dependencies:
    graphlib "^2.1.8"
    lodash "^4.17.15"

dargs@^4.0.1:
  version "4.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/dargs/download/dargs-4.1.0.tgz#03a9dbb4b5c2f139bf14ae53f0b8a2a6a86f4e17"
  integrity sha1-A6nbtLXC8Tm/FK5T8LiipqhvThc=
  dependencies:
    number-is-nan "^1.0.0"

dashdash@^1.12.0:
  version "1.14.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/dashdash/download/dashdash-1.14.1.tgz#853cfa0f7cbe2fed5de20326b8dd581035f6e2f0"
  integrity sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=
  dependencies:
    assert-plus "^1.0.0"

dateformat@^3.0.0:
  version "3.0.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/dateformat/download/dateformat-3.0.3.tgz#a6e37499a4d9a9cf85ef5872044d62901c9889ae"
  integrity sha1-puN0maTZqc+F71hyBE1ikByYia4=

dayjs@^1.10.7, dayjs@^1.8.18:
  version "1.11.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/dayjs/download/dayjs-1.11.7.tgz#4b296922642f70999544d1144a2c25730fce63e2"
  integrity sha1-SylpImQvcJmVRNEUSiwlcw/OY+I=

dayjs@^1.11.0, dayjs@^1.11.7:
  version "1.11.10"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/dayjs/-/dayjs-1.11.10.tgz#68acea85317a6e164457d6d6947564029a6a16a0"
  integrity sha512-vjAczensTgRcqDERK0SR2XMwsF/tSvnvlv6VcF2GIhg6Sx4yOIt/irsr1RDJsKiIyBzJDpCoXiWWq28MqH2cnQ==

dayjs@^1.11.5:
  version "1.11.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/dayjs/download/dayjs-1.11.5.tgz#00e8cc627f231f9499c19b38af49f56dc0ac5e93"
  integrity sha1-AOjMYn8jH5SZwZs4r0n1bcCsXpM=

dayjs@^1.11.9:
  version "1.11.9"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/dayjs/download/dayjs-1.11.9.tgz#9ca491933fadd0a60a2c19f6c237c03517d71d1a"
  integrity sha1-nKSRkz+t0KYKLBn2wjfANRfXHRo=

debug@3.1.0, debug@=3.1.0:
  version "3.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/debug/download/debug-3.1.0.tgz#5bb5a0672628b64149566ba16819e61518c67261"
  integrity sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE=
  dependencies:
    ms "2.0.0"

debug@^2.2.0, debug@^2.3.3:
  version "2.6.9"
  resolved "http://npm.devops.xiaohongshu.com:7001/debug/download/debug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@^3.1.0:
  version "3.2.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/debug/download/debug-3.2.7.tgz#72580b7e9145fb39b6676f9c5e5fb100b934179a"
  integrity sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=
  dependencies:
    ms "^2.1.1"

debug@^4.1.1, debug@^4.3.2:
  version "4.3.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/debug/download/debug-4.3.4.tgz#1319f6579357f2338d3337d2cdd4914bb5dcc865"
  integrity sha1-Exn2V5NX8jONMzfSzdSRS7XcyGU=
  dependencies:
    ms "2.1.2"

debuglog@^1.0.1:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/debuglog/download/debuglog-1.0.1.tgz#aa24ffb9ac3df9a2351837cfb2d279360cd78492"
  integrity sha1-qiT/uaw9+aI1GDfPstJ5NgzXhJI=

decamelize-keys@^1.0.0, decamelize-keys@^1.1.0:
  version "1.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/decamelize-keys/download/decamelize-keys-1.1.0.tgz#d171a87933252807eb3cb61dc1c1445d078df2d9"
  integrity sha1-0XGoeTMlKAfrPLYdwcFEXQeN8tk=
  dependencies:
    decamelize "^1.1.0"
    map-obj "^1.0.0"

decamelize@^1.1.0, decamelize@^1.1.2, decamelize@^1.2.0:
  version "1.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/decamelize/download/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

decimal.js@^10.4.3:
  version "10.4.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/decimal.js/download/decimal.js-10.4.3.tgz#1044092884d245d1b7f65725fa4ad4c6f781cc23"
  integrity sha1-EEQJKITSRdG39lcl+krUxveBzCM=

decode-uri-component@^0.2.0:
  version "0.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/decode-uri-component/download/decode-uri-component-0.2.0.tgz#eb3913333458775cb84cd1a1fae062106bb87545"
  integrity sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=

dedent@^0.7.0:
  version "0.7.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/dedent/download/dedent-0.7.0.tgz#2495ddbaf6eb874abb0e1be9df22d2e5a544326c"
  integrity sha1-JJXduvbrh0q7Dhvp3yLS5aVEMmw=

deep-equal@~1.0.1:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/deep-equal/download/deep-equal-1.0.1.tgz#f5d260292b660e084eff4cdbc9f08ad3247448b5"
  integrity sha1-9dJgKStmDghO/0zbyfCK0yR0SLU=

deepmerge@^4.2.2:
  version "4.2.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/deepmerge/download/deepmerge-4.2.2.tgz#44d2ea3679b8f4d4ffba33f03d865fc1e7bf4955"
  integrity sha1-RNLqNnm49NT/ujPwPYZfwee/SVU=

defaults@^1.0.3:
  version "1.0.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/defaults/download/defaults-1.0.3.tgz#c656051e9817d9ff08ed881477f3fe4019f3ef7d"
  integrity sha1-xlYFHpgX2f8I7YgUd/P+QBnz730=
  dependencies:
    clone "^1.0.2"

define-properties@^1.1.3, define-properties@^1.1.4:
  version "1.1.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/define-properties/download/define-properties-1.1.4.tgz#0b14d7bd7fbeb2f3572c3a7eda80ea5d57fb05b1"
  integrity sha1-CxTXvX++svNXLDp+2oDqXVf7BbE=
  dependencies:
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

define-property@^0.2.5:
  version "0.2.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/define-property/download/define-property-0.2.5.tgz#c35b1ef918ec3c990f9a5bc57be04aacec5c8116"
  integrity sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=
  dependencies:
    is-descriptor "^0.1.0"

define-property@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/define-property/download/define-property-1.0.0.tgz#769ebaaf3f4a63aad3af9e8d304c9bbe79bfb0e6"
  integrity sha1-dp66rz9KY6rTr56NMEybvnm/sOY=
  dependencies:
    is-descriptor "^1.0.0"

define-property@^2.0.2:
  version "2.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/define-property/download/define-property-2.0.2.tgz#d459689e8d654ba77e02a817f8710d702cb16e9d"
  integrity sha1-1Flono1lS6d+AqgX+HENcCyxbp0=
  dependencies:
    is-descriptor "^1.0.2"
    isobject "^3.0.1"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/delayed-stream/download/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

delegates@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/delegates/download/delegates-1.0.0.tgz#84c6e159b81904fdca59a0ef44cd870d31250f9a"
  integrity sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=

depd@2.0.0, depd@^2.0.0, depd@~2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/depd/download/depd-2.0.0.tgz#b696163cc757560d09cf22cc8fad1571b79e76df"
  integrity sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=

depd@~1.1.2:
  version "1.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/depd/download/depd-1.1.2.tgz#9bcd52e14c097763e749b274c4346ed2e560b5a9"
  integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=

deprecation@^2.0.0, deprecation@^2.3.1:
  version "2.3.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/deprecation/download/deprecation-2.3.1.tgz#6368cbdb40abf3373b525ac87e4a260c3a700919"
  integrity sha1-Y2jL20Cr8zc7UlrIfkomDDpwCRk=

destroy@^1.0.4:
  version "1.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/destroy/download/destroy-1.2.0.tgz#4803735509ad8be552934c67df614f94e66fa015"
  integrity sha1-SANzVQmti+VSk0xn32FPlOZvoBU=

detect-browser@^5.0.0, detect-browser@^5.1.0:
  version "5.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/detect-browser/-/detect-browser-5.3.0.tgz#9705ef2bddf46072d0f7265a1fe300e36fe7ceca"
  integrity sha512-53rsFbGdwMwlF7qvCt0ypLM5V5/Mbl0szB7GPN8y9NCcbknYOeVVXdrXEq+90IwAfrrzt6Hd+u2E2ntakICU8w==

detect-indent@^5.0.0:
  version "5.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/detect-indent/download/detect-indent-5.0.0.tgz#3871cc0a6a002e8c3e5b3cf7f336264675f06b9d"
  integrity sha1-OHHMCmoALow+Wzz38zYmRnXwa50=

dezalgo@^1.0.0:
  version "1.0.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/dezalgo/download/dezalgo-1.0.4.tgz#751235260469084c132157dfa857f386d4c33d81"
  integrity sha1-dRI1JgRpCEwTIVffqFfzhtTDPYE=
  dependencies:
    asap "^2.0.0"
    wrappy "1"

dir-glob@^2.2.2:
  version "2.2.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/dir-glob/download/dir-glob-2.2.2.tgz#fa09f0694153c8918b18ba0deafae94769fc50c4"
  integrity sha1-+gnwaUFTyJGLGLoN6vrpR2n8UMQ=
  dependencies:
    path-type "^3.0.0"

dom-zindex@^1.0.1:
  version "1.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/dom-zindex/-/dom-zindex-1.0.1.tgz#98df1d916f390f009bb0ec03b96df820e7c7073a"
  integrity sha512-M/MERVDZ8hguvjl6MAlLWSLYLS7PzEyXaTb5gEeJ+SF+e9iUC0sdvlzqe91MMDHBoy+nqw7wKcUOrDSyvMCrRg==

dom7@^3.0.0:
  version "3.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/dom7/download/dom7-3.0.0.tgz#b861ce5d67a6becd7aaa3ad02942ff14b1240331"
  integrity sha1-uGHOXWemvs16qjrQKUL/FLEkAzE=
  dependencies:
    ssr-window "^3.0.0-alpha.1"

dompurify@^3.2.6:
  version "3.2.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/dompurify/-/dompurify-3.2.6.tgz#ca040a6ad2b88e2a92dc45f38c79f84a714a1cad"
  integrity sha512-/2GogDQlohXPZe6D6NOgQvXLPSYBqIWMnZ8zzOhn09REE4eyAzb+Hed3jhoM9OkuaJ8P6ZGTTVWQKAi8ieIzfQ==
  optionalDependencies:
    "@types/trusted-types" "^2.0.7"

dot-case@^3.0.4:
  version "3.0.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/dot-case/download/dot-case-3.0.4.tgz#9b2b670d00a431667a8a75ba29cd1b98809ce751"
  integrity sha1-mytnDQCkMWZ6inW6Kc0bmICc51E=
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

dot-prop@^4.2.0:
  version "4.2.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/dot-prop/download/dot-prop-4.2.1.tgz#45884194a71fc2cda71cbb4bceb3a4dd2f433ba4"
  integrity sha1-RYhBlKcfws2nHLtLzrOk3S9DO6Q=
  dependencies:
    is-obj "^1.0.0"

dot-prop@^5.1.0:
  version "5.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/dot-prop/download/dot-prop-5.3.0.tgz#90ccce708cd9cd82cc4dc8c3ddd9abdd55b20e88"
  integrity sha1-kMzOcIzZzYLMTcjD3dmr3VWyDog=
  dependencies:
    is-obj "^2.0.0"

duplexer@^0.1.1:
  version "0.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/duplexer/download/duplexer-0.1.2.tgz#3abe43aef3835f8ae077d136ddce0f276b0400e6"
  integrity sha1-Or5DrvODX4rgd9E23c4PJ2sEAOY=

duplexify@^3.4.2, duplexify@^3.6.0:
  version "3.7.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/duplexify/download/duplexify-3.7.1.tgz#2a4df5317f6ccfd91f86d6fd25d8d8a103b88309"
  integrity sha1-Kk31MX9sz9kfhtb9JdjYoQO4gwk=
  dependencies:
    end-of-stream "^1.0.0"
    inherits "^2.0.1"
    readable-stream "^2.0.0"
    stream-shift "^1.0.0"

ecc-jsbn@~0.1.1:
  version "0.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz#3a83a904e54353287874c564b7549386849a98c9"
  integrity sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=
  dependencies:
    jsbn "~0.1.0"
    safer-buffer "^2.1.0"

echarts@5.2.1:
  version "5.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/echarts/download/echarts-5.2.1.tgz#bd58ec011cd82def4a714e4038ef4b73b8417bc3"
  integrity sha1-vVjsARzYLe9KcU5AOO9Lc7hBe8M=
  dependencies:
    tslib "2.3.0"
    zrender "5.2.1"

echarts@^5.2.2:
  version "5.4.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/echarts/-/echarts-5.4.3.tgz#f5522ef24419164903eedcfd2b506c6fc91fb20c"
  integrity sha512-mYKxLxhzy6zyTi/FaEbJMOZU1ULGEQHaeIeuMR5L+JnJTpz+YR03mnnpBhbR4+UYJAgiXgpyTVLffPAjOTLkZA==
  dependencies:
    tslib "2.3.0"
    zrender "5.4.4"

ee-first@1.1.1:
  version "1.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/ee-first/download/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

emoji-regex@^7.0.1:
  version "7.0.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/emoji-regex/download/emoji-regex-7.0.3.tgz#933a04052860c85e83c122479c4748a8e4c72156"
  integrity sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/emoji-regex/download/emoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

encodeurl@^1.0.2:
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/encodeurl/download/encodeurl-1.0.2.tgz#ad3ff4c86ec2d029322f5a02c3a9a606c95b3f59"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

encoding@^0.1.11:
  version "0.1.13"
  resolved "http://npm.devops.xiaohongshu.com:7001/encoding/download/encoding-0.1.13.tgz#56574afdd791f54a8e9b2785c0582a2d26210fa9"
  integrity sha1-VldK/deR9UqOmyeFwFgqLSYhD6k=
  dependencies:
    iconv-lite "^0.6.2"

end-of-stream@^1.0.0, end-of-stream@^1.1.0:
  version "1.4.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/end-of-stream/download/end-of-stream-1.4.4.tgz#5ae64a5f45057baf3626ec14da0ca5e4b2431eb0"
  integrity sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=
  dependencies:
    once "^1.4.0"

entities@^4.4.0:
  version "4.5.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/entities/download/entities-4.5.0.tgz#5d268ea5e7113ec74c4d033b79ea5a35a488fb48"
  integrity sha1-XSaOpecRPsdMTQM7eepaNaSI+0g=

env-paths@^2.2.0:
  version "2.2.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/env-paths/download/env-paths-2.2.1.tgz#420399d416ce1fbe9bc0a07c62fa68d67fd0f8f2"
  integrity sha1-QgOZ1BbOH76bwKB8Yvpo1n/Q+PI=

envinfo@^7.3.1:
  version "7.8.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/envinfo/download/envinfo-7.8.1.tgz#06377e3e5f4d379fea7ac592d5ad8927e0c4d475"
  integrity sha1-Bjd+Pl9NN5/qesWS1a2JJ+DE1HU=

err-code@^1.0.0:
  version "1.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/err-code/download/err-code-1.1.2.tgz#06e0116d3028f6aef4806849eb0ea6a748ae6960"
  integrity sha1-BuARbTAo9q70gGhJ6w6mp0iuaWA=

error-ex@^1.2.0, error-ex@^1.3.1:
  version "1.3.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/error-ex/download/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

es-abstract@^1.19.0, es-abstract@^1.19.2, es-abstract@^1.19.5, es-abstract@^1.20.1:
  version "1.20.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/es-abstract/download/es-abstract-1.20.1.tgz#027292cd6ef44bd12b1913b828116f54787d1814"
  integrity sha1-AnKSzW70S9ErGRO4KBFvVHh9GBQ=
  dependencies:
    call-bind "^1.0.2"
    es-to-primitive "^1.2.1"
    function-bind "^1.1.1"
    function.prototype.name "^1.1.5"
    get-intrinsic "^1.1.1"
    get-symbol-description "^1.0.0"
    has "^1.0.3"
    has-property-descriptors "^1.0.0"
    has-symbols "^1.0.3"
    internal-slot "^1.0.3"
    is-callable "^1.2.4"
    is-negative-zero "^2.0.2"
    is-regex "^1.1.4"
    is-shared-array-buffer "^1.0.2"
    is-string "^1.0.7"
    is-weakref "^1.0.2"
    object-inspect "^1.12.0"
    object-keys "^1.1.1"
    object.assign "^4.1.2"
    regexp.prototype.flags "^1.4.3"
    string.prototype.trimend "^1.0.5"
    string.prototype.trimstart "^1.0.5"
    unbox-primitive "^1.0.2"

es-array-method-boxes-properly@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/es-array-method-boxes-properly/download/es-array-method-boxes-properly-1.0.0.tgz#873f3e84418de4ee19c5be752990b2e44718d09e"
  integrity sha1-hz8+hEGN5O4Zxb51KZCy5EcY0J4=

es-to-primitive@^1.2.1:
  version "1.2.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/es-to-primitive/download/es-to-primitive-1.2.1.tgz#e55cd4c9cdc188bcefb03b366c736323fc5c898a"
  integrity sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo=
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

es5-ext@^0.10.35, es5-ext@^0.10.50, es5-ext@~0.10.14:
  version "0.10.62"
  resolved "http://npm.devops.xiaohongshu.com:7001/es5-ext/download/es5-ext-0.10.62.tgz#5e6adc19a6da524bf3d1e02bbc8960e5eb49a9a5"
  integrity sha1-XmrcGabaUkvz0eArvIlg5etJqaU=
  dependencies:
    es6-iterator "^2.0.3"
    es6-symbol "^3.1.3"
    next-tick "^1.1.0"

es6-iterator@^2.0.3:
  version "2.0.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/es6-iterator/download/es6-iterator-2.0.3.tgz#a7de889141a05a94b0854403b2d0a0fbfa98f3b7"
  integrity sha1-p96IkUGgWpSwhUQDstCg+/qY87c=
  dependencies:
    d "1"
    es5-ext "^0.10.35"
    es6-symbol "^3.1.1"

es6-promise@^4.0.3:
  version "4.2.8"
  resolved "http://npm.devops.xiaohongshu.com:7001/es6-promise/download/es6-promise-4.2.8.tgz#4eb21594c972bc40553d276e510539143db53e0a"
  integrity sha1-TrIVlMlyvEBVPSduUQU5FD21Pgo=

es6-promisify@^5.0.0:
  version "5.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/es6-promisify/download/es6-promisify-5.0.0.tgz#5109d62f3e56ea967c4b63505aef08291c8a5203"
  integrity sha1-UQnWLz5W6pZ8S2NQWu8IKRyKUgM=
  dependencies:
    es6-promise "^4.0.3"

es6-symbol@^3.1.1, es6-symbol@^3.1.3:
  version "3.1.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/es6-symbol/download/es6-symbol-3.1.3.tgz#bad5d3c1bcdac28269f4cb331e431c78ac705d18"
  integrity sha1-utXTwbzawoJp9MszHkMceKxwXRg=
  dependencies:
    d "^1.0.1"
    ext "^1.1.2"

escape-html@^1.0.3:
  version "1.0.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/escape-html/download/escape-html-1.0.3.tgz#0258eae4d3d0c0974de1c169188ef0051d1d1988"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

esprima@^4.0.0:
  version "4.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/esprima/download/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

estree-walker@^2.0.2:
  version "2.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/estree-walker/download/estree-walker-2.0.2.tgz#52f010178c2a4c117a7757cfe942adb7d2da4cac"
  integrity sha1-UvAQF4wqTBF6d1fP6UKtt9LaTKw=

event-emitter@^0.3.5:
  version "0.3.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/event-emitter/download/event-emitter-0.3.5.tgz#df8c69eef1647923c7157b9ce83840610b02cc39"
  integrity sha1-34xp7vFkeSPHFXuc6DhAYQsCzDk=
  dependencies:
    d "1"
    es5-ext "~0.10.14"

event-target-shim@^5.0.0:
  version "5.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/event-target-shim/download/event-target-shim-5.0.1.tgz#5d4d3ebdf9583d63a5333ce2deb7480ab2b05789"
  integrity sha1-XU0+vflYPWOlMzzi3rdICrKwV4k=

eventemitter3@^3.1.0:
  version "3.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/eventemitter3/download/eventemitter3-3.1.2.tgz#2d3d48f9c346698fce83a85d7d664e98535df6e7"
  integrity sha1-LT1I+cNGaY/Og6hdfWZOmFNd9uc=

eventemitter3@^4.0.0:
  version "4.0.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/eventemitter3/-/eventemitter3-4.0.7.tgz#2de9b68f6528d5644ef5c59526a1b4a07306169f"
  integrity sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==

execa@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/execa/download/execa-1.0.0.tgz#c6236a5bb4df6d6f15e88e7f017798216749ddd8"
  integrity sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=
  dependencies:
    cross-spawn "^6.0.0"
    get-stream "^4.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

expand-brackets@^2.1.4:
  version "2.1.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/expand-brackets/download/expand-brackets-2.1.4.tgz#b77735e315ce30f6b6eff0f83b04151a22449622"
  integrity sha1-t3c14xXOMPa27/D4OwQVGiJEliI=
  dependencies:
    debug "^2.3.3"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    posix-character-classes "^0.1.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

ext@^1.1.2:
  version "1.7.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/ext/download/ext-1.7.0.tgz#0ea4383c0103d60e70be99e9a7f11027a33c4f5f"
  integrity sha1-DqQ4PAED1g5wvpnpp/EQJ6M8T18=
  dependencies:
    type "^2.7.2"

extend-shallow@^2.0.1:
  version "2.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/extend-shallow/download/extend-shallow-2.0.1.tgz#51af7d614ad9a9f610ea1bafbb989d6b1c56890f"
  integrity sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=
  dependencies:
    is-extendable "^0.1.0"

extend-shallow@^3.0.0, extend-shallow@^3.0.2:
  version "3.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/extend-shallow/download/extend-shallow-3.0.2.tgz#26a71aaf073b39fb2127172746131c2704028db8"
  integrity sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

extend@~3.0.2:
  version "3.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/extend/download/extend-3.0.2.tgz#f8b1136b4071fbd8eb140aff858b1019ec2915fa"
  integrity sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=

external-editor@^3.0.3:
  version "3.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/external-editor/download/external-editor-3.1.0.tgz#cb03f740befae03ea4d283caed2741a83f335495"
  integrity sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

extglob@^2.0.4:
  version "2.0.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/extglob/download/extglob-2.0.4.tgz#ad00fe4dc612a9232e8718711dc5cb5ab0285543"
  integrity sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=
  dependencies:
    array-unique "^0.3.2"
    define-property "^1.0.0"
    expand-brackets "^2.1.4"
    extend-shallow "^2.0.1"
    fragment-cache "^0.2.1"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

extsprintf@1.3.0:
  version "1.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/extsprintf/download/extsprintf-1.3.0.tgz#96918440e3041a7a414f8c52e3c574eb3c3e1e05"
  integrity sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=

extsprintf@^1.2.0:
  version "1.4.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/extsprintf/download/extsprintf-1.4.1.tgz#8d172c064867f235c0c84a596806d279bf4bcc07"
  integrity sha1-jRcsBkhn8jXAyEpZaAbSeb9LzAc=

fast-decode-uri-component@^1.0.1:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/fast-decode-uri-component/download/fast-decode-uri-component-1.0.1.tgz#46f8b6c22b30ff7a81357d4f59abfae938202543"
  integrity sha1-Rvi2wisw/3qBNX1PWav66TggJUM=

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz#3a7d56b559d6cbc3eb512325244e619a65c6c525"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-glob@^2.2.6:
  version "2.2.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/fast-glob/download/fast-glob-2.2.7.tgz#6953857c3afa475fff92ee6015d52da70a4cd39d"
  integrity sha1-aVOFfDr6R1//ku5gFdUtpwpM050=
  dependencies:
    "@mrmlnc/readdir-enhanced" "^2.2.1"
    "@nodelib/fs.stat" "^1.1.2"
    glob-parent "^3.1.0"
    is-glob "^4.0.0"
    merge2 "^1.2.3"
    micromatch "^3.1.10"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-querystring@^1.0.0:
  version "1.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/fast-querystring/download/fast-querystring-1.1.1.tgz#f4c56ef56b1a954880cfd8c01b83f9e1a3d3fda2"
  integrity sha1-9MVu9WsalUiAz9jAG4P54aPT/aI=
  dependencies:
    fast-decode-uri-component "^1.0.1"

fecha@~4.2.0:
  version "4.2.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/fecha/-/fecha-4.2.3.tgz#4d9ccdbc61e8629b259fdca67e65891448d569fd"
  integrity sha512-OP2IUU6HeYKJi3i0z4A19kHMQoLVs4Hc+DPqqxI2h/DPZHTm/vjsfC6P0b4jCMy14XizLBqvndQ+UilD7707Jw==

figgy-pudding@^3.4.1, figgy-pudding@^3.5.1:
  version "3.5.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/figgy-pudding/download/figgy-pudding-3.5.2.tgz#b4eee8148abb01dcf1d1ac34367d59e12fa61d6e"
  integrity sha1-tO7oFIq7Adzx0aw0Nn1Z4S+mHW4=

figures@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/figures/download/figures-2.0.0.tgz#3ab1a2d2a62c8bfb431a0c94cb797a2fce27c962"
  integrity sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=
  dependencies:
    escape-string-regexp "^1.0.5"

file-selector@^0.2.4:
  version "0.2.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/file-selector/download/file-selector-0.2.4.tgz#7b98286f9dbb9925f420130ea5ed0a69238d4d80"
  integrity sha1-e5gob527mSX0IBMOpe0KaSONTYA=
  dependencies:
    tslib "^2.0.3"

fill-range@^4.0.0:
  version "4.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/fill-range/download/fill-range-4.0.0.tgz#d544811d428f98eb06a63dc402d2403c328c38f7"
  integrity sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=
  dependencies:
    extend-shallow "^2.0.1"
    is-number "^3.0.0"
    repeat-string "^1.6.1"
    to-regex-range "^2.1.0"

filter-obj@^1.1.0:
  version "1.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/filter-obj/download/filter-obj-1.1.0.tgz#9b311112bc6c6127a16e016c6c5d7f19e0805c5b"
  integrity sha1-mzERErxsYSehbgFsbF1/GeCAXFs=

find-my-way@~7.6.0:
  version "7.6.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/find-my-way/download/find-my-way-7.6.2.tgz#4dd40200d3536aeef5c7342b10028e04cf79146c"
  integrity sha1-TdQCANNTau71xzQrEAKOBM95FGw=
  dependencies:
    fast-deep-equal "^3.1.3"
    fast-querystring "^1.0.0"
    safe-regex2 "^2.0.0"

find-up@^1.0.0:
  version "1.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/find-up/download/find-up-1.1.2.tgz#6b2e9822b1a2ce0a60ab64d610eccad53cb24d0f"
  integrity sha1-ay6YIrGizgpgq2TWEOzK1TyyTQ8=
  dependencies:
    path-exists "^2.0.0"
    pinkie-promise "^2.0.0"

find-up@^2.0.0:
  version "2.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/find-up/download/find-up-2.1.0.tgz#45d1b7e506c717ddd482775a2b77920a3c0c57a7"
  integrity sha1-RdG35QbHF93UgndaK3eSCjwMV6c=
  dependencies:
    locate-path "^2.0.0"

find-up@^3.0.0:
  version "3.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/find-up/download/find-up-3.0.0.tgz#49169f1d7993430646da61ecc5ae355c21c97b73"
  integrity sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=
  dependencies:
    locate-path "^3.0.0"

find-up@^4.1.0:
  version "4.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/find-up/download/find-up-4.1.0.tgz#97afe7d6cdc0bc5928584b7c8d7b16e8a9aa5d19"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

fluent-json-schema@~3.0.1:
  version "3.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/fluent-json-schema/download/fluent-json-schema-3.0.1.tgz#d81e16d2d0f3832ea08cc5d185b5a4ededd8a6a6"
  integrity sha1-2B4W0tDzgy6gjMXRhbWk7e3YpqY=
  dependencies:
    deepmerge "^4.2.2"

flush-write-stream@^1.0.0:
  version "1.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/flush-write-stream/download/flush-write-stream-1.1.1.tgz#8dd7d873a1babc207d94ead0c2e0e44276ebf2e8"
  integrity sha1-jdfYc6G6vCB9lOrQwuDkQnbr8ug=
  dependencies:
    inherits "^2.0.3"
    readable-stream "^2.3.6"

fn-name@~2.0.1:
  version "2.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/fn-name/download/fn-name-2.0.1.tgz#5214d7537a4d06a4a301c0cc262feb84188002e7"
  integrity sha1-UhTXU3pNBqSjAcDMJi/rhBiAAuc=

follow-redirects@1.5.10:
  version "1.5.10"
  resolved "http://npm.devops.xiaohongshu.com:7001/follow-redirects/download/follow-redirects-1.5.10.tgz#7b7a9f9aea2fdff36786a94ff643ed07f4ff5e2a"
  integrity sha1-e3qfmuov3/NnhqlP9kPtB/T/Xio=
  dependencies:
    debug "=3.1.0"

follow-redirects@^1.15.0:
  version "1.15.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/follow-redirects/download/follow-redirects-1.15.2.tgz#b460864144ba63f2681096f274c4e57026da2c13"
  integrity sha1-tGCGQUS6Y/JoEJbydMTlcCbaLBM=

for-in@^1.0.2:
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/for-in/download/for-in-1.0.2.tgz#81068d295a8142ec0ac726c6e2200c30fb6d5e80"
  integrity sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=

forever-agent@~0.6.1:
  version "0.6.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/forever-agent/download/forever-agent-0.6.1.tgz#fbc71f0c41adeb37f96c577ad1ed42d8fdacca91"
  integrity sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=

form-data@^3.0.0:
  version "3.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/form-data/download/form-data-3.0.1.tgz#ebd53791b78356a99af9a300d4282c4d5eb9755f"
  integrity sha1-69U3kbeDVqma+aMA1CgsTV65dV8=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

form-data@^4.0.0:
  version "4.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/form-data/download/form-data-4.0.0.tgz#93919daeaf361ee529584b9b31664dc12c9fa452"
  integrity sha1-k5Gdrq82HuUpWEubMWZNwSyfpFI=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

form-data@~2.3.2:
  version "2.3.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/form-data/download/form-data-2.3.3.tgz#dcce52c05f644f298c6a7ab936bd724ceffbf3a6"
  integrity sha1-3M5SwF9kTymManq5Nr1yTO/786Y=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

frac@~1.1.2:
  version "1.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/frac/download/frac-1.1.2.tgz#3d74f7f6478c88a1b5020306d747dc6313c74d0b"
  integrity sha1-PXT39keMiKG1AgMG10fcYxPHTQs=

fragment-cache@^0.2.1:
  version "0.2.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/fragment-cache/download/fragment-cache-0.2.1.tgz#4290fad27f13e89be7f33799c6bc5a0abfff0d19"
  integrity sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=
  dependencies:
    map-cache "^0.2.2"

fresh@~0.5.2:
  version "0.5.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/fresh/download/fresh-0.5.2.tgz#3d8cadd90d976569fa835ab1f8e4b23a105605a7"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

from2@^2.1.0:
  version "2.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/from2/download/from2-2.3.0.tgz#8bfb5502bde4a4d36cfdeea007fcca21d7e382af"
  integrity sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=
  dependencies:
    inherits "^2.0.1"
    readable-stream "^2.0.0"

fs-extra@^10.0.0:
  version "10.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/fs-extra/download/fs-extra-10.1.0.tgz#02873cfbc4084dde127eaa5f9905eef2325d1abf"
  integrity sha1-Aoc8+8QITd4SfqpfmQXu8jJdGr8=
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-extra@^8.1.0:
  version "8.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/fs-extra/download/fs-extra-8.1.0.tgz#49d43c45a88cd9677668cb7be1b46efdb8d2e1c0"
  integrity sha1-SdQ8RaiM2Wd2aMt74bRu/bjS4cA=
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs-minipass@^1.2.7:
  version "1.2.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/fs-minipass/download/fs-minipass-1.2.7.tgz#ccff8570841e7fe4265693da88936c55aed7f7c7"
  integrity sha1-zP+FcIQef+QmVpPaiJNsVa7X98c=
  dependencies:
    minipass "^2.6.0"

fs-write-stream-atomic@^1.0.8:
  version "1.0.10"
  resolved "http://npm.devops.xiaohongshu.com:7001/fs-write-stream-atomic/download/fs-write-stream-atomic-1.0.10.tgz#b47df53493ef911df75731e70a9ded0189db40c9"
  integrity sha1-tH31NJPvkR33VzHnCp3tAYnbQMk=
  dependencies:
    graceful-fs "^4.1.2"
    iferr "^0.1.5"
    imurmurhash "^0.1.4"
    readable-stream "1 || 2"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/fs.realpath/download/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

function-bind@^1.1.1:
  version "1.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/function-bind/download/function-bind-1.1.1.tgz#a56899d3ea3c9bab874bb9773b7c5ede92f4895d"
  integrity sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=

function.prototype.name@^1.1.5:
  version "1.1.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/function.prototype.name/download/function.prototype.name-1.1.5.tgz#cce0505fe1ffb80503e6f9e46cc64e46a12a9621"
  integrity sha1-zOBQX+H/uAUD5vnkbMZORqEqliE=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.0"
    functions-have-names "^1.2.2"

functions-have-names@^1.2.2:
  version "1.2.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/functions-have-names/download/functions-have-names-1.2.3.tgz#0404fe4ee2ba2f607f0e0ec3c80bae994133b834"
  integrity sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ=

gauge@~2.7.3:
  version "2.7.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/gauge/download/gauge-2.7.4.tgz#2c03405c7538c39d7eb37b317022e325fb018bf7"
  integrity sha1-LANAXHU4w51+s3sxcCLjJfsBi/c=
  dependencies:
    aproba "^1.0.3"
    console-control-strings "^1.0.0"
    has-unicode "^2.0.0"
    object-assign "^4.1.0"
    signal-exit "^3.0.0"
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wide-align "^1.1.0"

genfun@^5.0.0:
  version "5.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/genfun/download/genfun-5.0.0.tgz#9dd9710a06900a5c4a5bf57aca5da4e52fe76537"
  integrity sha1-ndlxCgaQClxKW/V6yl2k5S/nZTc=

get-caller-file@^2.0.1:
  version "2.0.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/get-caller-file/download/get-caller-file-2.0.5.tgz#4f94412a82db32f36e3b0b9741f8a97feb031f7e"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-intrinsic@^1.0.2, get-intrinsic@^1.1.0, get-intrinsic@^1.1.1:
  version "1.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/get-intrinsic/download/get-intrinsic-1.1.2.tgz#336975123e05ad0b7ba41f152ee4aadbea6cf598"
  integrity sha1-M2l1Ej4FrQt7pB8VLuSq2+ps9Zg=
  dependencies:
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.3"

get-pkg-repo@^1.0.0:
  version "1.4.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/get-pkg-repo/download/get-pkg-repo-1.4.0.tgz#c73b489c06d80cc5536c2c853f9e05232056972d"
  integrity sha1-xztInAbYDMVTbCyFP54FIyBWly0=
  dependencies:
    hosted-git-info "^2.1.4"
    meow "^3.3.0"
    normalize-package-data "^2.3.0"
    parse-github-repo-url "^1.3.0"
    through2 "^2.0.0"

get-port@^4.2.0:
  version "4.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/get-port/download/get-port-4.2.0.tgz#e37368b1e863b7629c43c5a323625f95cf24b119"
  integrity sha1-43Nosehjt2KcQ8WjI2Jflc8ksRk=

get-stdin@^4.0.1:
  version "4.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/get-stdin/download/get-stdin-4.0.1.tgz#b968c6b0a04384324902e8bf1a5df32579a450fe"
  integrity sha1-uWjGsKBDhDJJAui/Gl3zJXmkUP4=

get-stream@^4.0.0, get-stream@^4.1.0:
  version "4.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/get-stream/download/get-stream-4.1.0.tgz#c1b255575f3dc21d59bfc79cd3d2b46b1c3a54b5"
  integrity sha1-wbJVV189wh1Zv8ec09K0axw6VLU=
  dependencies:
    pump "^3.0.0"

get-symbol-description@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/get-symbol-description/download/get-symbol-description-1.0.0.tgz#7fdb81c900101fbd564dd5f1a30af5aadc1e58d6"
  integrity sha1-f9uByQAQH71WTdXxowr1qtweWNY=
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.1.1"

get-value@^2.0.3, get-value@^2.0.6:
  version "2.0.6"
  resolved "http://npm.devops.xiaohongshu.com:7001/get-value/download/get-value-2.0.6.tgz#dc15ca1c672387ca76bd37ac0a395ba2042a2c28"
  integrity sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=

getpass@^0.1.1:
  version "0.1.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/getpass/download/getpass-0.1.7.tgz#5eff8e3e684d569ae4cb2b1282604e8ba62149fa"
  integrity sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=
  dependencies:
    assert-plus "^1.0.0"

git-raw-commits@2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/git-raw-commits/download/git-raw-commits-2.0.0.tgz#d92addf74440c14bcc5c83ecce3fb7f8a79118b5"
  integrity sha1-2Srd90RAwUvMXIPszj+3+KeRGLU=
  dependencies:
    dargs "^4.0.1"
    lodash.template "^4.0.2"
    meow "^4.0.0"
    split2 "^2.0.0"
    through2 "^2.0.0"

git-remote-origin-url@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/git-remote-origin-url/download/git-remote-origin-url-2.0.0.tgz#5282659dae2107145a11126112ad3216ec5fa65f"
  integrity sha1-UoJlna4hBxRaERJhEq0yFuxfpl8=
  dependencies:
    gitconfiglocal "^1.0.0"
    pify "^2.3.0"

git-semver-tags@^2.0.3:
  version "2.0.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/git-semver-tags/download/git-semver-tags-2.0.3.tgz#48988a718acf593800f99622a952a77c405bfa34"
  integrity sha1-SJiKcYrPWTgA+ZYiqVKnfEBb+jQ=
  dependencies:
    meow "^4.0.0"
    semver "^6.0.0"

git-up@^4.0.0:
  version "4.0.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/git-up/download/git-up-4.0.5.tgz#e7bb70981a37ea2fb8fe049669800a1f9a01d759"
  integrity sha1-57twmBo36i+4/gSWaYAKH5oB11k=
  dependencies:
    is-ssh "^1.3.0"
    parse-url "^6.0.0"

git-url-parse@^11.1.2:
  version "11.6.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/git-url-parse/download/git-url-parse-11.6.0.tgz#c634b8de7faa66498a2b88932df31702c67df605"
  integrity sha1-xjS43n+qZkmKK4iTLfMXAsZ99gU=
  dependencies:
    git-up "^4.0.0"

gitconfiglocal@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/gitconfiglocal/download/gitconfiglocal-1.0.0.tgz#41d045f3851a5ea88f03f24ca1c6178114464b9b"
  integrity sha1-QdBF84UaXqiPA/JMocYXgRRGS5s=
  dependencies:
    ini "^1.3.2"

gl-matrix@^3.0.0, gl-matrix@^3.1.0, gl-matrix@^3.3.0, gl-matrix@^3.4.3:
  version "3.4.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/gl-matrix/-/gl-matrix-3.4.3.tgz#fc1191e8320009fd4d20e9339595c6041ddc22c9"
  integrity sha512-wcCp8vu8FT22BnvKVPjXa/ICBWRq/zjFfdofZy1WSpQZpphblv12/bOQLBC1rMM7SGOFS9ltVmKOHil5+Ml7gA==

gl-vec2@^1.3.0:
  version "1.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/gl-vec2/-/gl-vec2-1.3.0.tgz#83d472ed46034de8e09cbc857123fb6c81c51199"
  integrity sha512-YiqaAuNsheWmUV0Sa8k94kBB0D6RWjwZztyO+trEYS8KzJ6OQB/4686gdrf59wld4hHFIvaxynO3nRxpk1Ij/A==

glob-parent@^3.1.0:
  version "3.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/glob-parent/download/glob-parent-3.1.0.tgz#9e6af6299d8d3bd2bd40430832bd113df906c5ae"
  integrity sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=
  dependencies:
    is-glob "^3.1.0"
    path-dirname "^1.0.0"

glob-parent@^5.0.0:
  version "5.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/glob-parent/download/glob-parent-5.1.2.tgz#869832c58034fe68a4093c17dc15e8340d8401c4"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob-to-regexp@^0.3.0:
  version "0.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/glob-to-regexp/download/glob-to-regexp-0.3.0.tgz#8c5a1494d2066c570cc3bfe4496175acc4d502ab"
  integrity sha1-jFoUlNIGbFcMw7/kSWF1rMTVAqs=

glob@^7.1.1, glob@^7.1.3, glob@^7.1.4:
  version "7.2.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/glob/download/glob-7.2.3.tgz#b8df0fb802bbfa8e89bd1d938b4e16578ed44f2b"
  integrity sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globby@^9.2.0:
  version "9.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/globby/download/globby-9.2.0.tgz#fd029a706c703d29bdd170f4b6db3a3f7a7cb63d"
  integrity sha1-/QKacGxwPSm90XD0tts6P3p8tj0=
  dependencies:
    "@types/glob" "^7.1.1"
    array-union "^1.0.2"
    dir-glob "^2.2.2"
    fast-glob "^2.2.6"
    glob "^7.1.3"
    ignore "^4.0.3"
    pify "^4.0.1"
    slash "^2.0.0"

google-protobuf@^3.17.3:
  version "3.21.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/google-protobuf/download/google-protobuf-3.21.0.tgz#8dfa3fca16218618d373d414d3c1139e28034d6e"
  integrity sha1-jfo/yhYhhhjTc9QU08ETnigDTW4=

graceful-fs@^4.1.11, graceful-fs@^4.1.15, graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.2:
  version "4.2.10"
  resolved "http://npm.devops.xiaohongshu.com:7001/graceful-fs/download/graceful-fs-4.2.10.tgz#147d3a006da4ca3ce14728c7aefc287c367d7a6c"
  integrity sha1-FH06AG2kyjzhRyjHrvwofDZ9emw=

graphlib@^2.1.8:
  version "2.1.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/graphlib/-/graphlib-2.1.8.tgz#5761d414737870084c92ec7b5dbcb0592c9d35da"
  integrity sha512-jcLLfkpoVGmH7/InMC/1hIvOPSUh38oJtGhvrOFGzioE1DZ+0YW16RgmOJhHiuWTvGiJQ9Z1Ik43JvkRPRvE+A==
  dependencies:
    lodash "^4.17.15"

handlebars@^4.7.6:
  version "4.7.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/handlebars/download/handlebars-4.7.7.tgz#9ce33416aad02dbd6c8fafa8240d5d98004945a1"
  integrity sha1-nOM0FqrQLb1sj6+oJA1dmABJRaE=
  dependencies:
    minimist "^1.2.5"
    neo-async "^2.6.0"
    source-map "^0.6.1"
    wordwrap "^1.0.0"
  optionalDependencies:
    uglify-js "^3.1.4"

har-schema@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/har-schema/download/har-schema-2.0.0.tgz#a94c2224ebcac04782a0d9035521f24735b7ec92"
  integrity sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=

har-validator@~5.1.3:
  version "5.1.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/har-validator/download/har-validator-5.1.5.tgz#1f0803b9f8cb20c0fa13822df1ecddb36bde1efd"
  integrity sha1-HwgDufjLIMD6E4It8ezds2veHv0=
  dependencies:
    ajv "^6.12.3"
    har-schema "^2.0.0"

hard-rejection@^2.1.0:
  version "2.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/hard-rejection/download/hard-rejection-2.1.0.tgz#1c6eda5c1685c63942766d79bb40ae773cecd883"
  integrity sha1-HG7aXBaFxjlCdm15u0Cudzzs2IM=

has-bigints@^1.0.1, has-bigints@^1.0.2:
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/has-bigints/download/has-bigints-1.0.2.tgz#0871bd3e3d51626f6ca0966668ba35d5602d6eaa"
  integrity sha1-CHG9Pj1RYm9soJZmaLo11WAtbqo=

has-flag@^3.0.0:
  version "3.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/has-flag/download/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-property-descriptors@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/has-property-descriptors/download/has-property-descriptors-1.0.0.tgz#610708600606d36961ed04c196193b6a607fa861"
  integrity sha1-YQcIYAYG02lh7QTBlhk7amB/qGE=
  dependencies:
    get-intrinsic "^1.1.1"

has-symbols@^1.0.2, has-symbols@^1.0.3:
  version "1.0.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/has-symbols/download/has-symbols-1.0.3.tgz#bb7b2c4349251dce87b125f7bdf874aa7c8b39f8"
  integrity sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg=

has-tostringtag@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/has-tostringtag/download/has-tostringtag-1.0.0.tgz#7e133818a7d394734f941e73c3d3f9291e658b25"
  integrity sha1-fhM4GKfTlHNPlB5zw9P5KR5liyU=
  dependencies:
    has-symbols "^1.0.2"

has-unicode@^2.0.0, has-unicode@^2.0.1:
  version "2.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/has-unicode/download/has-unicode-2.0.1.tgz#e0e6fe6a28cf51138855e086d1691e771de2a8b9"
  integrity sha1-4Ob+aijPUROIVeCG0Wkedx3iqLk=

has-value@^0.3.1:
  version "0.3.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/has-value/download/has-value-0.3.1.tgz#7b1f58bada62ca827ec0a2078025654845995e1f"
  integrity sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=
  dependencies:
    get-value "^2.0.3"
    has-values "^0.1.4"
    isobject "^2.0.0"

has-value@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/has-value/download/has-value-1.0.0.tgz#18b281da585b1c5c51def24c930ed29a0be6b177"
  integrity sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=
  dependencies:
    get-value "^2.0.6"
    has-values "^1.0.0"
    isobject "^3.0.0"

has-values@^0.1.4:
  version "0.1.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/has-values/download/has-values-0.1.4.tgz#6d61de95d91dfca9b9a02089ad384bff8f62b771"
  integrity sha1-bWHeldkd/Km5oCCJrThL/49it3E=

has-values@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/has-values/download/has-values-1.0.0.tgz#95b0b63fec2146619a6fe57fe75628d5a39efe4f"
  integrity sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

has@^1.0.3:
  version "1.0.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/has/download/has-1.0.3.tgz#722d7cbfc1f6aa8241f16dd814e011e1f41e8796"
  integrity sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=
  dependencies:
    function-bind "^1.1.1"

headers-polyfill@^3.1.2:
  version "3.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/headers-polyfill/download/headers-polyfill-3.1.2.tgz#9a4dcb545c5b95d9569592ef7ec0708aab763fbe"
  integrity sha1-mk3LVFxbldlWlZLvfsBwiqt2P74=

hosted-git-info@^2.1.4, hosted-git-info@^2.7.1:
  version "2.8.9"
  resolved "http://npm.devops.xiaohongshu.com:7001/hosted-git-info/download/hosted-git-info-2.8.9.tgz#dffc0bf9a21c02209090f2aa69429e1414daf3f9"
  integrity sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k=

hosted-git-info@^4.0.1:
  version "4.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/hosted-git-info/download/hosted-git-info-4.1.0.tgz#827b82867e9ff1c8d0c4d9d53880397d2c86d224"
  integrity sha1-gnuChn6f8cjQxNnVOIA5fSyG0iQ=
  dependencies:
    lru-cache "^6.0.0"

html-void-elements@^2.0.0:
  version "2.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/html-void-elements/download/html-void-elements-2.0.1.tgz#29459b8b05c200b6c5ee98743c41b979d577549f"
  integrity sha1-KUWbiwXCALbF7ph0PEG5edV3VJ8=

http-assert@^1.3.0:
  version "1.5.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/http-assert/download/http-assert-1.5.0.tgz#c389ccd87ac16ed2dfa6246fd73b926aa00e6b8f"
  integrity sha1-w4nM2HrBbtLfpiRv1zuSaqAOa48=
  dependencies:
    deep-equal "~1.0.1"
    http-errors "~1.8.0"

http-cache-semantics@^3.8.1:
  version "3.8.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/http-cache-semantics/download/http-cache-semantics-3.8.1.tgz#39b0e16add9b605bf0a9ef3d9daaf4843b4cacd2"
  integrity sha1-ObDhat2bYFvwqe89nar0hDtMrNI=

http-errors@2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/http-errors/download/http-errors-2.0.0.tgz#b7774a1486ef73cf7667ac9ae0858c012c57b9d3"
  integrity sha1-t3dKFIbvc892Z6ya4IWMASxXudM=
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

http-errors@^1.6.3, http-errors@^1.7.3, http-errors@~1.8.0:
  version "1.8.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/http-errors/download/http-errors-1.8.1.tgz#7c3f28577cbc8a207388455dbd62295ed07bd68c"
  integrity sha1-fD8oV3y8iiBziEVdvWIpXtB71ow=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.1"

http-errors@~1.6.2:
  version "1.6.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/http-errors/download/http-errors-1.6.3.tgz#8b55680bb4be283a0b5bf4ea2e38580be1d9320d"
  integrity sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.0"
    statuses ">= 1.4.0 < 2"

http-proxy-agent@^2.1.0:
  version "2.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/http-proxy-agent/download/http-proxy-agent-2.1.0.tgz#e4821beef5b2142a2026bd73926fe537631c5405"
  integrity sha1-5IIb7vWyFCogJr1zkm/lN2McVAU=
  dependencies:
    agent-base "4"
    debug "3.1.0"

http-signature@~1.2.0:
  version "1.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/http-signature/download/http-signature-1.2.0.tgz#9aecd925114772f3d95b65a60abb8f7c18fbace1"
  integrity sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=
  dependencies:
    assert-plus "^1.0.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

https-proxy-agent@^2.2.3:
  version "2.2.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/https-proxy-agent/download/https-proxy-agent-2.2.4.tgz#4ee7a737abd92678a293d9b34a1af4d0d08c787b"
  integrity sha1-TuenN6vZJniik9mzShr00NCMeHs=
  dependencies:
    agent-base "^4.3.0"
    debug "^3.1.0"

humanize-ms@^1.2.1:
  version "1.2.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/humanize-ms/download/humanize-ms-1.2.1.tgz#c46e3159a293f6b896da29316d8b6fe8bb79bbed"
  integrity sha1-xG4xWaKT9riW2ikxbYtv6Lt5u+0=
  dependencies:
    ms "^2.0.0"

i18next@^20.4.0:
  version "20.6.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/i18next/download/i18next-20.6.1.tgz#535e5f6e5baeb685c7d25df70db63bf3cc0aa345"
  integrity sha1-U15fbluutoXH0l33DbY788wKo0U=
  dependencies:
    "@babel/runtime" "^7.12.0"

iconv-lite@0.4.24, iconv-lite@^0.4.24:
  version "0.4.24"
  resolved "http://npm.devops.xiaohongshu.com:7001/iconv-lite/download/iconv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

iconv-lite@^0.6.2:
  version "0.6.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/iconv-lite/download/iconv-lite-0.6.3.tgz#a52f80bf38da1952eb5c681790719871a1a72501"
  integrity sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

ieee754@^1.2.1:
  version "1.2.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/ieee754/download/ieee754-1.2.1.tgz#8eb7a10a63fff25d15a57b001586d177d1b0d352"
  integrity sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=

iferr@^0.1.5:
  version "0.1.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/iferr/download/iferr-0.1.5.tgz#c60eed69e6d8fdb6b3104a1fcbca1c192dc5b501"
  integrity sha1-xg7taebY/bazEEofy8ocGS3FtQE=

ignore-walk@^3.0.1:
  version "3.0.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/ignore-walk/download/ignore-walk-3.0.4.tgz#c9a09f69b7c7b479a5d74ac1a3c0d4236d2a6335"
  integrity sha1-yaCfabfHtHml10rBo8DUI20qYzU=
  dependencies:
    minimatch "^3.0.4"

ignore@^4.0.3:
  version "4.0.6"
  resolved "http://npm.devops.xiaohongshu.com:7001/ignore/download/ignore-4.0.6.tgz#750e3db5862087b4737ebac8207ffd1ef27b25fc"
  integrity sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=

immer@^9.0.6:
  version "9.0.21"
  resolved "http://npm.devops.xiaohongshu.com:7001/immer/download/immer-9.0.21.tgz#1e025ea31a40f24fb064f1fef23e931496330176"
  integrity sha1-HgJeoxpA8k+wZPH+8j6TFJYzAXY=

import-fresh@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/import-fresh/download/import-fresh-2.0.0.tgz#d81355c15612d386c61f9ddd3922d4304822a546"
  integrity sha1-2BNVwVYS04bGH53dOSLUMEgipUY=
  dependencies:
    caller-path "^2.0.0"
    resolve-from "^3.0.0"

import-html-entry@^1.14.0:
  version "1.14.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/import-html-entry/download/import-html-entry-1.14.0.tgz#9a05928373bb7e9362cd8dd428a82387a87524ff"
  integrity sha1-mgWSg3O7fpNizY3UKKgjh6h1JP8=
  dependencies:
    "@babel/runtime" "^7.7.2"

import-local@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/import-local/download/import-local-2.0.0.tgz#55070be38a5993cf18ef6db7e961f5bee5c5a09d"
  integrity sha1-VQcL44pZk88Y72236WH1vuXFoJ0=
  dependencies:
    pkg-dir "^3.0.0"
    resolve-cwd "^2.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/imurmurhash/download/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

indent-string@^2.1.0:
  version "2.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/indent-string/download/indent-string-2.1.0.tgz#8e2d48348742121b4a8218b7a137e9a52049dc80"
  integrity sha1-ji1INIdCEhtKghi3oTfppSBJ3IA=
  dependencies:
    repeating "^2.0.0"

indent-string@^3.0.0:
  version "3.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/indent-string/download/indent-string-3.2.0.tgz#4a5fd6d27cc332f37e5419a504dbb837105c9289"
  integrity sha1-Sl/W0nzDMvN+VBmlBNu4NxBckok=

indent-string@^4.0.0:
  version "4.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/indent-string/download/indent-string-4.0.0.tgz#624f8f4497d619b2d9768531d58f4122854d7251"
  integrity sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=

infer-owner@^1.0.3, infer-owner@^1.0.4:
  version "1.0.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/infer-owner/download/infer-owner-1.0.4.tgz#c4cefcaa8e51051c2a40ba2ce8a3d27295af9467"
  integrity sha1-xM78qo5RBRwqQLos6KPScpWvlGc=

inflation@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/inflation/download/inflation-2.0.0.tgz#8b417e47c28f925a45133d914ca1fd389107f30f"
  integrity sha1-i0F+R8KPklpFEz2RTKH9OJEH8w8=

inflight@^1.0.4:
  version "1.0.6"
  resolved "http://npm.devops.xiaohongshu.com:7001/inflight/download/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@^2.0.1, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.3:
  version "2.0.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/inherits/download/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

inherits@2.0.3:
  version "2.0.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/inherits/download/inherits-2.0.3.tgz#633c2c83e3da42a502f52466022480f4208261de"
  integrity sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=

ini@^1.3.2, ini@^1.3.4:
  version "1.3.8"
  resolved "http://npm.devops.xiaohongshu.com:7001/ini/download/ini-1.3.8.tgz#a29da425b48806f34767a4efce397269af28432c"
  integrity sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw=

init-package-json@^1.10.3:
  version "1.10.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/init-package-json/download/init-package-json-1.10.3.tgz#45ffe2f610a8ca134f2bd1db5637b235070f6cbe"
  integrity sha1-Rf/i9hCoyhNPK9HbVjeyNQcPbL4=
  dependencies:
    glob "^7.1.1"
    npm-package-arg "^4.0.0 || ^5.0.0 || ^6.0.0"
    promzard "^0.3.0"
    read "~1.0.1"
    read-package-json "1 || 2"
    semver "2.x || 3.x || 4 || 5"
    validate-npm-package-license "^3.0.1"
    validate-npm-package-name "^3.0.0"

inquirer@^6.2.0:
  version "6.5.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/inquirer/download/inquirer-6.5.2.tgz#ad50942375d036d327ff528c08bd5fab089928ca"
  integrity sha1-rVCUI3XQNtMn/1KMCL1fqwiZKMo=
  dependencies:
    ansi-escapes "^3.2.0"
    chalk "^2.4.2"
    cli-cursor "^2.1.0"
    cli-width "^2.0.0"
    external-editor "^3.0.3"
    figures "^2.0.0"
    lodash "^4.17.12"
    mute-stream "0.0.7"
    run-async "^2.2.0"
    rxjs "^6.4.0"
    string-width "^2.1.0"
    strip-ansi "^5.1.0"
    through "^2.3.6"

insert-css@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/insert-css/-/insert-css-2.0.0.tgz#eb5d1097b7542f4c79ea3060d3aee07d053880f4"
  integrity sha512-xGq5ISgcUP5cvGkS2MMFLtPDBtrtQPSFfC6gA6U8wHKqfjTIMZLZNxOItQnoSjdOzlXOLU/yD32RKC4SvjNbtA==

internal-slot@^1.0.3:
  version "1.0.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/internal-slot/download/internal-slot-1.0.3.tgz#7347e307deeea2faac2ac6205d4bc7d34967f59c"
  integrity sha1-c0fjB97uovqsKsYgXUvH00ln9Zw=
  dependencies:
    get-intrinsic "^1.1.0"
    has "^1.0.3"
    side-channel "^1.0.4"

ip@1.1.5:
  version "1.1.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/ip/download/ip-1.1.5.tgz#bdded70114290828c0a039e72ef25f5aaec4354a"
  integrity sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo=

is-accessor-descriptor@^0.1.6:
  version "0.1.6"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz#a9e12cb3ae8d876727eeef3843f8a0897b5c98d6"
  integrity sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=
  dependencies:
    kind-of "^3.0.2"

is-accessor-descriptor@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz#169c2f6d3df1f992618072365c9b0ea1f6878656"
  integrity sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=
  dependencies:
    kind-of "^6.0.0"

is-any-array@^2.0.0:
  version "2.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-any-array/-/is-any-array-2.0.1.tgz#9233242a9c098220290aa2ec28f82ca7fa79899e"
  integrity sha512-UtilS7hLRu++wb/WBAw9bNuP1Eg04Ivn1vERJck8zJthEvXCBEBpGR/33u/xLKWEQf95803oalHrVDptcAvFdQ==

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-arrayish/download/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-arrayish@^0.3.1:
  version "0.3.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-arrayish/-/is-arrayish-0.3.2.tgz#4574a2ae56f7ab206896fb431eaeed066fdf8f03"
  integrity sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==

is-bigint@^1.0.1:
  version "1.0.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-bigint/download/is-bigint-1.0.4.tgz#08147a1875bc2b32005d41ccd8291dffc6691df3"
  integrity sha1-CBR6GHW8KzIAXUHM2Ckd/8ZpHfM=
  dependencies:
    has-bigints "^1.0.1"

is-boolean-object@^1.1.0:
  version "1.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-boolean-object/download/is-boolean-object-1.1.2.tgz#5c6dc200246dd9321ae4b885a114bb1f75f63719"
  integrity sha1-XG3CACRt2TIa5LiFoRS7H3X2Nxk=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-buffer@^1.1.5, is-buffer@~1.1.6:
  version "1.1.6"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-buffer/download/is-buffer-1.1.6.tgz#efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be"
  integrity sha1-76ouqdqg16suoTqXsritUf776L4=

is-callable@^1.1.4, is-callable@^1.2.4:
  version "1.2.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-callable/download/is-callable-1.2.4.tgz#47301d58dd0259407865547853df6d61fe471945"
  integrity sha1-RzAdWN0CWUB4ZVR4U99tYf5HGUU=

is-ci@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-ci/download/is-ci-2.0.0.tgz#6bc6334181810e04b5c22b3d589fdca55026404c"
  integrity sha1-a8YzQYGBDgS1wis9WJ/cpVAmQEw=
  dependencies:
    ci-info "^2.0.0"

is-core-module@^2.5.0, is-core-module@^2.9.0:
  version "2.10.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-core-module/download/is-core-module-2.10.0.tgz#9012ede0a91c69587e647514e1d5277019e728ed"
  integrity sha1-kBLt4KkcaVh+ZHUU4dUncBnnKO0=
  dependencies:
    has "^1.0.3"

is-data-descriptor@^0.1.4:
  version "0.1.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz#0b5ee648388e2c860282e793f1856fec3f301b56"
  integrity sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=
  dependencies:
    kind-of "^3.0.2"

is-data-descriptor@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz#d84876321d0e7add03990406abbbbd36ba9268c7"
  integrity sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=
  dependencies:
    kind-of "^6.0.0"

is-date-object@^1.0.1:
  version "1.0.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-date-object/download/is-date-object-1.0.5.tgz#0841d5536e724c25597bf6ea62e1bd38298df31f"
  integrity sha1-CEHVU25yTCVZe/bqYuG9OCmN8x8=
  dependencies:
    has-tostringtag "^1.0.0"

is-descriptor@^0.1.0:
  version "0.1.6"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-descriptor/download/is-descriptor-0.1.6.tgz#366d8240dde487ca51823b1ab9f07a10a78251ca"
  integrity sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=
  dependencies:
    is-accessor-descriptor "^0.1.6"
    is-data-descriptor "^0.1.4"
    kind-of "^5.0.0"

is-descriptor@^1.0.0, is-descriptor@^1.0.2:
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-descriptor/download/is-descriptor-1.0.2.tgz#3b159746a66604b04f8c81524ba365c5f14d86ec"
  integrity sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=
  dependencies:
    is-accessor-descriptor "^1.0.0"
    is-data-descriptor "^1.0.0"
    kind-of "^6.0.2"

is-directory@^0.3.1:
  version "0.3.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-directory/download/is-directory-0.3.1.tgz#61339b6f2475fc772fd9c9d83f5c8575dc154ae1"
  integrity sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE=

is-extendable@^0.1.0, is-extendable@^0.1.1:
  version "0.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-extendable/download/is-extendable-0.1.1.tgz#62b110e289a471418e3ec36a617d472e301dfc89"
  integrity sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=

is-extendable@^1.0.1:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-extendable/download/is-extendable-1.0.1.tgz#a7470f9e426733d81bd81e1155264e3a3507cab4"
  integrity sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=
  dependencies:
    is-plain-object "^2.0.4"

is-extglob@^2.1.0, is-extglob@^2.1.1:
  version "2.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-extglob/download/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-finite@^1.0.0:
  version "1.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-finite/download/is-finite-1.1.0.tgz#904135c77fb42c0641d6aa1bcdbc4daa8da082f3"
  integrity sha1-kEE1x3+0LAZB1qobzbxNqo2ggvM=

is-fullwidth-code-point@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz#ef9e31386f031a7f0d643af82fde50c457ef00cb"
  integrity sha1-754xOG8DGn8NZDr4L95QxFfvAMs=
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz#a3b30a5c4f199183167aaab93beefae3ddfb654f"
  integrity sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-generator-function@^1.0.7:
  version "1.0.10"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-generator-function/download/is-generator-function-1.0.10.tgz#f1558baf1ac17e0deea7c0415c438351ff2b3c72"
  integrity sha1-8VWLrxrBfg3up8BBXEODUf8rPHI=
  dependencies:
    has-tostringtag "^1.0.0"

is-glob@^3.1.0:
  version "3.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-glob/download/is-glob-3.1.0.tgz#7ba5ae24217804ac70707b96922567486cc3e84a"
  integrity sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=
  dependencies:
    is-extglob "^2.1.0"

is-glob@^4.0.0, is-glob@^4.0.1:
  version "4.0.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-glob/download/is-glob-4.0.3.tgz#64f61e42cbbb2eec2071a9dac0b28ba1e65d5084"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-hotkey@^0.2.0:
  version "0.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-hotkey/download/is-hotkey-0.2.0.tgz#1835a68171a91e5c9460869d96336947c8340cef"
  integrity sha1-GDWmgXGpHlyUYIadljNpR8g0DO8=

is-negative-zero@^2.0.2:
  version "2.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-negative-zero/download/is-negative-zero-2.0.2.tgz#7bf6f03a28003b8b3965de3ac26f664d765f3150"
  integrity sha1-e/bwOigAO4s5Zd46wm9mTXZfMVA=

is-number-object@^1.0.4:
  version "1.0.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-number-object/download/is-number-object-1.0.7.tgz#59d50ada4c45251784e9904f5246c742f07a42fc"
  integrity sha1-WdUK2kxFJReE6ZBPUkbHQvB6Qvw=
  dependencies:
    has-tostringtag "^1.0.0"

is-number@^3.0.0:
  version "3.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-number/download/is-number-3.0.0.tgz#24fd6201a4782cf50561c810276afc7d12d71195"
  integrity sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=
  dependencies:
    kind-of "^3.0.2"

is-obj@^1.0.0:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-obj/download/is-obj-1.0.1.tgz#3e4729ac1f5fde025cd7d83a896dab9f4f67db0f"
  integrity sha1-PkcprB9f3gJc19g6iW2rn09n2w8=

is-obj@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-obj/download/is-obj-2.0.0.tgz#473fb05d973705e3fd9620545018ca8e22ef4982"
  integrity sha1-Rz+wXZc3BeP9liBUUBjKjiLvSYI=

is-plain-obj@^1.0.0, is-plain-obj@^1.1.0:
  version "1.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-plain-obj/download/is-plain-obj-1.1.0.tgz#71a50c8429dfca773c92a390a4a03b39fcd51d3e"
  integrity sha1-caUMhCnfync8kqOQpKA7OfzVHT4=

is-plain-object@^2.0.3, is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-plain-object/download/is-plain-object-2.0.4.tgz#2c163b3fafb1b606d9d17928f05c2a1c38e07677"
  integrity sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=
  dependencies:
    isobject "^3.0.1"

is-plain-object@^5.0.0:
  version "5.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-plain-object/download/is-plain-object-5.0.0.tgz#4427f50ab3429e9025ea7d52e9043a9ef4159344"
  integrity sha1-RCf1CrNCnpAl6n1S6QQ6nvQVk0Q=

is-regex@^1.1.4:
  version "1.1.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-regex/download/is-regex-1.1.4.tgz#eef5663cd59fa4c0ae339505323df6854bb15958"
  integrity sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-shared-array-buffer@^1.0.2:
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-shared-array-buffer/download/is-shared-array-buffer-1.0.2.tgz#8f259c573b60b6a32d4058a1a07430c0a7344c79"
  integrity sha1-jyWcVztgtqMtQFihoHQwwKc0THk=
  dependencies:
    call-bind "^1.0.2"

is-ssh@^1.3.0:
  version "1.4.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-ssh/download/is-ssh-1.4.0.tgz#4f8220601d2839d8fa624b3106f8e8884f01b8b2"
  integrity sha1-T4IgYB0oOdj6YksxBvjoiE8BuLI=
  dependencies:
    protocols "^2.0.1"

is-stream@^1.1.0:
  version "1.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-stream/download/is-stream-1.1.0.tgz#12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44"
  integrity sha1-EtSj3U5o4Lec6428hBc66A2RykQ=

is-string@^1.0.4, is-string@^1.0.5, is-string@^1.0.7:
  version "1.0.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-string/download/is-string-1.0.7.tgz#0dd12bf2006f255bb58f695110eff7491eebc0fd"
  integrity sha1-DdEr8gBvJVu1j2lREO/3SR7rwP0=
  dependencies:
    has-tostringtag "^1.0.0"

is-symbol@^1.0.2, is-symbol@^1.0.3:
  version "1.0.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-symbol/download/is-symbol-1.0.4.tgz#a6dac93b635b063ca6872236de88910a57af139c"
  integrity sha1-ptrJO2NbBjymhyI23oiRClevE5w=
  dependencies:
    has-symbols "^1.0.2"

is-text-path@^1.0.1:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-text-path/download/is-text-path-1.0.1.tgz#4e1aa0fb51bfbcb3e92688001397202c1775b66e"
  integrity sha1-Thqg+1G/vLPpJogAE5cgLBd1tm4=
  dependencies:
    text-extensions "^1.0.0"

is-typedarray@^1.0.0, is-typedarray@~1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-typedarray/download/is-typedarray-1.0.0.tgz#e479c80858df0c1b11ddda6940f96011fcda4a9a"
  integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=

is-url@^1.2.4:
  version "1.2.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-url/download/is-url-1.2.4.tgz#04a4df46d28c4cff3d73d01ff06abeb318a1aa52"
  integrity sha1-BKTfRtKMTP89c9Af8Gq+sxihqlI=

is-utf8@^0.2.0:
  version "0.2.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-utf8/download/is-utf8-0.2.1.tgz#4b0da1442104d1b336340e80797e865cf39f7d72"
  integrity sha1-Sw2hRCEE0bM2NA6AeX6GXPOffXI=

is-weakref@^1.0.2:
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-weakref/download/is-weakref-1.0.2.tgz#9529f383a9338205e89765e0392efc2f100f06f2"
  integrity sha1-lSnzg6kzggXol2XgOS78LxAPBvI=
  dependencies:
    call-bind "^1.0.2"

is-windows@^1.0.0, is-windows@^1.0.2:
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/is-windows/download/is-windows-1.0.2.tgz#d1850eb9791ecd18e6182ce12a30f396634bb19d"
  integrity sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=

isarray@1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/isarray/download/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/isexe/download/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isobject@^2.0.0:
  version "2.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/isobject/download/isobject-2.1.0.tgz#f065561096a3f1da2ef46272f815c840d87e0c89"
  integrity sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=
  dependencies:
    isarray "1.0.0"

isobject@^3.0.0, isobject@^3.0.1:
  version "3.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/isobject/download/isobject-3.0.1.tgz#4e431e92b11a9731636aa1f9c8d1ccbcfdab78df"
  integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=

isomorphic-ws@^4.0.1:
  version "4.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/isomorphic-ws/download/isomorphic-ws-4.0.1.tgz#55fd4cd6c5e6491e76dc125938dd863f5cd4f2dc"
  integrity sha1-Vf1M1sXmSR523BJZON2GP1zU8tw=

isstream@~0.1.2:
  version "0.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/isstream/download/isstream-0.1.2.tgz#47e63f7af55afa6f92e1500e690eb8b8529c099a"
  integrity sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=

js-cookie@^3.0.5:
  version "3.0.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/js-cookie/download/js-cookie-3.0.5.tgz#0b7e2fd0c01552c58ba86e0841f94dc2557dcdbc"
  integrity sha1-C34v0MAVUsWLqG4IQflNwlV9zbw=

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/js-tokens/download/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-yaml@^3.13.1:
  version "3.14.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/js-yaml/download/js-yaml-3.14.1.tgz#dae812fdb3825fa306609a8717383c50c36a0537"
  integrity sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

jsbn@~0.1.0:
  version "0.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/jsbn/download/jsbn-0.1.1.tgz#a5e654c2e5a2deb5f201d96cefbca80c0ef2f513"
  integrity sha1-peZUwuWi3rXyAdls77yoDA7y9RM=

jsmind@^0.8.7:
  version "0.8.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jsmind/-/jsmind-0.8.7.tgz#2758d81f680aeb3eaf279d32c4b40ff410e0e1f9"
  integrity sha512-sEoYSEmugKPaPPXh0byA2/WhFZqfe4lu2iaMAHmMc4c8GoEkrER+htTmnMmXza0l9iwOPMnm31zPSqdQdcjXSw==

json-parse-better-errors@^1.0.0, json-parse-better-errors@^1.0.1:
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz#bb867cfb3450e69107c131d1c514bab3dc8bcaa9"
  integrity sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz#7c47805a94319928e05777405dc12e1f7a4ee02d"
  integrity sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/json-schema-traverse/download/json-schema-traverse-1.0.0.tgz#ae7bcb3656ab77a73ba5c49bf654f38e6b6860e2"
  integrity sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=

json-schema@0.4.0:
  version "0.4.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/json-schema/download/json-schema-0.4.0.tgz#f7de4cf6efab838ebaeb3236474cbba5a1930ab5"
  integrity sha1-995M9u+rg4666zI2R0y7paGTCrU=

json-stringify-safe@^5.0.1, json-stringify-safe@~5.0.1:
  version "5.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz#1296a2d58fd45f19a0f6ce01d65701e2c735b6eb"
  integrity sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=

jsonfile@^4.0.0:
  version "4.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/jsonfile/download/jsonfile-4.0.0.tgz#8771aae0799b64076b76640fca058f9c10e33ecb"
  integrity sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/jsonfile/download/jsonfile-6.1.0.tgz#bc55b2634793c679ec6403094eb13698a6ec0aae"
  integrity sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonparse@^1.2.0:
  version "1.3.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/jsonparse/download/jsonparse-1.3.1.tgz#3f4dae4a91fac315f71062f8521cc239f1366280"
  integrity sha1-P02uSpH6wxX3EGL4UhzCOfE2YoA=

jsonschema@^1.2.6:
  version "1.4.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/jsonschema/download/jsonschema-1.4.1.tgz#cc4c3f0077fb4542982973d8a083b6b34f482dab"
  integrity sha1-zEw/AHf7RUKYKXPYoIO2s09ILas=

jsprim@^1.2.2:
  version "1.4.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/jsprim/download/jsprim-1.4.2.tgz#712c65533a15c878ba59e9ed5f0e26d5b77c5feb"
  integrity sha1-cSxlUzoVyHi6WentXw4m1bd8X+s=
  dependencies:
    assert-plus "1.0.0"
    extsprintf "1.3.0"
    json-schema "0.4.0"
    verror "1.10.0"

keygrip@~1.1.0:
  version "1.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/keygrip/download/keygrip-1.1.0.tgz#871b1681d5e159c62a445b0c74b615e0917e7226"
  integrity sha1-hxsWgdXhWcYqRFsMdLYV4JF+ciY=
  dependencies:
    tsscmp "1.0.6"

kind-of@^3.0.2, kind-of@^3.0.3, kind-of@^3.2.0:
  version "3.2.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/kind-of/download/kind-of-3.2.2.tgz#31ea21a734bab9bbb0f32466d893aea51e4a3c64"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/kind-of/download/kind-of-4.0.0.tgz#20813df3d712928b207378691a45066fae72dd57"
  integrity sha1-IIE989cSkosgc3hpGkUGb65y3Vc=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^5.0.0:
  version "5.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/kind-of/download/kind-of-5.1.0.tgz#729c91e2d857b7a419a1f9aa65685c4c33f5845d"
  integrity sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=

kind-of@^6.0.0, kind-of@^6.0.2, kind-of@^6.0.3:
  version "6.0.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/kind-of/download/kind-of-6.0.3.tgz#07c05034a6c349fa06e24fa35aa76db4580ce4dd"
  integrity sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=

klaw-sync@~6.0.0:
  version "6.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/klaw-sync/download/klaw-sync-6.0.0.tgz#1fd2cfd56ebb6250181114f0a581167099c2b28c"
  integrity sha1-H9LP1W67YlAYERTwpYEWcJnCsow=
  dependencies:
    graceful-fs "^4.1.11"

koa-bodyparser@~4.4.0:
  version "4.4.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/koa-bodyparser/-/koa-bodyparser-4.4.1.tgz#a908d848e142cc57d9eece478e932bf00dce3029"
  integrity sha512-kBH3IYPMb+iAXnrxIhXnW+gXV8OTzCu8VPDqvcDHW9SQrbkHmqPQtiZwrltNmSq6/lpipHnT7k7PsjlVD7kK0w==
  dependencies:
    co-body "^6.0.0"
    copy-to "^2.0.1"
    type-is "^1.6.18"

koa-compose@^4.1.0, koa-compose@~4.1.0:
  version "4.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/koa-compose/download/koa-compose-4.1.0.tgz#507306b9371901db41121c812e923d0d67d3e877"
  integrity sha1-UHMGuTcZAdtBEhyBLpI9DWfT6Hc=

koa-convert@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/koa-convert/download/koa-convert-2.0.0.tgz#86a0c44d81d40551bae22fee6709904573eea4f5"
  integrity sha1-hqDETYHUBVG64i/uZwmQRXPupPU=
  dependencies:
    co "^4.6.0"
    koa-compose "^4.1.0"

koa-send@^5.0.0:
  version "5.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/koa-send/download/koa-send-5.0.1.tgz#39dceebfafb395d0d60beaffba3a70b4f543fe79"
  integrity sha1-Odzuv6+zldDWC+r/ujpwtPVD/nk=
  dependencies:
    debug "^4.1.1"
    http-errors "^1.7.3"
    resolve-path "^1.4.0"

koa-static@~5.0.0:
  version "5.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/koa-static/download/koa-static-5.0.0.tgz#5e92fc96b537ad5219f425319c95b64772776943"
  integrity sha1-XpL8lrU3rVIZ9CUxnJW2R3J3aUM=
  dependencies:
    debug "^3.1.0"
    koa-send "^5.0.0"

koa@~2.14.1:
  version "2.14.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/koa/download/koa-2.14.2.tgz#a57f925c03931c2b4d94b19d2ebf76d3244863fc"
  integrity sha1-pX+SXAOTHCtNlLGdLr920yRIY/w=
  dependencies:
    accepts "^1.3.5"
    cache-content-type "^1.0.0"
    content-disposition "~0.5.2"
    content-type "^1.0.4"
    cookies "~0.8.0"
    debug "^4.3.2"
    delegates "^1.0.0"
    depd "^2.0.0"
    destroy "^1.0.4"
    encodeurl "^1.0.2"
    escape-html "^1.0.3"
    fresh "~0.5.2"
    http-assert "^1.3.0"
    http-errors "^1.6.3"
    is-generator-function "^1.0.7"
    koa-compose "^4.1.0"
    koa-convert "^2.0.0"
    on-finished "^2.3.0"
    only "~0.0.2"
    parseurl "^1.3.2"
    statuses "^1.5.0"
    type-is "^1.6.16"
    vary "^1.1.2"

launch-editor-middleware@^2.6.0:
  version "2.6.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/launch-editor-middleware/download/launch-editor-middleware-2.6.0.tgz#2ba4fe4b695d7fe3d44dee86b6d46d57b8332dfd"
  integrity sha1-K6T+S2ldf+PUTe6GttRtV7gzLf0=
  dependencies:
    launch-editor "^2.6.0"

launch-editor@^2.6.0:
  version "2.6.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/launch-editor/download/launch-editor-2.6.0.tgz#4c0c1a6ac126c572bd9ff9a30da1d2cae66defd7"
  integrity sha1-TAwaasEmxXK9n/mjDaHSyuZt79c=
  dependencies:
    picocolors "^1.0.0"
    shell-quote "^1.7.3"

lerna@^3.19.0:
  version "3.22.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/lerna/download/lerna-3.22.1.tgz#82027ac3da9c627fd8bf02ccfeff806a98e65b62"
  integrity sha1-ggJ6w9qcYn/YvwLM/v+AapjmW2I=
  dependencies:
    "@lerna/add" "3.21.0"
    "@lerna/bootstrap" "3.21.0"
    "@lerna/changed" "3.21.0"
    "@lerna/clean" "3.21.0"
    "@lerna/cli" "3.18.5"
    "@lerna/create" "3.22.0"
    "@lerna/diff" "3.21.0"
    "@lerna/exec" "3.21.0"
    "@lerna/import" "3.22.0"
    "@lerna/info" "3.21.0"
    "@lerna/init" "3.21.0"
    "@lerna/link" "3.21.0"
    "@lerna/list" "3.21.0"
    "@lerna/publish" "3.22.1"
    "@lerna/run" "3.21.0"
    "@lerna/version" "3.22.1"
    import-local "^2.0.0"
    npmlog "^4.1.2"

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/lines-and-columns/download/lines-and-columns-1.2.4.tgz#eca284f75d2965079309dc0ad9255abb2ebc1632"
  integrity sha1-7KKE910pZQeTCdwK2SVauy68FjI=

linkify-it@^5.0.0:
  version "5.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/linkify-it/-/linkify-it-5.0.0.tgz#9ef238bfa6dc70bd8e7f9572b52d369af569b421"
  integrity sha512-5aHCbzQRADcdP+ATqnDuhhJ/MRIqDkZX5pyjFHRRysS8vZ5AbqGEoFIb6pYHPZ+L/OC2Lc+xT8uHVVR5CAK/wQ==
  dependencies:
    uc.micro "^2.0.0"

load-json-file@^1.0.0:
  version "1.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/load-json-file/download/load-json-file-1.1.0.tgz#956905708d58b4bab4c2261b04f59f31c99374c0"
  integrity sha1-lWkFcI1YtLq0wiYbBPWfMcmTdMA=
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^2.2.0"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"
    strip-bom "^2.0.0"

load-json-file@^4.0.0:
  version "4.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/load-json-file/download/load-json-file-4.0.0.tgz#2f5f45ab91e33216234fd53adab668eb4ec0993b"
  integrity sha1-L19Fq5HjMhYjT9U62rZo607AmTs=
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^4.0.0"
    pify "^3.0.0"
    strip-bom "^3.0.0"

load-json-file@^5.3.0:
  version "5.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/load-json-file/download/load-json-file-5.3.0.tgz#4d3c1e01fa1c03ea78a60ac7af932c9ce53403f3"
  integrity sha1-TTweAfocA+p4pgrHr5MsnOU0A/M=
  dependencies:
    graceful-fs "^4.1.15"
    parse-json "^4.0.0"
    pify "^4.0.1"
    strip-bom "^3.0.0"
    type-fest "^0.3.0"

locate-path@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/locate-path/download/locate-path-2.0.0.tgz#2b568b265eec944c6d9c0de9c3dbbbca0354cd8e"
  integrity sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=
  dependencies:
    p-locate "^2.0.0"
    path-exists "^3.0.0"

locate-path@^3.0.0:
  version "3.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/locate-path/download/locate-path-3.0.0.tgz#dbec3b3ab759758071b58fe59fc41871af21400e"
  integrity sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=
  dependencies:
    p-locate "^3.0.0"
    path-exists "^3.0.0"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/locate-path/download/locate-path-5.0.0.tgz#1afba396afd676a6d42504d0a67a3a7eb9f62aa0"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

lodash-es@^4.17.21:
  version "4.17.21"
  resolved "http://npm.devops.xiaohongshu.com:7001/lodash-es/download/lodash-es-4.17.21.tgz#43e626c46e6591b7750beb2b50117390c609e3ee"
  integrity sha1-Q+YmxG5lkbd1C+srUBFzkMYJ4+4=

lodash-unified@^1.0.2:
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/lodash-unified/download/lodash-unified-1.0.2.tgz#bb2694db3533781e5cce984af60cfaea318b83c1"
  integrity sha1-uyaU2zUzeB5czphK9gz66jGLg8E=

lodash._reinterpolate@^3.0.0:
  version "3.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/lodash._reinterpolate/download/lodash._reinterpolate-3.0.0.tgz#0ccf2d89166af03b3663c796538b75ac6e114d9d"
  integrity sha1-DM8tiRZq8Ds2Y8eWU4t1rG4RTZ0=

lodash.camelcase@^4.3.0:
  version "4.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/lodash.camelcase/download/lodash.camelcase-4.3.0.tgz#b28aa6288a2b9fc651035c7711f65ab6190331a6"
  integrity sha1-soqmKIorn8ZRA1x3EfZathkDMaY=

lodash.clonedeep@^4.5.0:
  version "4.5.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/lodash.clonedeep/download/lodash.clonedeep-4.5.0.tgz#e23f3f9c4f8fbdde872529c1071857a086e5ccef"
  integrity sha1-4j8/nE+Pvd6HJSnBBxhXoIblzO8=

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "http://npm.devops.xiaohongshu.com:7001/lodash.debounce/download/lodash.debounce-4.0.8.tgz#82d79bff30a67c4005ffd5e2515300ad9ca4d7af"
  integrity sha1-gteb/zCmfEAF/9XiUVMArZyk168=

lodash.foreach@^4.5.0:
  version "4.5.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/lodash.foreach/download/lodash.foreach-4.5.0.tgz#1a6a35eace401280c7f06dddec35165ab27e3e53"
  integrity sha1-Gmo16s5AEoDH8G3d7DUWWrJ+PlM=

lodash.get@^4.4.2:
  version "4.4.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/lodash.get/download/lodash.get-4.4.2.tgz#2d177f652fa31e939b4438d5341499dfa3825e99"
  integrity sha1-LRd/ZS+jHpObRDjVNBSZ36OCXpk=

lodash.isequal@^4.5.0:
  version "4.5.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/lodash.isequal/download/lodash.isequal-4.5.0.tgz#415c4478f2bcc30120c22ce10ed3226f7d3e18e0"
  integrity sha1-QVxEePK8wwEgwizhDtMib30+GOA=

lodash.ismatch@^4.4.0:
  version "4.4.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/lodash.ismatch/download/lodash.ismatch-4.4.0.tgz#756cb5150ca3ba6f11085a78849645f188f85f37"
  integrity sha1-dWy1FQyjum8RCFp4hJZF8Yj4Xzc=

lodash.merge@~4.6.2:
  version "4.6.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/lodash.merge/download/lodash.merge-4.6.2.tgz#558aa53b43b661e1925a0afdfa36a9a1085fe57a"
  integrity sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=

lodash.mergewith@^4.6.2:
  version "4.6.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/lodash.mergewith/download/lodash.mergewith-4.6.2.tgz#617121f89ac55f59047c7aec1ccd6654c6590f55"
  integrity sha1-YXEh+JrFX1kEfHrsHM1mVMZZD1U=

lodash.set@^4.3.2:
  version "4.3.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/lodash.set/download/lodash.set-4.3.2.tgz#d8757b1da807dde24816b0d6a84bea1a76230b23"
  integrity sha1-2HV7HagH3eJIFrDWqEvqGnYjCyM=

lodash.sortby@^4.7.0:
  version "4.7.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/lodash.sortby/download/lodash.sortby-4.7.0.tgz#edd14c824e2cc9c1e0b0a1b42bb5210516a42438"
  integrity sha1-7dFMgk4sycHgsKG0K7UhBRakJDg=

lodash.template@^4.0.2, lodash.template@^4.5.0:
  version "4.5.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/lodash.template/download/lodash.template-4.5.0.tgz#f976195cf3f347d0d5f52483569fe8031ccce8ab"
  integrity sha1-+XYZXPPzR9DV9SSDVp/oAxzM6Ks=
  dependencies:
    lodash._reinterpolate "^3.0.0"
    lodash.templatesettings "^4.0.0"

lodash.templatesettings@^4.0.0:
  version "4.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/lodash.templatesettings/download/lodash.templatesettings-4.2.0.tgz#e481310f049d3cf6d47e912ad09313b154f0fb33"
  integrity sha1-5IExDwSdPPbUfpEq0JMTsVTw+zM=
  dependencies:
    lodash._reinterpolate "^3.0.0"

lodash.throttle@^4.1.1:
  version "4.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/lodash.throttle/download/lodash.throttle-4.1.1.tgz#c23e91b710242ac70c37f1e1cda9274cc39bf2f4"
  integrity sha1-wj6RtxAkKscMN/HhzaknTMOb8vQ=

lodash.toarray@^4.4.0:
  version "4.4.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/lodash.toarray/download/lodash.toarray-4.4.0.tgz#24c4bfcd6b2fba38bfd0594db1179d8e9b656561"
  integrity sha1-JMS/zWsvuji/0FlNsRedjptlZWE=

lodash.uniq@^4.5.0:
  version "4.5.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/lodash.uniq/download/lodash.uniq-4.5.0.tgz#d0225373aeb652adc1bc82e4945339a842754773"
  integrity sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=

lodash.uniqueid@^4.0.1:
  version "4.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/lodash.uniqueid/download/lodash.uniqueid-4.0.1.tgz#3268f26a7c88e4f4b1758d679271814e31fa5b26"
  integrity sha1-MmjyanyI5PSxdY1nknGBTjH6WyY=

lodash@^4.17.11, lodash@^4.17.12, lodash@^4.17.15, lodash@^4.17.21, lodash@^4.2.1:
  version "4.17.21"
  resolved "http://npm.devops.xiaohongshu.com:7001/lodash/download/lodash-4.17.21.tgz#679591c564c3bffaae8454cf0b3df370c3d6911c"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

long@^5.2.3:
  version "5.2.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/long/download/long-5.2.3.tgz#a3ba97f3877cf1d778eccbcb048525ebb77499e1"
  integrity sha1-o7qX84d88dd47MvLBIUl67d0meE=

loose-envify@^1.3.1, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/loose-envify/download/loose-envify-1.4.0.tgz#71ee51fa7be4caec1a63839f7e682d8132d30caf"
  integrity sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

loud-rejection@^1.0.0:
  version "1.6.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/loud-rejection/download/loud-rejection-1.6.0.tgz#5b46f80147edee578870f086d04821cf998e551f"
  integrity sha1-W0b4AUft7leIcPCG0Eghz5mOVR8=
  dependencies:
    currently-unhandled "^0.4.1"
    signal-exit "^3.0.0"

lower-case@^2.0.1, lower-case@^2.0.2:
  version "2.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/lower-case/download/lower-case-2.0.2.tgz#6fa237c63dbdc4a82ca0fd882e4722dc5e634e28"
  integrity sha1-b6I3xj29xKgsoP2ILkci3F5jTig=
  dependencies:
    tslib "^2.0.3"

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/lru-cache/download/lru-cache-5.1.1.tgz#1da27e6710271947695daf6848e847f01d84b920"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/lru-cache/download/lru-cache-6.0.0.tgz#6d6fe6570ebd96aaf90fcad1dafa3b2566db3a94"
  integrity sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=
  dependencies:
    yallist "^4.0.0"

macos-release@^2.2.0:
  version "2.5.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/macos-release/download/macos-release-2.5.0.tgz#067c2c88b5f3fb3c56a375b2ec93826220fa1ff2"
  integrity sha1-BnwsiLXz+zxWo3Wy7JOCYiD6H/I=

magic-string@^0.25.7:
  version "0.25.9"
  resolved "http://npm.devops.xiaohongshu.com:7001/magic-string/download/magic-string-0.25.9.tgz#de7f9faf91ef8a1c91d02c2e5314c8277dbcdd1c"
  integrity sha1-3n+fr5HvihyR0CwuUxTIJ3283Rw=
  dependencies:
    sourcemap-codec "^1.4.8"

make-dir@^1.0.0:
  version "1.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/make-dir/download/make-dir-1.3.0.tgz#79c1033b80515bd6d24ec9933e860ca75ee27f0c"
  integrity sha1-ecEDO4BRW9bSTsmTPoYMp17ifww=
  dependencies:
    pify "^3.0.0"

make-dir@^2.1.0:
  version "2.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/make-dir/download/make-dir-2.1.0.tgz#5f0310e18b8be898cc07009295a30ae41e91e6f5"
  integrity sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=
  dependencies:
    pify "^4.0.1"
    semver "^5.6.0"

make-fetch-happen@^5.0.0:
  version "5.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/make-fetch-happen/download/make-fetch-happen-5.0.2.tgz#aa8387104f2687edca01c8687ee45013d02d19bd"
  integrity sha1-qoOHEE8mh+3KAchofuRQE9AtGb0=
  dependencies:
    agentkeepalive "^3.4.1"
    cacache "^12.0.0"
    http-cache-semantics "^3.8.1"
    http-proxy-agent "^2.1.0"
    https-proxy-agent "^2.2.3"
    lru-cache "^5.1.1"
    mississippi "^3.0.0"
    node-fetch-npm "^2.0.2"
    promise-retry "^1.1.1"
    socks-proxy-agent "^4.0.0"
    ssri "^6.0.0"

map-cache@^0.2.2:
  version "0.2.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/map-cache/download/map-cache-0.2.2.tgz#c32abd0bd6525d9b051645bb4f26ac5dc98a0dbf"
  integrity sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=

map-obj@^1.0.0, map-obj@^1.0.1:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/map-obj/download/map-obj-1.0.1.tgz#d933ceb9205d82bdcf4886f6742bdc2b4dea146d"
  integrity sha1-2TPOuSBdgr3PSIb2dCvcK03qFG0=

map-obj@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/map-obj/download/map-obj-2.0.0.tgz#a65cd29087a92598b8791257a523e021222ac1f9"
  integrity sha1-plzSkIepJZi4eRJXpSPgISIqwfk=

map-obj@^4.0.0:
  version "4.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/map-obj/download/map-obj-4.3.0.tgz#9304f906e93faae70880da102a9f1df0ea8bb05a"
  integrity sha1-kwT5Buk/qucIgNoQKp8d8OqLsFo=

map-visit@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/map-visit/download/map-visit-1.0.0.tgz#ecdca8f13144e660f1b5bd41f12f3479d98dfb8f"
  integrity sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=
  dependencies:
    object-visit "^1.0.0"

markdown-it@^14.1.0:
  version "14.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/markdown-it/-/markdown-it-14.1.0.tgz#3c3c5992883c633db4714ccb4d7b5935d98b7d45"
  integrity sha512-a54IwgWPaeBCAAsv13YgmALOF1elABB08FxO9i+r4VFk5Vl4pKokRPeX8u5TCgSsPi6ec1otfLjdOpVcgbpshg==
  dependencies:
    argparse "^2.0.1"
    entities "^4.4.0"
    linkify-it "^5.0.0"
    mdurl "^2.0.0"
    punycode.js "^2.3.1"
    uc.micro "^2.1.0"

md5@^2.2.1:
  version "2.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/md5/download/md5-2.3.0.tgz#c3da9a6aae3a30b46b7b0c349b87b110dc3bda4f"
  integrity sha1-w9qaaq46MLRreww0m4exENw72k8=
  dependencies:
    charenc "0.0.2"
    crypt "0.0.2"
    is-buffer "~1.1.6"

mdurl@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/mdurl/-/mdurl-2.0.0.tgz#80676ec0433025dd3e17ee983d0fe8de5a2237e0"
  integrity sha512-Lf+9+2r+Tdp5wXDXC4PcIBjTDtq4UKjCPMQhKIuzpJNW0b96kVqSwW0bT7FhRSfmAiFYgP+SCRvdrDozfh0U5w==

media-typer@0.3.0:
  version "0.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/media-typer/download/media-typer-0.3.0.tgz#8710d7af0aa626f8fffa1ce00168545263255748"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

meow@^3.3.0:
  version "3.7.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/meow/download/meow-3.7.0.tgz#72cb668b425228290abbfa856892587308a801fb"
  integrity sha1-cstmi0JSKCkKu/qFaJJYcwioAfs=
  dependencies:
    camelcase-keys "^2.0.0"
    decamelize "^1.1.2"
    loud-rejection "^1.0.0"
    map-obj "^1.0.1"
    minimist "^1.1.3"
    normalize-package-data "^2.3.4"
    object-assign "^4.0.1"
    read-pkg-up "^1.0.1"
    redent "^1.0.0"
    trim-newlines "^1.0.0"

meow@^4.0.0:
  version "4.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/meow/download/meow-4.0.1.tgz#d48598f6f4b1472f35bf6317a95945ace347f975"
  integrity sha1-1IWY9vSxRy81v2MXqVlFrONH+XU=
  dependencies:
    camelcase-keys "^4.0.0"
    decamelize-keys "^1.0.0"
    loud-rejection "^1.0.0"
    minimist "^1.1.3"
    minimist-options "^3.0.1"
    normalize-package-data "^2.3.4"
    read-pkg-up "^3.0.0"
    redent "^2.0.0"
    trim-newlines "^2.0.0"

meow@^8.0.0:
  version "8.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/meow/download/meow-8.1.2.tgz#bcbe45bda0ee1729d350c03cffc8395a36c4e897"
  integrity sha1-vL5FvaDuFynTUMA8/8g5WjbE6Jc=
  dependencies:
    "@types/minimist" "^1.2.0"
    camelcase-keys "^6.2.2"
    decamelize-keys "^1.1.0"
    hard-rejection "^2.1.0"
    minimist-options "4.1.0"
    normalize-package-data "^3.0.0"
    read-pkg-up "^7.0.1"
    redent "^3.0.0"
    trim-newlines "^3.0.0"
    type-fest "^0.18.0"
    yargs-parser "^20.2.3"

merge2@^1.2.3:
  version "1.4.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/merge2/download/merge2-1.4.1.tgz#4368892f885e907455a6fd7dc55c0c9d404990ae"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

micromatch@^3.1.10:
  version "3.1.10"
  resolved "http://npm.devops.xiaohongshu.com:7001/micromatch/download/micromatch-3.1.10.tgz#70859bc95c9840952f359a068a3fc49f9ecfac23"
  integrity sha1-cIWbyVyYQJUvNZoGij/En57PrCM=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.3.1"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    extglob "^2.0.4"
    fragment-cache "^0.2.1"
    kind-of "^6.0.2"
    nanomatch "^1.2.9"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.2"

mime-db@1.52.0:
  version "1.52.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/mime-db/download/mime-db-1.52.0.tgz#bbabcdc02859f4987301c856e3387ce5ec43bf70"
  integrity sha1-u6vNwChZ9JhzAchW4zh85exDv3A=

mime-match@^1.0.2:
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/mime-match/download/mime-match-1.0.2.tgz#3f87c31e9af1a5fd485fb9db134428b23bbb7ba8"
  integrity sha1-P4fDHprxpf1IX7nbE0Qosju7e6g=
  dependencies:
    wildcard "^1.1.0"

mime-types@^2.1.12, mime-types@^2.1.18, mime-types@~2.1.19, mime-types@~2.1.24, mime-types@~2.1.34:
  version "2.1.35"
  resolved "http://npm.devops.xiaohongshu.com:7001/mime-types/download/mime-types-2.1.35.tgz#381a871b62a734450660ae3deee44813f70d959a"
  integrity sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=
  dependencies:
    mime-db "1.52.0"

mimic-fn@^1.0.0:
  version "1.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/mimic-fn/download/mimic-fn-1.2.0.tgz#820c86a39334640e99516928bd03fca88057d022"
  integrity sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=

min-indent@^1.0.0:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/min-indent/download/min-indent-1.0.1.tgz#a63f681673b30571fbe8bc25686ae746eefa9869"
  integrity sha1-pj9oFnOzBXH76LwlaGrnRu76mGk=

minimatch@^3.0.4, minimatch@^3.1.1:
  version "3.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/minimatch/download/minimatch-3.1.2.tgz#19cd194bfd3e428f049a70817c038d89ab4be35b"
  integrity sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=
  dependencies:
    brace-expansion "^1.1.7"

minimist-options@4.1.0:
  version "4.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/minimist-options/download/minimist-options-4.1.0.tgz#c0655713c53a8a2ebd77ffa247d342c40f010619"
  integrity sha1-wGVXE8U6ii69d/+iR9NCxA8BBhk=
  dependencies:
    arrify "^1.0.1"
    is-plain-obj "^1.1.0"
    kind-of "^6.0.3"

minimist-options@^3.0.1:
  version "3.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/minimist-options/download/minimist-options-3.0.2.tgz#fba4c8191339e13ecf4d61beb03f070103f3d954"
  integrity sha1-+6TIGRM54T7PTWG+sD8HAQPz2VQ=
  dependencies:
    arrify "^1.0.1"
    is-plain-obj "^1.1.0"

minimist@^1.1.3, minimist@^1.2.0, minimist@^1.2.5, minimist@^1.2.6:
  version "1.2.6"
  resolved "http://npm.devops.xiaohongshu.com:7001/minimist/download/minimist-1.2.6.tgz#8637a5b759ea0d6e98702cfb3a9283323c93af44"
  integrity sha1-hjelt1nqDW6YcCz7OpKDMjyTr0Q=

minipass@^2.3.5, minipass@^2.6.0, minipass@^2.9.0:
  version "2.9.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/minipass/download/minipass-2.9.0.tgz#e713762e7d3e32fed803115cf93e04bca9fcc9a6"
  integrity sha1-5xN2Ln0+Mv7YAxFc+T4EvKn8yaY=
  dependencies:
    safe-buffer "^5.1.2"
    yallist "^3.0.0"

minizlib@^1.3.3:
  version "1.3.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/minizlib/download/minizlib-1.3.3.tgz#2290de96818a34c29551c8a8d301216bd65a861d"
  integrity sha1-IpDeloGKNMKVUcio0wEha9Zahh0=
  dependencies:
    minipass "^2.9.0"

mississippi@^3.0.0:
  version "3.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/mississippi/download/mississippi-3.0.0.tgz#ea0a3291f97e0b5e8776b363d5f0a12d94c67022"
  integrity sha1-6goykfl+C16HdrNj1fChLZTGcCI=
  dependencies:
    concat-stream "^1.5.0"
    duplexify "^3.4.2"
    end-of-stream "^1.1.0"
    flush-write-stream "^1.0.0"
    from2 "^2.1.0"
    parallel-transform "^1.1.0"
    pump "^3.0.0"
    pumpify "^1.3.3"
    stream-each "^1.1.0"
    through2 "^2.0.0"

mitt@^2.1.0:
  version "2.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/mitt/download/mitt-2.1.0.tgz#f740577c23176c6205b121b2973514eade1b2230"
  integrity sha1-90BXfCMXbGIFsSGylzUU6t4bIjA=

mixin-deep@^1.2.0:
  version "1.3.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/mixin-deep/download/mixin-deep-1.3.2.tgz#1120b43dc359a785dce65b55b82e257ccf479566"
  integrity sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=
  dependencies:
    for-in "^1.0.2"
    is-extendable "^1.0.1"

mkdirp-promise@^5.0.1:
  version "5.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/mkdirp-promise/download/mkdirp-promise-5.0.1.tgz#e9b8f68e552c68a9c1713b84883f7a1dd039b8a1"
  integrity sha1-6bj2jlUsaKnBcTuEiD96HdA5uKE=
  dependencies:
    mkdirp "*"

mkdirp@*:
  version "1.0.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/mkdirp/download/mkdirp-1.0.4.tgz#3eb5ed62622756d79a5f0e2a221dfebad75c2f7e"
  integrity sha1-PrXtYmInVteaXw4qIh3+utdcL34=

mkdirp@^0.5.1, mkdirp@^0.5.5:
  version "0.5.6"
  resolved "http://npm.devops.xiaohongshu.com:7001/mkdirp/download/mkdirp-0.5.6.tgz#7def03d2432dcae4ba1d611445c48396062255f6"
  integrity sha1-fe8D0kMtyuS6HWEURcSDlgYiVfY=
  dependencies:
    minimist "^1.2.6"

ml-array-max@^1.2.4:
  version "1.2.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ml-array-max/-/ml-array-max-1.2.4.tgz#2373e2b7e51c8807e456cc0ef364c5863713623b"
  integrity sha512-BlEeg80jI0tW6WaPyGxf5Sa4sqvcyY6lbSn5Vcv44lp1I2GR6AWojfUvLnGTNsIXrZ8uqWmo8VcG1WpkI2ONMQ==
  dependencies:
    is-any-array "^2.0.0"

ml-array-min@^1.2.3:
  version "1.2.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ml-array-min/-/ml-array-min-1.2.3.tgz#662f027c400105816b849cc3cd786915d0801495"
  integrity sha512-VcZ5f3VZ1iihtrGvgfh/q0XlMobG6GQ8FsNyQXD3T+IlstDv85g8kfV0xUG1QPRO/t21aukaJowDzMTc7j5V6Q==
  dependencies:
    is-any-array "^2.0.0"

ml-array-rescale@^1.3.7:
  version "1.3.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ml-array-rescale/-/ml-array-rescale-1.3.7.tgz#c4d129320d113a732e62dd963dc1695bba9a5340"
  integrity sha512-48NGChTouvEo9KBctDfHC3udWnQKNKEWN0ziELvY3KG25GR5cA8K8wNVzracsqSW1QEkAXjTNx+ycgAv06/1mQ==
  dependencies:
    is-any-array "^2.0.0"
    ml-array-max "^1.2.4"
    ml-array-min "^1.2.3"

ml-matrix@^6.5.0:
  version "6.10.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ml-matrix/-/ml-matrix-6.10.4.tgz#babee344b20062d9c123aa801c2e5d0d0c7477f6"
  integrity sha512-rUyEhfNPzqFsltYwvjNeYQXlYEaVea3KgzcJKJteQUj2WVAGFx9fLNRjtMR9mg2B6bd5buxlmkZmxM4hmO+SKg==
  dependencies:
    is-any-array "^2.0.0"
    ml-array-rescale "^1.3.7"

modify-values@^1.0.0:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/modify-values/download/modify-values-1.0.1.tgz#b3939fa605546474e3e3e3c63d64bd43b4ee6022"
  integrity sha1-s5OfpgVUZHTj4+PGPWS9Q7TuYCI=

move-concurrently@^1.0.1:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/move-concurrently/download/move-concurrently-1.0.1.tgz#be2c005fda32e0b29af1f05d7c4b33214c701f92"
  integrity sha1-viwAX9oy4LKa8fBdfEszIUxwH5I=
  dependencies:
    aproba "^1.1.1"
    copy-concurrently "^1.0.0"
    fs-write-stream-atomic "^1.0.8"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"
    run-queue "^1.0.3"

ms@2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/ms/download/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.2:
  version "2.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/ms/download/ms-2.1.2.tgz#d09d1f357b443f493382a8eb3ccd183872ae6009"
  integrity sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=

ms@^2.0.0, ms@^2.1.1:
  version "2.1.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/ms/download/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

multimatch@^3.0.0:
  version "3.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/multimatch/download/multimatch-3.0.0.tgz#0e2534cc6bc238d9ab67e1b9cd5fcd85a6dbf70b"
  integrity sha1-DiU0zGvCONmrZ+G5zV/Nhabb9ws=
  dependencies:
    array-differ "^2.0.3"
    array-union "^1.0.2"
    arrify "^1.0.1"
    minimatch "^3.0.4"

mute-stream@0.0.7:
  version "0.0.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/mute-stream/download/mute-stream-0.0.7.tgz#3075ce93bc21b8fab43e1bc4da7e8115ed1e7bab"
  integrity sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s=

mute-stream@~0.0.4:
  version "0.0.8"
  resolved "http://npm.devops.xiaohongshu.com:7001/mute-stream/download/mute-stream-0.0.8.tgz#1630c42b2251ff81e2a283de96a5497ea92e5e0d"
  integrity sha1-FjDEKyJR/4HiooPelqVJfqkuXg0=

mz@^2.5.0:
  version "2.7.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/mz/download/mz-2.7.0.tgz#95008057a56cafadc2bc63dde7f9ff6955948e32"
  integrity sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI=
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

namespace-emitter@^2.0.1:
  version "2.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/namespace-emitter/download/namespace-emitter-2.0.1.tgz#978d51361c61313b4e6b8cf6f3853d08dfa2b17c"
  integrity sha1-l41RNhxhMTtOa4z284U9CN+isXw=

nanoid@^3.1.25, nanoid@^3.2.0:
  version "3.3.6"
  resolved "http://npm.devops.xiaohongshu.com:7001/nanoid/download/nanoid-3.3.6.tgz#443380c856d6e9f9824267d960b4236ad583ea4c"
  integrity sha1-RDOAyFbW6fmCQmfZYLQjatWD6kw=

nanoid@^3.3.4:
  version "3.3.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/nanoid/download/nanoid-3.3.4.tgz#730b67e3cd09e2deacf03c027c81c9d9dbc5e8ab"
  integrity sha1-cwtn480J4t6s8DwCfIHJ2dvF6Ks=

nanomatch@^1.2.9:
  version "1.2.13"
  resolved "http://npm.devops.xiaohongshu.com:7001/nanomatch/download/nanomatch-1.2.13.tgz#b87a8aa4fc0de8fe6be88895b38983ff265bd119"
  integrity sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    fragment-cache "^0.2.1"
    is-windows "^1.0.2"
    kind-of "^6.0.2"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

negotiator@0.6.3:
  version "0.6.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/negotiator/download/negotiator-0.6.3.tgz#58e323a72fedc0d6f9cd4d31fe49f51479590ccd"
  integrity sha1-WOMjpy/twNb5zU0x/kn1FHlZDM0=

neo-async@^2.6.0:
  version "2.6.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/neo-async/download/neo-async-2.6.2.tgz#b4aafb93e3aeb2d8174ca53cf163ab7d7308305f"
  integrity sha1-tKr7k+OustgXTKU88WOrfXMIMF8=

next-tick@^1.1.0:
  version "1.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/next-tick/download/next-tick-1.1.0.tgz#1836ee30ad56d67ef281b22bd199f709449b35eb"
  integrity sha1-GDbuMK1W1n7ygbIr0Zn3CUSbNes=

nice-try@^1.0.4:
  version "1.0.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/nice-try/download/nice-try-1.0.5.tgz#a3378a7696ce7d223e88fc9b764bd7ef1089e366"
  integrity sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=

no-case@^3.0.4:
  version "3.0.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/no-case/download/no-case-3.0.4.tgz#d361fd5c9800f558551a8369fc0dcd4662b6124d"
  integrity sha1-02H9XJgA9VhVGoNp/A3NRmK2Ek0=
  dependencies:
    lower-case "^2.0.2"
    tslib "^2.0.3"

node-fetch-npm@^2.0.2:
  version "2.0.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/node-fetch-npm/download/node-fetch-npm-2.0.4.tgz#6507d0e17a9ec0be3bec516958a497cec54bf5a4"
  integrity sha1-ZQfQ4XqewL477FFpWKSXzsVL9aQ=
  dependencies:
    encoding "^0.1.11"
    json-parse-better-errors "^1.0.0"
    safe-buffer "^5.1.1"

node-fetch@^2.5.0, node-fetch@^2.6.7:
  version "2.6.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/node-fetch/download/node-fetch-2.6.7.tgz#24de9fba827e3b4ae44dc8b20256a379160052ad"
  integrity sha1-JN6fuoJ+O0rkTciyAlajeRYAUq0=
  dependencies:
    whatwg-url "^5.0.0"

node-fetch@~2.6.9:
  version "2.6.11"
  resolved "http://npm.devops.xiaohongshu.com:7001/node-fetch/download/node-fetch-2.6.11.tgz#cde7fc71deef3131ef80a738919f999e6edfff25"
  integrity sha1-zef8cd7vMTHvgKc4kZ+Znm7f/yU=
  dependencies:
    whatwg-url "^5.0.0"

node-gyp@^5.0.2:
  version "5.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/node-gyp/download/node-gyp-5.1.1.tgz#eb915f7b631c937d282e33aed44cb7a025f62a3e"
  integrity sha1-65Ffe2Mck30oLjOu1Ey3oCX2Kj4=
  dependencies:
    env-paths "^2.2.0"
    glob "^7.1.4"
    graceful-fs "^4.2.2"
    mkdirp "^0.5.1"
    nopt "^4.0.1"
    npmlog "^4.1.2"
    request "^2.88.0"
    rimraf "^2.6.3"
    semver "^5.7.1"
    tar "^4.4.12"
    which "^1.3.1"

node-int64@^0.4.0, node-int64@~0.4.0:
  version "0.4.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/node-int64/download/node-int64-0.4.0.tgz#87a9065cdb355d3182d8f94ce11188b825c68a3b"
  integrity sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=

nopt@^4.0.1:
  version "4.0.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/nopt/download/nopt-4.0.3.tgz#a375cad9d02fd921278d954c2254d5aa57e15e48"
  integrity sha1-o3XK2dAv2SEnjZVMIlTVqlfhXkg=
  dependencies:
    abbrev "1"
    osenv "^0.1.4"

normalize-package-data@^2.0.0, normalize-package-data@^2.3.0, normalize-package-data@^2.3.2, normalize-package-data@^2.3.4, normalize-package-data@^2.3.5, normalize-package-data@^2.4.0, normalize-package-data@^2.5.0:
  version "2.5.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/normalize-package-data/download/normalize-package-data-2.5.0.tgz#e66db1838b200c1dfc233225d12cb36520e234a8"
  integrity sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-package-data@^3.0.0:
  version "3.0.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/normalize-package-data/download/normalize-package-data-3.0.3.tgz#dbcc3e2da59509a0983422884cd172eefdfa525e"
  integrity sha1-28w+LaWVCaCYNCKITNFy7v36Ul4=
  dependencies:
    hosted-git-info "^4.0.1"
    is-core-module "^2.5.0"
    semver "^7.3.4"
    validate-npm-package-license "^3.0.1"

normalize-url@^6.1.0:
  version "6.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/normalize-url/download/normalize-url-6.1.0.tgz#40d0885b535deffe3f3147bec877d05fe4c5668a"
  integrity sha1-QNCIW1Nd7/4/MUe+yHfQX+TFZoo=

npm-bundled@^1.0.1:
  version "1.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/npm-bundled/download/npm-bundled-1.1.2.tgz#944c78789bd739035b70baa2ca5cc32b8d860bc1"
  integrity sha1-lEx4eJvXOQNbcLqiylzDK42GC8E=
  dependencies:
    npm-normalize-package-bin "^1.0.1"

npm-lifecycle@^3.1.2:
  version "3.1.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/npm-lifecycle/download/npm-lifecycle-3.1.5.tgz#9882d3642b8c82c815782a12e6a1bfeed0026309"
  integrity sha1-mILTZCuMgsgVeCoS5qG/7tACYwk=
  dependencies:
    byline "^5.0.0"
    graceful-fs "^4.1.15"
    node-gyp "^5.0.2"
    resolve-from "^4.0.0"
    slide "^1.1.6"
    uid-number "0.0.6"
    umask "^1.1.0"
    which "^1.3.1"

npm-normalize-package-bin@^1.0.0, npm-normalize-package-bin@^1.0.1:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/npm-normalize-package-bin/download/npm-normalize-package-bin-1.0.1.tgz#6e79a41f23fd235c0623218228da7d9c23b8f6e2"
  integrity sha1-bnmkHyP9I1wGIyGCKNp9nCO49uI=

"npm-package-arg@^4.0.0 || ^5.0.0 || ^6.0.0", npm-package-arg@^6.0.0, npm-package-arg@^6.1.0:
  version "6.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/npm-package-arg/download/npm-package-arg-6.1.1.tgz#02168cb0a49a2b75bf988a28698de7b529df5cb7"
  integrity sha1-AhaMsKSaK3W/mIooaY3ntSnfXLc=
  dependencies:
    hosted-git-info "^2.7.1"
    osenv "^0.1.5"
    semver "^5.6.0"
    validate-npm-package-name "^3.0.0"

npm-packlist@^1.4.4:
  version "1.4.8"
  resolved "http://npm.devops.xiaohongshu.com:7001/npm-packlist/download/npm-packlist-1.4.8.tgz#56ee6cc135b9f98ad3d51c1c95da22bbb9b2ef3e"
  integrity sha1-Vu5swTW5+YrT1Rwcldoiu7my7z4=
  dependencies:
    ignore-walk "^3.0.1"
    npm-bundled "^1.0.1"
    npm-normalize-package-bin "^1.0.1"

npm-pick-manifest@^3.0.0:
  version "3.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/npm-pick-manifest/download/npm-pick-manifest-3.0.2.tgz#f4d9e5fd4be2153e5f4e5f9b7be8dc419a99abb7"
  integrity sha1-9Nnl/UviFT5fTl+be+jcQZqZq7c=
  dependencies:
    figgy-pudding "^3.5.1"
    npm-package-arg "^6.0.0"
    semver "^5.4.1"

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/npm-run-path/download/npm-run-path-2.0.2.tgz#35a9232dfa35d7067b4cb2ddf2357b1871536c5f"
  integrity sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=
  dependencies:
    path-key "^2.0.0"

npmlog@^4.1.2:
  version "4.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/npmlog/download/npmlog-4.1.2.tgz#08a7f2a8bf734604779a9efa4ad5cc717abb954b"
  integrity sha1-CKfyqL9zRgR3mp76StXMcXq7lUs=
  dependencies:
    are-we-there-yet "~1.1.2"
    console-control-strings "~1.1.0"
    gauge "~2.7.3"
    set-blocking "~2.0.0"

number-is-nan@^1.0.0:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/number-is-nan/download/number-is-nan-1.0.1.tgz#097b602b53422a522c1afb8790318336941a011d"
  integrity sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=

oauth-sign@~0.9.0:
  version "0.9.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/oauth-sign/download/oauth-sign-0.9.0.tgz#47a7b016baa68b5fa0ecf3dee08a85c679ac6455"
  integrity sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=

object-assign@^4.0.1, object-assign@^4.1.0, object-assign@^4.1.1:
  version "4.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/object-assign/download/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-copy@^0.1.0:
  version "0.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/object-copy/download/object-copy-0.1.0.tgz#7e7d858b781bd7c991a41ba975ed3812754e998c"
  integrity sha1-fn2Fi3gb18mRpBupde04EnVOmYw=
  dependencies:
    copy-descriptor "^0.1.0"
    define-property "^0.2.5"
    kind-of "^3.0.3"

object-inspect@^1.12.0, object-inspect@^1.9.0:
  version "1.12.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/object-inspect/download/object-inspect-1.12.2.tgz#c0641f26394532f28ab8d796ab954e43c009a8ea"
  integrity sha1-wGQfJjlFMvKKuNeWq5VOQ8AJqOo=

object-keys@^1.1.1:
  version "1.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/object-keys/download/object-keys-1.1.1.tgz#1c47f272df277f3b1daf061677d9c82e2322c60e"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object-visit@^1.0.0:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/object-visit/download/object-visit-1.0.1.tgz#f79c4493af0c5377b59fe39d395e41042dd045bb"
  integrity sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=
  dependencies:
    isobject "^3.0.0"

object.assign@^4.1.2:
  version "4.1.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/object.assign/download/object.assign-4.1.4.tgz#9673c7c7c351ab8c4d0b516f4343ebf4dfb7799f"
  integrity sha1-lnPHx8NRq4xNC1FvQ0Pr9N+3eZ8=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    has-symbols "^1.0.3"
    object-keys "^1.1.1"

object.getownpropertydescriptors@^2.0.3:
  version "2.1.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/object.getownpropertydescriptors/download/object.getownpropertydescriptors-2.1.4.tgz#7965e6437a57278b587383831a9b829455a4bc37"
  integrity sha1-eWXmQ3pXJ4tYc4ODGpuClFWkvDc=
  dependencies:
    array.prototype.reduce "^1.0.4"
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.1"

object.pick@^1.3.0:
  version "1.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/object.pick/download/object.pick-1.3.0.tgz#87a10ac4c1694bd2e1cbf53591a66141fb5dd747"
  integrity sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=
  dependencies:
    isobject "^3.0.1"

octokit-pagination-methods@^1.1.0:
  version "1.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/octokit-pagination-methods/download/octokit-pagination-methods-1.1.0.tgz#cf472edc9d551055f9ef73f6e42b4dbb4c80bea4"
  integrity sha1-z0cu3J1VEFX573P25CtNu0yAvqQ=

on-finished@^2.3.0:
  version "2.4.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/on-finished/download/on-finished-2.4.1.tgz#58c8c44116e54845ad57f14ab10b03533184ac3f"
  integrity sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=
  dependencies:
    ee-first "1.1.1"

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/once/download/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^2.0.0:
  version "2.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/onetime/download/onetime-2.0.1.tgz#067428230fd67443b2794b22bba528b6867962d4"
  integrity sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=
  dependencies:
    mimic-fn "^1.0.0"

only@~0.0.2:
  version "0.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/only/download/only-0.0.2.tgz#2afde84d03e50b9a8edc444e30610a70295edfb4"
  integrity sha1-Kv3oTQPlC5qO3EROMGEKcCle37Q=

os-homedir@^1.0.0:
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/os-homedir/download/os-homedir-1.0.2.tgz#ffbc4988336e0e833de0c168c7ef152121aa7fb3"
  integrity sha1-/7xJiDNuDoM94MFox+8VISGqf7M=

os-name@^3.1.0:
  version "3.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/os-name/download/os-name-3.1.0.tgz#dec19d966296e1cd62d701a5a66ee1ddeae70801"
  integrity sha1-3sGdlmKW4c1i1wGlpm7h3ernCAE=
  dependencies:
    macos-release "^2.2.0"
    windows-release "^3.1.0"

os-tmpdir@^1.0.0, os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/os-tmpdir/download/os-tmpdir-1.0.2.tgz#bbe67406c79aa85c5cfec766fe5734555dfa1274"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

osenv@^0.1.4, osenv@^0.1.5:
  version "0.1.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/osenv/download/osenv-0.1.5.tgz#85cdfafaeb28e8677f416e287592b5f3f49ea410"
  integrity sha1-hc36+uso6Gd/QW4odZK18/SepBA=
  dependencies:
    os-homedir "^1.0.0"
    os-tmpdir "^1.0.0"

p-finally@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/p-finally/download/p-finally-1.0.0.tgz#3fbcfb15b899a44123b34b6dcc18b724336a2cae"
  integrity sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=

p-limit@^1.1.0:
  version "1.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/p-limit/download/p-limit-1.3.0.tgz#b86bd5f0c25690911c7590fcbfc2010d54b3ccb8"
  integrity sha1-uGvV8MJWkJEcdZD8v8IBDVSzzLg=
  dependencies:
    p-try "^1.0.0"

p-limit@^2.0.0, p-limit@^2.2.0:
  version "2.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/p-limit/download/p-limit-2.3.0.tgz#3dd33c647a214fdfffd835933eb086da0dc21db1"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-locate@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/p-locate/download/p-locate-2.0.0.tgz#20a0103b222a70c8fd39cc2e580680f3dde5ec43"
  integrity sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=
  dependencies:
    p-limit "^1.1.0"

p-locate@^3.0.0:
  version "3.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/p-locate/download/p-locate-3.0.0.tgz#322d69a05c0264b25997d9f40cd8a891ab0064a4"
  integrity sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=
  dependencies:
    p-limit "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/p-locate/download/p-locate-4.1.0.tgz#a3428bb7088b3a60292f66919278b7c297ad4f07"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-map-series@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/p-map-series/download/p-map-series-1.0.0.tgz#bf98fe575705658a9e1351befb85ae4c1f07bdca"
  integrity sha1-v5j+V1cFZYqeE1G++4WuTB8Hvco=
  dependencies:
    p-reduce "^1.0.0"

p-map@^2.1.0:
  version "2.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/p-map/download/p-map-2.1.0.tgz#310928feef9c9ecc65b68b17693018a665cea175"
  integrity sha1-MQko/u+cnsxltosXaTAYpmXOoXU=

p-pipe@^1.2.0:
  version "1.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/p-pipe/download/p-pipe-1.2.0.tgz#4b1a11399a11520a67790ee5a0c1d5881d6befe9"
  integrity sha1-SxoROZoRUgpneQ7loMHViB1r7+k=

p-queue@^4.0.0:
  version "4.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/p-queue/download/p-queue-4.0.0.tgz#ed0eee8798927ed6f2c2f5f5b77fdb2061a5d346"
  integrity sha1-7Q7uh5iSftbywvX1t3/bIGGl00Y=
  dependencies:
    eventemitter3 "^3.1.0"

p-reduce@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/p-reduce/download/p-reduce-1.0.0.tgz#18c2b0dd936a4690a529f8231f58a0fdb6a47dfa"
  integrity sha1-GMKw3ZNqRpClKfgjH1ig/bakffo=

p-try@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/p-try/download/p-try-1.0.0.tgz#cbc79cdbaf8fd4228e13f621f2b1a237c1b207b3"
  integrity sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=

p-try@^2.0.0:
  version "2.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/p-try/download/p-try-2.2.0.tgz#cb2868540e313d61de58fafbe35ce9004d5540e6"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

p-waterfall@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/p-waterfall/download/p-waterfall-1.0.0.tgz#7ed94b3ceb3332782353af6aae11aa9fc235bb00"
  integrity sha1-ftlLPOszMngjU69qrhGqn8I1uwA=
  dependencies:
    p-reduce "^1.0.0"

parallel-transform@^1.1.0:
  version "1.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/parallel-transform/download/parallel-transform-1.2.0.tgz#9049ca37d6cb2182c3b1d2c720be94d14a5814fc"
  integrity sha1-kEnKN9bLIYLDsdLHIL6U0UpYFPw=
  dependencies:
    cyclist "^1.0.1"
    inherits "^2.0.3"
    readable-stream "^2.1.5"

param-case@^3.0.4:
  version "3.0.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/param-case/download/param-case-3.0.4.tgz#7d17fe4aa12bde34d4a77d91acfb6219caad01c5"
  integrity sha1-fRf+SqEr3jTUp32RrPtiGcqtAcU=
  dependencies:
    dot-case "^3.0.4"
    tslib "^2.0.3"

parse-github-repo-url@^1.3.0:
  version "1.4.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/parse-github-repo-url/download/parse-github-repo-url-1.4.1.tgz#9e7d8bb252a6cb6ba42595060b7bf6df3dbc1f50"
  integrity sha1-nn2LslKmy2ukJZUGC3v23z28H1A=

parse-json@^2.2.0:
  version "2.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/parse-json/download/parse-json-2.2.0.tgz#f480f40434ef80741f8469099f8dea18f55a4dc9"
  integrity sha1-9ID0BDTvgHQfhGkJn43qGPVaTck=
  dependencies:
    error-ex "^1.2.0"

parse-json@^4.0.0:
  version "4.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/parse-json/download/parse-json-4.0.0.tgz#be35f5425be1f7f6c747184f98a788cb99477ee0"
  integrity sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=
  dependencies:
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"

parse-json@^5.0.0:
  version "5.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/parse-json/download/parse-json-5.2.0.tgz#c76fc66dee54231c962b22bcc8a72cf2f99753cd"
  integrity sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse-path@^4.0.0:
  version "4.0.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/parse-path/download/parse-path-4.0.4.tgz#4bf424e6b743fb080831f03b536af9fc43f0ffea"
  integrity sha1-S/Qk5rdD+wgIMfA7U2r5/EPw/+o=
  dependencies:
    is-ssh "^1.3.0"
    protocols "^1.4.0"
    qs "^6.9.4"
    query-string "^6.13.8"

parse-url@^6.0.0:
  version "6.0.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/parse-url/download/parse-url-6.0.5.tgz#4acab8982cef1846a0f8675fa686cef24b2f6f9b"
  integrity sha1-Ssq4mCzvGEag+GdfpobO8ksvb5s=
  dependencies:
    is-ssh "^1.3.0"
    normalize-url "^6.1.0"
    parse-path "^4.0.0"
    protocols "^1.4.0"

parseurl@^1.3.2:
  version "1.3.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/parseurl/download/parseurl-1.3.3.tgz#9da19e7bee8d12dff0513ed5b76957793bc2e8d4"
  integrity sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=

pascal-case@^3.1.1, pascal-case@^3.1.2:
  version "3.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/pascal-case/download/pascal-case-3.1.2.tgz#b48e0ef2b98e205e7c1dae747d0b1508237660eb"
  integrity sha1-tI4O8rmOIF58Ha50fQsVCCN2YOs=
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

pascalcase@^0.1.1:
  version "0.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/pascalcase/download/pascalcase-0.1.1.tgz#b363e55e8006ca6fe21784d2db22bd15d7917f14"
  integrity sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=

path-dirname@^1.0.0:
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/path-dirname/download/path-dirname-1.0.2.tgz#cc33d24d525e099a5388c0336c6e32b9160609e0"
  integrity sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=

path-exists@^2.0.0:
  version "2.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/path-exists/download/path-exists-2.1.0.tgz#0feb6c64f0fc518d9a754dd5efb62c7022761f4b"
  integrity sha1-D+tsZPD8UY2adU3V77YscCJ2H0s=
  dependencies:
    pinkie-promise "^2.0.0"

path-exists@^3.0.0:
  version "3.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/path-exists/download/path-exists-3.0.0.tgz#ce0ebeaa5f78cb18925ea7d810d7b59b010fd515"
  integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/path-exists/download/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@1.0.1, path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/path-is-absolute/download/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^2.0.0, path-key@^2.0.1:
  version "2.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/path-key/download/path-key-2.0.1.tgz#411cadb574c5a140d3a4b1910d40d80cc9f40b40"
  integrity sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=

path-parse@^1.0.7:
  version "1.0.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/path-parse/download/path-parse-1.0.7.tgz#fbc114b60ca42b30d9daf5858e4bd68bbedb6735"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

path-type@^1.0.0:
  version "1.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/path-type/download/path-type-1.1.0.tgz#59c44f7ee491da704da415da5a4070ba4f8fe441"
  integrity sha1-WcRPfuSR2nBNpBXaWkBwuk+P5EE=
  dependencies:
    graceful-fs "^4.1.2"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

path-type@^3.0.0:
  version "3.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/path-type/download/path-type-3.0.0.tgz#cef31dc8e0a1a3bb0d105c0cd97cf3bf47f4e36f"
  integrity sha1-zvMdyOCho7sNEFwM2Xzzv0f0428=
  dependencies:
    pify "^3.0.0"

peek-readable@^4.1.0:
  version "4.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/peek-readable/download/peek-readable-4.1.0.tgz#4ece1111bf5c2ad8867c314c81356847e8a62e72"
  integrity sha1-Ts4REb9cKtiGfDFMgTVoR+imLnI=

performance-now@^2.1.0:
  version "2.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/performance-now/download/performance-now-2.1.0.tgz#6309f4e0e5fa913ec1c69307ae364b4b377c9e7b"
  integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=

picocolors@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/picocolors/download/picocolors-1.0.0.tgz#cb5bdc74ff3f51892236eaf79d68bc44564ab81c"
  integrity sha1-y1vcdP8/UYkiNur3nWi8RFZKuBw=

pify@^2.0.0, pify@^2.3.0:
  version "2.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/pify/download/pify-2.3.0.tgz#ed141a6ac043a849ea588498e7dca8b15330e90c"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pify@^3.0.0:
  version "3.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/pify/download/pify-3.0.0.tgz#e5a4acd2c101fdf3d9a4d07f0dbc4db49dd28176"
  integrity sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=

pify@^4.0.1:
  version "4.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/pify/download/pify-4.0.1.tgz#4b2cd25c50d598735c50292224fd8c6df41e3231"
  integrity sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=

pinkie-promise@^2.0.0:
  version "2.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/pinkie-promise/download/pinkie-promise-2.0.1.tgz#2135d6dfa7a358c069ac9b178776288228450ffa"
  integrity sha1-ITXW36ejWMBprJsXh3YogihFD/o=
  dependencies:
    pinkie "^2.0.0"

pinkie@^2.0.0:
  version "2.0.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/pinkie/download/pinkie-2.0.4.tgz#72556b80cfa0d48a974e80e77248e80ed4f7f870"
  integrity sha1-clVrgM+g1IqXToDnckjoDtT3+HA=

pkg-dir@^3.0.0:
  version "3.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/pkg-dir/download/pkg-dir-3.0.0.tgz#2749020f239ed990881b1f71210d51eb6523bea3"
  integrity sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM=
  dependencies:
    find-up "^3.0.0"

posix-character-classes@^0.1.0:
  version "0.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/posix-character-classes/download/posix-character-classes-0.1.1.tgz#01eac0fe3b5af71a2a6c02feabb8c1fef7e00eab"
  integrity sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=

postcss@^8.1.10:
  version "8.4.17"
  resolved "http://npm.devops.xiaohongshu.com:7001/postcss/download/postcss-8.4.17.tgz#f87863ec7cd353f81f7ab2dec5d67d861bbb1be5"
  integrity sha1-+Hhj7HzTU/gferLexdZ9hhu7G+U=
  dependencies:
    nanoid "^3.3.4"
    picocolors "^1.0.0"
    source-map-js "^1.0.2"

preact@^10.5.13:
  version "10.15.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/preact/download/preact-10.15.1.tgz#a1de60c9fc0c79a522d969c65dcaddc5d994eede"
  integrity sha1-od5gyfwMeaUi2WnGXcrdxdmU7t4=

prismjs@^1.23.0:
  version "1.29.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/prismjs/download/prismjs-1.29.0.tgz#f113555a8fa9b57c35e637bba27509dcf802dd12"
  integrity sha1-8RNVWo+ptXw15je7onUJ3PgC3RI=

probe.gl@^3.1.1:
  version "3.6.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/probe.gl/-/probe.gl-3.6.0.tgz#e816234412b27a70b9be029cb82c8cf96cd72659"
  integrity sha512-19JydJWI7+DtR4feV+pu4Mn1I5TAc0xojuxVgZdXIyfmTLfUaFnk4OloWK1bKbPtkgGKLr2lnbnCXmpZEcEp9g==
  dependencies:
    "@babel/runtime" "^7.0.0"
    "@probe.gl/env" "3.6.0"
    "@probe.gl/log" "3.6.0"
    "@probe.gl/stats" "3.6.0"

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/process-nextick-args/download/process-nextick-args-2.0.1.tgz#7820d9b16120cc55ca9ae7792680ae7dba6d7fe2"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

prom-client@~14.1.1:
  version "14.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/prom-client/-/prom-client-14.1.1.tgz#e9bebef0e2269bfde22a322f4ca803cb52b4a0c0"
  integrity sha512-hFU32q7UZQ59bVJQGUtm3I2PrJ3gWvoCkilX9sF165ks1qflhugVCeK+S1JjJYHvyt3o5kj68+q3bchormjnzw==
  dependencies:
    tdigest "^0.1.1"

promise-inflight@^1.0.1:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/promise-inflight/download/promise-inflight-1.0.1.tgz#98472870bf228132fcbdd868129bad12c3c029e3"
  integrity sha1-mEcocL8igTL8vdhoEputEsPAKeM=

promise-retry@^1.1.1:
  version "1.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/promise-retry/download/promise-retry-1.1.1.tgz#6739e968e3051da20ce6497fb2b50f6911df3d6d"
  integrity sha1-ZznpaOMFHaIM5kl/srUPaRHfPW0=
  dependencies:
    err-code "^1.0.0"
    retry "^0.10.0"

promzard@^0.3.0:
  version "0.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/promzard/download/promzard-0.3.0.tgz#26a5d6ee8c7dee4cb12208305acfb93ba382a9ee"
  integrity sha1-JqXW7ox97kyxIggwWs+5O6OCqe4=
  dependencies:
    read "1"

prop-types@^15.7.2, prop-types@^15.8.1:
  version "15.8.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/prop-types/download/prop-types-15.8.1.tgz#67d87bf1a694f48435cf332c24af10214a3140b5"
  integrity sha1-Z9h78aaU9IQ1zzMsJK8QIUoxQLU=
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

property-expr@^1.5.0:
  version "1.5.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/property-expr/download/property-expr-1.5.1.tgz#22e8706894a0c8e28d58735804f6ba3a3673314f"
  integrity sha1-IuhwaJSgyOKNWHNYBPa6OjZzMU8=

proto-list@~1.2.1:
  version "1.2.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/proto-list/download/proto-list-1.2.4.tgz#212d5bfe1318306a420f6402b8e26ff39647a849"
  integrity sha1-IS1b/hMYMGpCD2QCuOJv85ZHqEk=

protocols@^1.4.0:
  version "1.4.8"
  resolved "http://npm.devops.xiaohongshu.com:7001/protocols/download/protocols-1.4.8.tgz#48eea2d8f58d9644a4a32caae5d5db290a075ce8"
  integrity sha1-SO6i2PWNlkSkoyyq5dXbKQoHXOg=

protocols@^2.0.1:
  version "2.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/protocols/download/protocols-2.0.1.tgz#8f155da3fc0f32644e83c5782c8e8212ccf70a86"
  integrity sha1-jxVdo/wPMmROg8V4LI6CEsz3CoY=

protoduck@^5.0.1:
  version "5.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/protoduck/download/protoduck-5.0.1.tgz#03c3659ca18007b69a50fd82a7ebcc516261151f"
  integrity sha1-A8NlnKGAB7aaUP2Cp+vMUWJhFR8=
  dependencies:
    genfun "^5.0.0"

proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/proxy-from-env/download/proxy-from-env-1.1.0.tgz#e102f16ca355424865755d2c9e8ea4f24d58c3e2"
  integrity sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=

psl@^1.1.28:
  version "1.9.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/psl/download/psl-1.9.0.tgz#d0df2a137f00794565fcaf3b2c00cd09f8d5a5a7"
  integrity sha1-0N8qE38AeUVl/K87LADNCfjVpac=

pump@^2.0.0:
  version "2.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/pump/download/pump-2.0.1.tgz#12399add6e4cf7526d973cbc8b5ce2e2908b3909"
  integrity sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pump@^3.0.0:
  version "3.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/pump/download/pump-3.0.0.tgz#b4a2116815bde2f4e1ea602354e8c75565107a64"
  integrity sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pumpify@^1.3.3:
  version "1.5.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/pumpify/download/pumpify-1.5.1.tgz#36513be246ab27570b1a374a5ce278bfd74370ce"
  integrity sha1-NlE74karJ1cLGjdKXOJ4v9dDcM4=
  dependencies:
    duplexify "^3.6.0"
    inherits "^2.0.3"
    pump "^2.0.0"

punycode.js@^2.3.1:
  version "2.3.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/punycode.js/-/punycode.js-2.3.1.tgz#6b53e56ad75588234e79f4affa90972c7dd8cdb7"
  integrity sha512-uxFIHU0YlHYhDQtV4R9J6a52SLx28BCjT+4ieh7IGbgwVJWO+km431c4yRlREUAsAmt/uMjQUyQHNEPf0M39CA==

punycode@1.3.2:
  version "1.3.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/punycode/download/punycode-1.3.2.tgz#9653a036fb7c1ee42342f2325cceefea3926c48d"
  integrity sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0=

punycode@^2.1.0, punycode@^2.1.1:
  version "2.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/punycode/download/punycode-2.1.1.tgz#b58b010ac40c22c5657616c8d2c2c02c7bf479ec"
  integrity sha1-tYsBCsQMIsVldhbI0sLALHv0eew=

q@^1.5.0, q@^1.5.1:
  version "1.5.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/q/download/q-1.5.1.tgz#7e32f75b41381291d04611f1bf14109ac00651d7"
  integrity sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc=

qiankun@^2.7.3:
  version "2.8.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/qiankun/download/qiankun-2.8.0.tgz#b507e1258a997f3c7f56995eb2bfd8c5a671e307"
  integrity sha1-tQfhJYqZfzx/Vplesr/YxaZx4wc=
  dependencies:
    "@babel/runtime" "^7.10.5"
    import-html-entry "^1.14.0"
    lodash "^4.17.11"
    single-spa "^5.9.2"

qiniu-js@^3.1.0:
  version "3.4.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/qiniu-js/download/qiniu-js-3.4.1.tgz#0d258771164fac5d92df1797d1c55e38db934a57"
  integrity sha1-DSWHcRZPrF2S3xeX0cVeONuTSlc=
  dependencies:
    "@babel/runtime-corejs2" "^7.10.2"
    querystring "^0.2.1"
    spark-md5 "^3.0.0"

qrious@^4.0.2:
  version "4.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/qrious/download/qrious-4.0.2.tgz#09c4d4079d2b961617f62c69cff3b9bb66a39693"
  integrity sha1-CcTUB50rlhYX9ixpz/O5u2ajlpM=

qs@^6.5.2, qs@^6.9.4:
  version "6.11.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/qs/download/qs-6.11.0.tgz#fd0d963446f7a65e1367e01abd85429453f0c37a"
  integrity sha1-/Q2WNEb3pl4TZ+AavYVClFPww3o=
  dependencies:
    side-channel "^1.0.4"

qs@~6.5.2:
  version "6.5.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/qs/download/qs-6.5.3.tgz#3aeeffc91967ef6e35c0e488ef46fb296ab76aad"
  integrity sha1-Ou7/yRln7241wOSI70b7KWq3aq0=

query-string@^6.13.8:
  version "6.14.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/query-string/download/query-string-6.14.1.tgz#7ac2dca46da7f309449ba0f86b1fd28255b0c86a"
  integrity sha1-esLcpG2n8wlEm6D4ax/SglWwyGo=
  dependencies:
    decode-uri-component "^0.2.0"
    filter-obj "^1.1.0"
    split-on-first "^1.0.0"
    strict-uri-encode "^2.0.0"

querystring@0.2.0:
  version "0.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/querystring/download/querystring-0.2.0.tgz#b209849203bb25df820da756e747005878521620"
  integrity sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA=

querystring@^0.2.1:
  version "0.2.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/querystring/download/querystring-0.2.1.tgz#40d77615bb09d16902a85c3e38aa8b5ed761c2dd"
  integrity sha1-QNd2FbsJ0WkCqFw+OKqLXtdhwt0=

querystringify@^2.1.1:
  version "2.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/querystringify/download/querystringify-2.2.0.tgz#3345941b4153cb9d082d8eee4cda2016a9aef7f6"
  integrity sha1-M0WUG0FTy50ILY7uTNogFqmu9/Y=

quick-lru@^1.0.0:
  version "1.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/quick-lru/download/quick-lru-1.1.0.tgz#4360b17c61136ad38078397ff11416e186dcfbb8"
  integrity sha1-Q2CxfGETatOAeDl/8RQW4Ybc+7g=

quick-lru@^4.0.1:
  version "4.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/quick-lru/download/quick-lru-4.0.1.tgz#5b8878f113a58217848c6482026c73e1ba57727f"
  integrity sha1-W4h48ROlgheEjGSCAmxz4bpXcn8=

randombytes@^2.1.0:
  version "2.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/randombytes/download/randombytes-2.1.0.tgz#df6f84372f0270dc65cdf6291349ab7a473d4f2a"
  integrity sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=
  dependencies:
    safe-buffer "^5.1.0"

raw-body@^2.3.3:
  version "2.5.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/raw-body/download/raw-body-2.5.1.tgz#fe1b1628b181b700215e5fd42389f98b71392857"
  integrity sha1-/hsWKLGBtwAhXl/UI4n5i3E5KFc=
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

react-is@^16.13.1:
  version "16.13.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/react-is/download/react-is-16.13.1.tgz#789729a4dc36de2999dc156dd6c1d9c18cea56a4"
  integrity sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=

read-cmd-shim@^1.0.1:
  version "1.0.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/read-cmd-shim/download/read-cmd-shim-1.0.5.tgz#87e43eba50098ba5a32d0ceb583ab8e43b961c16"
  integrity sha1-h+Q+ulAJi6WjLQzrWDq45DuWHBY=
  dependencies:
    graceful-fs "^4.1.2"

"read-package-json@1 || 2", read-package-json@^2.0.0, read-package-json@^2.0.13:
  version "2.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/read-package-json/download/read-package-json-2.1.2.tgz#6992b2b66c7177259feb8eaac73c3acd28b9222a"
  integrity sha1-aZKytmxxdyWf646qxzw6zSi5Iio=
  dependencies:
    glob "^7.1.1"
    json-parse-even-better-errors "^2.3.0"
    normalize-package-data "^2.0.0"
    npm-normalize-package-bin "^1.0.0"

read-package-tree@^5.1.6:
  version "5.3.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/read-package-tree/download/read-package-tree-5.3.1.tgz#a32cb64c7f31eb8a6f31ef06f9cedf74068fe636"
  integrity sha1-oyy2TH8x64pvMe8G+c7fdAaP5jY=
  dependencies:
    read-package-json "^2.0.0"
    readdir-scoped-modules "^1.0.0"
    util-promisify "^2.1.0"

read-pkg-up@^1.0.1:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/read-pkg-up/download/read-pkg-up-1.0.1.tgz#9d63c13276c065918d57f002a57f40a1b643fb02"
  integrity sha1-nWPBMnbAZZGNV/ACpX9AobZD+wI=
  dependencies:
    find-up "^1.0.0"
    read-pkg "^1.0.0"

read-pkg-up@^3.0.0:
  version "3.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/read-pkg-up/download/read-pkg-up-3.0.0.tgz#3ed496685dba0f8fe118d0691dc51f4a1ff96f07"
  integrity sha1-PtSWaF26D4/hGNBpHcUfSh/5bwc=
  dependencies:
    find-up "^2.0.0"
    read-pkg "^3.0.0"

read-pkg-up@^7.0.1:
  version "7.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/read-pkg-up/download/read-pkg-up-7.0.1.tgz#f3a6135758459733ae2b95638056e1854e7ef507"
  integrity sha1-86YTV1hFlzOuK5VjgFbhhU5+9Qc=
  dependencies:
    find-up "^4.1.0"
    read-pkg "^5.2.0"
    type-fest "^0.8.1"

read-pkg@^1.0.0:
  version "1.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/read-pkg/download/read-pkg-1.1.0.tgz#f5ffaa5ecd29cb31c0474bca7d756b6bb29e3f28"
  integrity sha1-9f+qXs0pyzHAR0vKfXVra7KePyg=
  dependencies:
    load-json-file "^1.0.0"
    normalize-package-data "^2.3.2"
    path-type "^1.0.0"

read-pkg@^3.0.0:
  version "3.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/read-pkg/download/read-pkg-3.0.0.tgz#9cbc686978fee65d16c00e2b19c237fcf6e38389"
  integrity sha1-nLxoaXj+5l0WwA4rGcI3/Pbjg4k=
  dependencies:
    load-json-file "^4.0.0"
    normalize-package-data "^2.3.2"
    path-type "^3.0.0"

read-pkg@^5.2.0:
  version "5.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/read-pkg/download/read-pkg-5.2.0.tgz#7bf295438ca5a33e56cd30e053b34ee7250c93cc"
  integrity sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w=
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    normalize-package-data "^2.5.0"
    parse-json "^5.0.0"
    type-fest "^0.6.0"

read@1, read@~1.0.1:
  version "1.0.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/read/download/read-1.0.7.tgz#b3da19bd052431a97671d44a42634adf710b40c4"
  integrity sha1-s9oZvQUkMal2cdRKQmNK33ELQMQ=
  dependencies:
    mute-stream "~0.0.4"

"readable-stream@1 || 2", readable-stream@^2.0.0, readable-stream@^2.0.6, readable-stream@^2.1.5, readable-stream@^2.2.2, readable-stream@^2.3.6, readable-stream@~2.3.6:
  version "2.3.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/readable-stream/download/readable-stream-2.3.7.tgz#1eca1cf711aef814c04f62252a36a62f6cb23b57"
  integrity sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

"readable-stream@2 || 3", readable-stream@3, readable-stream@^3.0.0, readable-stream@^3.0.2:
  version "3.6.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/readable-stream/download/readable-stream-3.6.0.tgz#337bbda3adc0706bd3e024426a286d4b4b2c9198"
  integrity sha1-M3u9o63AcGvT4CRCaihtS0sskZg=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-web-to-node-stream@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/readable-web-to-node-stream/download/readable-web-to-node-stream-2.0.0.tgz#751e632f466552ac0d5c440cc01470352f93c4b7"
  integrity sha1-dR5jL0ZlUqwNXEQMwBRwNS+TxLc=

readdir-scoped-modules@^1.0.0:
  version "1.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/readdir-scoped-modules/download/readdir-scoped-modules-1.1.0.tgz#8d45407b4f870a0dcaebc0e28670d18e74514309"
  integrity sha1-jUVAe0+HCg3K68DihnDRjnRRQwk=
  dependencies:
    debuglog "^1.0.1"
    dezalgo "^1.0.0"
    graceful-fs "^4.1.2"
    once "^1.3.0"

redent@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/redent/download/redent-1.0.0.tgz#cf916ab1fd5f1f16dfb20822dd6ec7f730c2afde"
  integrity sha1-z5Fqsf1fHxbfsggi3W7H9zDCr94=
  dependencies:
    indent-string "^2.1.0"
    strip-indent "^1.0.1"

redent@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/redent/download/redent-2.0.0.tgz#c1b2007b42d57eb1389079b3c8333639d5e1ccaa"
  integrity sha1-wbIAe0LVfrE4kHmzyDM2OdXhzKo=
  dependencies:
    indent-string "^3.0.0"
    strip-indent "^2.0.0"

redent@^3.0.0:
  version "3.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/redent/download/redent-3.0.0.tgz#e557b7998316bb53c9f1f56fa626352c6963059f"
  integrity sha1-5Ve3mYMWu1PJ8fVvpiY1LGljBZ8=
  dependencies:
    indent-string "^4.0.0"
    strip-indent "^3.0.0"

reflect-metadata@~0.1.13:
  version "0.1.13"
  resolved "http://npm.devops.xiaohongshu.com:7001/reflect-metadata/download/reflect-metadata-0.1.13.tgz#67ae3ca57c972a2aa1642b10fe363fe32d49dc08"
  integrity sha1-Z648pXyXKiqhZCsQ/jY/4y1J3Ag=

regenerator-runtime@^0.13.11:
  version "0.13.11"
  resolved "http://npm.devops.xiaohongshu.com:7001/regenerator-runtime/download/regenerator-runtime-0.13.11.tgz#f6dca3e7ceec20590d07ada785636a90cdca17f9"
  integrity sha1-9tyj587sIFkNB62nhWNqkM3KF/k=

regenerator-runtime@^0.13.4:
  version "0.13.9"
  resolved "http://npm.devops.xiaohongshu.com:7001/regenerator-runtime/download/regenerator-runtime-0.13.9.tgz#8925742a98ffd90814988d7566ad30ca3b263b52"
  integrity sha1-iSV0Kpj/2QgUmI11Zq0wyjsmO1I=

regex-not@^1.0.0, regex-not@^1.0.2:
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/regex-not/download/regex-not-1.0.2.tgz#1f4ece27e00b0b65e0247a6810e6a85d83a5752c"
  integrity sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=
  dependencies:
    extend-shallow "^3.0.2"
    safe-regex "^1.1.0"

regexp.prototype.flags@^1.4.3:
  version "1.4.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/regexp.prototype.flags/download/regexp.prototype.flags-1.4.3.tgz#87cab30f80f66660181a3bb7bf5981a872b367ac"
  integrity sha1-h8qzD4D2ZmAYGju3v1mBqHKzZ6w=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    functions-have-names "^1.2.2"

regl@^1.3.11:
  version "1.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/regl/-/regl-1.7.0.tgz#0d185431044a356bf80e9b775b11b935ef2746d3"
  integrity sha512-bEAtp/qrtKucxXSJkD4ebopFZYP0q1+3Vb2WECWv/T8yQEgKxDxJ7ztO285tAMaYZVR6mM1GgI6CCn8FROtL1w==

repeat-element@^1.1.2:
  version "1.1.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/repeat-element/download/repeat-element-1.1.4.tgz#be681520847ab58c7568ac75fbfad28ed42d39e9"
  integrity sha1-vmgVIIR6tYx1aKx1+/rSjtQtOek=

repeat-string@^1.6.1:
  version "1.6.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/repeat-string/download/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637"
  integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=

repeating@^2.0.0:
  version "2.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/repeating/download/repeating-2.0.1.tgz#5214c53a926d3552707527fbab415dbc08d06dda"
  integrity sha1-UhTFOpJtNVJwdSf7q0FdvAjQbdo=
  dependencies:
    is-finite "^1.0.0"

request@^2.88.0:
  version "2.88.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/request/download/request-2.88.2.tgz#d73c918731cb5a87da047e207234146f664d12b3"
  integrity sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=
  dependencies:
    aws-sign2 "~0.7.0"
    aws4 "^1.8.0"
    caseless "~0.12.0"
    combined-stream "~1.0.6"
    extend "~3.0.2"
    forever-agent "~0.6.1"
    form-data "~2.3.2"
    har-validator "~5.1.3"
    http-signature "~1.2.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.19"
    oauth-sign "~0.9.0"
    performance-now "^2.1.0"
    qs "~6.5.2"
    safe-buffer "^5.1.2"
    tough-cookie "~2.5.0"
    tunnel-agent "^0.6.0"
    uuid "^3.3.2"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/require-directory/download/require-directory-2.1.1.tgz#8c64ad5fd30dab1c976e2344ffe7f792a6a6df42"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/require-from-string/download/require-from-string-2.0.2.tgz#89a7fdd938261267318eafe14f9c32e598c36909"
  integrity sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=

require-main-filename@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/require-main-filename/download/require-main-filename-2.0.0.tgz#d0b329ecc7cc0f61649f62215be69af54aa8989b"
  integrity sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs=

requires-port@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/requires-port/download/requires-port-1.0.0.tgz#925d2601d39ac485e091cf0da5c6e694dc3dcaff"
  integrity sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=

resize-observer-polyfill@^1.5.1:
  version "1.5.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/resize-observer-polyfill/download/resize-observer-polyfill-1.5.1.tgz#0e9020dd3d21024458d4ebd27e23e40269810464"
  integrity sha1-DpAg3T0hAkRY1OvSfiPkAmmBBGQ=

resolve-cwd@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/resolve-cwd/download/resolve-cwd-2.0.0.tgz#00a9f7387556e27038eae232caa372a6a59b665a"
  integrity sha1-AKn3OHVW4nA46uIyyqNypqWbZlo=
  dependencies:
    resolve-from "^3.0.0"

resolve-from@^3.0.0:
  version "3.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/resolve-from/download/resolve-from-3.0.0.tgz#b22c7af7d9d6881bc8b6e653335eebcb0a188748"
  integrity sha1-six699nWiBvItuZTM17rywoYh0g=

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/resolve-from/download/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-path@^1.4.0:
  version "1.4.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/resolve-path/download/resolve-path-1.4.0.tgz#c4bda9f5efb2fce65247873ab36bb4d834fe16f7"
  integrity sha1-xL2p9e+y/OZSR4c6s2u02DT+Fvc=
  dependencies:
    http-errors "~1.6.2"
    path-is-absolute "1.0.1"

resolve-url@^0.2.1:
  version "0.2.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/resolve-url/download/resolve-url-0.2.1.tgz#2c637fe77c893afd2a663fe21aa9080068e2052a"
  integrity sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=

resolve@^1.10.0:
  version "1.22.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/resolve/download/resolve-1.22.1.tgz#27cb2ebb53f91abb49470a928bba7558066ac177"
  integrity sha1-J8suu1P5GrtJRwqSi7p1WAZqwXc=
  dependencies:
    is-core-module "^2.9.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/restore-cursor/download/restore-cursor-2.0.0.tgz#9f7ee287f82fd326d4fd162923d62129eee0dfaf"
  integrity sha1-n37ih/gv0ybU/RYpI9YhKe7g368=
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

ret@~0.1.10:
  version "0.1.15"
  resolved "http://npm.devops.xiaohongshu.com:7001/ret/download/ret-0.1.15.tgz#b8a4825d5bdb1fc3f6f53c2bc33f81388681c7bc"
  integrity sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=

ret@~0.2.0:
  version "0.2.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/ret/download/ret-0.2.2.tgz#b6861782a1f4762dce43402a71eb7a283f44573c"
  integrity sha1-toYXgqH0di3OQ0Aqcet6KD9EVzw=

retry@^0.10.0:
  version "0.10.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/retry/download/retry-0.10.1.tgz#e76388d217992c252750241d3d3956fed98d8ff4"
  integrity sha1-52OI0heZLCUnUCQdPTlW/tmNj/Q=

rimraf@^2.5.4, rimraf@^2.6.2, rimraf@^2.6.3:
  version "2.7.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/rimraf/download/rimraf-2.7.1.tgz#35797f13a7fdadc566142c29d4f07ccad483e3ec"
  integrity sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=
  dependencies:
    glob "^7.1.3"

run-async@^2.2.0:
  version "2.4.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/run-async/download/run-async-2.4.1.tgz#8440eccf99ea3e70bd409d49aab88e10c189a455"
  integrity sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=

run-queue@^1.0.0, run-queue@^1.0.3:
  version "1.0.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/run-queue/download/run-queue-1.0.3.tgz#e848396f057d223f24386924618e25694161ec47"
  integrity sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec=
  dependencies:
    aproba "^1.1.1"

rxjs@^6.4.0:
  version "6.6.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/rxjs/download/rxjs-6.6.7.tgz#90ac018acabf491bf65044235d5863c4dab804c9"
  integrity sha1-kKwBisq/SRv2UEQjXVhjxNq4BMk=
  dependencies:
    tslib "^1.9.0"

safe-buffer@5.2.1, safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@^5.1.1, safe-buffer@^5.1.2, safe-buffer@^5.2.0, safe-buffer@^5.2.1, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/safe-buffer/download/safe-buffer-5.2.1.tgz#1eaf9fa9bdb1fdd4ec75f58f9cdb4e6b7827eec6"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/safe-buffer/download/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-regex2@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/safe-regex2/download/safe-regex2-2.0.0.tgz#b287524c397c7a2994470367e0185e1916b1f5b9"
  integrity sha1-sodSTDl8eimURwNn4BheGRax9bk=
  dependencies:
    ret "~0.2.0"

safe-regex@^1.1.0:
  version "1.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/safe-regex/download/safe-regex-1.1.0.tgz#40a3669f3b077d1e943d44629e157dd48023bf2e"
  integrity sha1-QKNmnzsHfR6UPURinhV91IAjvy4=
  dependencies:
    ret "~0.1.10"

"safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0", safer-buffer@^2.0.2, safer-buffer@^2.1.0, safer-buffer@~2.1.0:
  version "2.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/safer-buffer/download/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

scroll-into-view-if-needed@^2.2.28:
  version "2.2.31"
  resolved "http://npm.devops.xiaohongshu.com:7001/scroll-into-view-if-needed/download/scroll-into-view-if-needed-2.2.31.tgz#d3c482959dc483e37962d1521254e3295d0d1587"
  integrity sha1-08SClZ3Eg+N5YtFSElTjKV0NFYc=
  dependencies:
    compute-scroll-into-view "^1.0.20"

selectable.js@^0.17.6:
  version "0.17.8"
  resolved "http://npm.devops.xiaohongshu.com:7001/selectable.js/download/selectable.js-0.17.8.tgz#03291b4f32c29625f11e966c653fb38c2cf26542"
  integrity sha1-AykbTzLCliXxHpZsZT+zjCzyZUI=

"semver@2 || 3 || 4 || 5", "semver@2.x || 3.x || 4 || 5", semver@^5.4.1, semver@^5.5.0, semver@^5.5.1, semver@^5.6.0, semver@^5.7.0, semver@^5.7.1:
  version "5.7.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/semver/download/semver-5.7.1.tgz#a954f931aeba508d307bbf069eff0c01c96116f7"
  integrity sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=

semver@^6.0.0, semver@^6.2.0:
  version "6.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/semver/download/semver-6.3.0.tgz#ee0a64c8af5e8ceea67687b133761e1becbd1d3d"
  integrity sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=

semver@^7.3.4:
  version "7.3.8"
  resolved "http://npm.devops.xiaohongshu.com:7001/semver/download/semver-7.3.8.tgz#07a78feafb3f7b32347d725e33de7e2a2df67798"
  integrity sha1-B6eP6vs/ezI0fXJeM95+Ki32d5g=
  dependencies:
    lru-cache "^6.0.0"

serialize-javascript@^6.0.0:
  version "6.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/serialize-javascript/download/serialize-javascript-6.0.0.tgz#efae5d88f45d7924141da8b5c3a7a7e663fefeb8"
  integrity sha1-765diPRdeSQUHai1w6en5mP+/rg=
  dependencies:
    randombytes "^2.1.0"

set-blocking@^2.0.0, set-blocking@~2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/set-blocking/download/set-blocking-2.0.0.tgz#045f9782d011ae9a6803ddd382b24392b3d890f7"
  integrity sha1-BF+XgtARrppoA93TgrJDkrPYkPc=

set-value@^2.0.0, set-value@^2.0.1:
  version "2.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/set-value/download/set-value-2.0.1.tgz#a18d40530e6f07de4228c7defe4227af8cad005b"
  integrity sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=
  dependencies:
    extend-shallow "^2.0.1"
    is-extendable "^0.1.1"
    is-plain-object "^2.0.3"
    split-string "^3.0.1"

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/setprototypeof/download/setprototypeof-1.1.0.tgz#d0bd85536887b6fe7c0d818cb962d9d91c54e656"
  integrity sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY=

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/setprototypeof/download/setprototypeof-1.2.0.tgz#66c9a24a73f9fc28cbe66b09fed3d33dcaf1b424"
  integrity sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=

shallow-clone@^3.0.0:
  version "3.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/shallow-clone/download/shallow-clone-3.0.1.tgz#8f2981ad92531f55035b01fb230769a40e02efa3"
  integrity sha1-jymBrZJTH1UDWwH7IwdppA4C76M=
  dependencies:
    kind-of "^6.0.2"

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/shebang-command/download/shebang-command-1.2.0.tgz#44aac65b695b03398968c39f363fee5deafdf1ea"
  integrity sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=
  dependencies:
    shebang-regex "^1.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/shebang-regex/download/shebang-regex-1.0.0.tgz#da42f49740c0b42db2ca9728571cb190c98efea3"
  integrity sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=

shell-quote@^1.7.3:
  version "1.8.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/shell-quote/download/shell-quote-1.8.1.tgz#6dbf4db75515ad5bac63b4f1894c3a154c766680"
  integrity sha1-bb9Nt1UVrVusY7TxiUw6FUx2ZoA=

side-channel@^1.0.4:
  version "1.0.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/side-channel/download/side-channel-1.0.4.tgz#efce5c8fdc104ee751b25c58d4290011fa5ea2cf"
  integrity sha1-785cj9wQTudRslxY1CkAEfpeos8=
  dependencies:
    call-bind "^1.0.0"
    get-intrinsic "^1.0.2"
    object-inspect "^1.9.0"

signal-exit@^3.0.0, signal-exit@^3.0.2:
  version "3.0.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/signal-exit/download/signal-exit-3.0.7.tgz#a9a1767f8af84155114eaabd73f99273c8f59ad9"
  integrity sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=

simple-swizzle@^0.2.2:
  version "0.2.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/simple-swizzle/-/simple-swizzle-0.2.2.tgz#a4da6b635ffcccca33f70d17cb92592de95e557a"
  integrity sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==
  dependencies:
    is-arrayish "^0.3.1"

single-spa@^5.9.2:
  version "5.9.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/single-spa/download/single-spa-5.9.4.tgz#2a995b0784867a3f60ceb458de295ee67f045077"
  integrity sha1-KplbB4SGej9gzrRY3ile5n8EUHc=

slash@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/slash/download/slash-2.0.0.tgz#de552851a1759df3a8f206535442f5ec4ddeab44"
  integrity sha1-3lUoUaF1nfOo8gZTVEL17E3eq0Q=

slate-history@^0.66.0:
  version "0.66.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/slate-history/download/slate-history-0.66.0.tgz#ac63fddb903098ceb4c944433e3f75fe63acf940"
  integrity sha1-rGP925AwmM60yURDPj91/mOs+UA=
  dependencies:
    is-plain-object "^5.0.0"

slate@^0.72.0:
  version "0.72.8"
  resolved "http://npm.devops.xiaohongshu.com:7001/slate/download/slate-0.72.8.tgz#5a018edf24e45448655293a68bfbcf563aa5ba81"
  integrity sha1-WgGO3yTkVEhlUpOmi/vPVjqluoE=
  dependencies:
    immer "^9.0.6"
    is-plain-object "^5.0.0"
    tiny-warning "^1.0.3"

slide@^1.1.6:
  version "1.1.6"
  resolved "http://npm.devops.xiaohongshu.com:7001/slide/download/slide-1.1.6.tgz#56eb027d65b4d2dce6cb2e2d32c4d4afc9e1d707"
  integrity sha1-VusCfWW00tzmyy4tMsTUr8nh1wc=

smart-buffer@^4.1.0:
  version "4.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/smart-buffer/download/smart-buffer-4.2.0.tgz#6e1d71fa4f18c05f7d0ff216dd16a481d0e8d9ae"
  integrity sha1-bh1x+k8YwF99D/IW3RakgdDo2a4=

snabbdom@^3.1.0:
  version "3.5.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/snabbdom/download/snabbdom-3.5.1.tgz#25f80ef15b194baea703d9d5441892e369de18e1"
  integrity sha1-JfgO8VsZS66nA9nVRBiS42neGOE=

snapdragon-node@^2.0.1:
  version "2.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/snapdragon-node/download/snapdragon-node-2.1.1.tgz#6c175f86ff14bdb0724563e8f3c1b021a286853b"
  integrity sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=
  dependencies:
    define-property "^1.0.0"
    isobject "^3.0.0"
    snapdragon-util "^3.0.1"

snapdragon-util@^3.0.1:
  version "3.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/snapdragon-util/download/snapdragon-util-3.0.1.tgz#f956479486f2acd79700693f6f7b805e45ab56e2"
  integrity sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=
  dependencies:
    kind-of "^3.2.0"

snapdragon@^0.8.1:
  version "0.8.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/snapdragon/download/snapdragon-0.8.2.tgz#64922e7c565b0e14204ba1aa7d6964278d25182d"
  integrity sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=
  dependencies:
    base "^0.11.1"
    debug "^2.2.0"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    map-cache "^0.2.2"
    source-map "^0.5.6"
    source-map-resolve "^0.5.0"
    use "^3.1.0"

socks-proxy-agent@^4.0.0:
  version "4.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/socks-proxy-agent/download/socks-proxy-agent-4.0.2.tgz#3c8991f3145b2799e70e11bd5fbc8b1963116386"
  integrity sha1-PImR8xRbJ5nnDhG9X7yLGWMRY4Y=
  dependencies:
    agent-base "~4.2.1"
    socks "~2.3.2"

socks@~2.3.2:
  version "2.3.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/socks/download/socks-2.3.3.tgz#01129f0a5d534d2b897712ed8aceab7ee65d78e3"
  integrity sha1-ARKfCl1TTSuJdxLtis6rfuZdeOM=
  dependencies:
    ip "1.1.5"
    smart-buffer "^4.1.0"

sort-keys@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/sort-keys/download/sort-keys-2.0.0.tgz#658535584861ec97d730d6cf41822e1f56684128"
  integrity sha1-ZYU1WEhh7JfXMNbPQYIuH1ZoQSg=
  dependencies:
    is-plain-obj "^1.0.0"

sortablejs@1.14.0:
  version "1.14.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/sortablejs/download/sortablejs-1.14.0.tgz#6d2e17ccbdb25f464734df621d4f35d4ab35b3d8"
  integrity sha1-bS4XzL2yX0ZHNN9iHU811Ks1s9g=

sortablejs@^1.10.2, sortablejs@^1.15.0:
  version "1.15.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/sortablejs/download/sortablejs-1.15.0.tgz#53230b8aa3502bb77a29e2005808ffdb4a5f7e2a"
  integrity sha1-UyMLiqNQK7d6KeIAWAj/20pffio=

source-map-js@^1.0.2:
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/source-map-js/download/source-map-js-1.0.2.tgz#adbc361d9c62df380125e7f161f71c826f1e490c"
  integrity sha1-rbw2HZxi3zgBJefxYfccgm8eSQw=

source-map-resolve@^0.5.0:
  version "0.5.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/source-map-resolve/download/source-map-resolve-0.5.3.tgz#190866bece7553e1f8f267a2ee82c606b5509a1a"
  integrity sha1-GQhmvs51U+H48mei7oLGBrVQmho=
  dependencies:
    atob "^2.1.2"
    decode-uri-component "^0.2.0"
    resolve-url "^0.2.1"
    source-map-url "^0.4.0"
    urix "^0.1.0"

source-map-url@^0.4.0:
  version "0.4.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/source-map-url/download/source-map-url-0.4.1.tgz#0af66605a745a5a2f91cf1bbf8a7afbc283dec56"
  integrity sha1-CvZmBadFpaL5HPG7+KevvCg97FY=

source-map@^0.5.6:
  version "0.5.7"
  resolved "http://npm.devops.xiaohongshu.com:7001/source-map/download/source-map-0.5.7.tgz#8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

source-map@^0.6.1:
  version "0.6.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/source-map/download/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

sourcemap-codec@^1.4.8:
  version "1.4.8"
  resolved "http://npm.devops.xiaohongshu.com:7001/sourcemap-codec/download/sourcemap-codec-1.4.8.tgz#ea804bd94857402e6992d05a38ef1ae35a9ab4c4"
  integrity sha1-6oBL2UhXQC5pktBaOO8a41qatMQ=

spark-md5@^3.0.0:
  version "3.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/spark-md5/download/spark-md5-3.0.2.tgz#7952c4a30784347abcee73268e473b9c0167e3fc"
  integrity sha1-eVLEoweENHq87nMmjkc7nAFn4/w=

spdx-correct@^3.0.0:
  version "3.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/spdx-correct/download/spdx-correct-3.1.1.tgz#dece81ac9c1e6713e5f7d1b6f17d468fa53d89a9"
  integrity sha1-3s6BrJweZxPl99G28X1Gj6U9iak=
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/spdx-exceptions/download/spdx-exceptions-2.3.0.tgz#3f28ce1a77a00372683eade4a433183527a2163d"
  integrity sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0=

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz#cf70f50482eefdc98e3ce0a6833e4a53ceeba679"
  integrity sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.12"
  resolved "http://npm.devops.xiaohongshu.com:7001/spdx-license-ids/download/spdx-license-ids-3.0.12.tgz#69077835abe2710b65f03969898b6637b505a779"
  integrity sha1-aQd4NavicQtl8DlpiYtmN7UFp3k=

split-on-first@^1.0.0:
  version "1.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/split-on-first/download/split-on-first-1.1.0.tgz#f610afeee3b12bce1d0c30425e76398b78249a5f"
  integrity sha1-9hCv7uOxK84dDDBCXnY5i3gkml8=

split-string@^3.0.1, split-string@^3.0.2:
  version "3.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/split-string/download/split-string-3.1.0.tgz#7cb09dda3a86585705c64b39a6466038682e8fe2"
  integrity sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=
  dependencies:
    extend-shallow "^3.0.0"

split2@^2.0.0:
  version "2.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/split2/download/split2-2.2.0.tgz#186b2575bcf83e85b7d18465756238ee4ee42493"
  integrity sha1-GGsldbz4PoW30YRldWI47k7kJJM=
  dependencies:
    through2 "^2.0.2"

split2@^3.0.0:
  version "3.2.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/split2/download/split2-3.2.2.tgz#bf2cf2a37d838312c249c89206fd7a17dd12365f"
  integrity sha1-vyzyo32DgxLCSciSBv16F90SNl8=
  dependencies:
    readable-stream "^3.0.0"

split@^1.0.0:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/split/download/split-1.0.1.tgz#605bd9be303aa59fb35f9229fbea0ddec9ea07d9"
  integrity sha1-YFvZvjA6pZ+zX5Ip++oN3snqB9k=
  dependencies:
    through "2"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/sprintf-js/download/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

ssf@~0.11.2:
  version "0.11.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ssf/download/ssf-0.11.2.tgz#0b99698b237548d088fc43cdf2b70c1a7512c06c"
  integrity sha1-C5lpiyN1SNCI/EPN8rcMGnUSwGw=
  dependencies:
    frac "~1.1.2"

sshpk@^1.7.0:
  version "1.17.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/sshpk/download/sshpk-1.17.0.tgz#578082d92d4fe612b13007496e543fa0fbcbe4c5"
  integrity sha1-V4CC2S1P5hKxMAdJblQ/oPvL5MU=
  dependencies:
    asn1 "~0.2.3"
    assert-plus "^1.0.0"
    bcrypt-pbkdf "^1.0.0"
    dashdash "^1.12.0"
    ecc-jsbn "~0.1.1"
    getpass "^0.1.1"
    jsbn "~0.1.0"
    safer-buffer "^2.0.2"
    tweetnacl "~0.14.0"

ssr-window@^3.0.0-alpha.1:
  version "3.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/ssr-window/download/ssr-window-3.0.0.tgz#fd5b82801638943e0cc704c4691801435af7ac37"
  integrity sha1-/VuCgBY4lD4MxwTEaRgBQ1r3rDc=

ssri@^6.0.0, ssri@^6.0.1:
  version "6.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/ssri/download/ssri-6.0.2.tgz#157939134f20464e7301ddba3e90ffa8f7728ac5"
  integrity sha1-FXk5E08gRk5zAd26PpD/qPdyisU=
  dependencies:
    figgy-pudding "^3.5.1"

static-extend@^0.1.1:
  version "0.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/static-extend/download/static-extend-0.1.2.tgz#60809c39cbff55337226fd5e0b520f341f1fb5c6"
  integrity sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=
  dependencies:
    define-property "^0.2.5"
    object-copy "^0.1.0"

statuses@2.0.1:
  version "2.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/statuses/download/statuses-2.0.1.tgz#55cb000ccf1d48728bd23c685a063998cf1a1b63"
  integrity sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=

"statuses@>= 1.4.0 < 2", "statuses@>= 1.5.0 < 2", statuses@^1.5.0:
  version "1.5.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/statuses/download/statuses-1.5.0.tgz#161c7dac177659fd9811f43771fa99381478628c"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

stream-each@^1.1.0:
  version "1.2.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/stream-each/download/stream-each-1.2.3.tgz#ebe27a0c389b04fbcc233642952e10731afa9bae"
  integrity sha1-6+J6DDibBPvMIzZClS4Qcxr6m64=
  dependencies:
    end-of-stream "^1.1.0"
    stream-shift "^1.0.0"

stream-shift@^1.0.0:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/stream-shift/download/stream-shift-1.0.1.tgz#d7088281559ab2778424279b0877da3c392d5a3d"
  integrity sha1-1wiCgVWasneEJCebCHfaPDktWj0=

strict-uri-encode@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/strict-uri-encode/download/strict-uri-encode-2.0.0.tgz#b9c7330c7042862f6b142dc274bbcc5866ce3546"
  integrity sha1-ucczDHBChi9rFC3CdLvMWGbONUY=

string-width@^1.0.1:
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/string-width/download/string-width-1.0.2.tgz#118bdf5b8cdc51a2a7e70d211e07e2b0b9b107d3"
  integrity sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    strip-ansi "^3.0.0"

"string-width@^1.0.2 || 2 || 3 || 4":
  version "4.2.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/string-width/download/string-width-4.2.3.tgz#269c7117d27b05ad2e536830a8ec895ef9c6d010"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^2.1.0:
  version "2.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/string-width/download/string-width-2.1.1.tgz#ab93f27a8dc13d28cac815c462143a6d9012ae9e"
  integrity sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string-width@^3.0.0, string-width@^3.1.0:
  version "3.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/string-width/download/string-width-3.1.0.tgz#22767be21b62af1081574306f69ac51b62203961"
  integrity sha1-InZ74htirxCBV0MG9prFG2IgOWE=
  dependencies:
    emoji-regex "^7.0.1"
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^5.1.0"

string.prototype.trimend@^1.0.5:
  version "1.0.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/string.prototype.trimend/download/string.prototype.trimend-1.0.5.tgz#914a65baaab25fbdd4ee291ca7dde57e869cb8d0"
  integrity sha1-kUpluqqyX73U7ikcp93lfoacuNA=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.19.5"

string.prototype.trimstart@^1.0.5:
  version "1.0.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/string.prototype.trimstart/download/string.prototype.trimstart-1.0.5.tgz#5466d93ba58cfa2134839f81d7f42437e8c01fef"
  integrity sha1-VGbZO6WM+iE0g5+B1/QkN+jAH+8=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.19.5"

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/string_decoder/download/string_decoder-1.3.0.tgz#42f114594a46cf1a8e30b0a84f56c78c3edac21e"
  integrity sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/string_decoder/download/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

strip-ansi@^3.0.0, strip-ansi@^3.0.1:
  version "3.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/strip-ansi/download/strip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"
  integrity sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/strip-ansi/download/strip-ansi-4.0.0.tgz#a8479022eb1ac368a871389b635262c505ee368f"
  integrity sha1-qEeQIusaw2iocTibY1JixQXuNo8=
  dependencies:
    ansi-regex "^3.0.0"

strip-ansi@^5.0.0, strip-ansi@^5.1.0, strip-ansi@^5.2.0:
  version "5.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/strip-ansi/download/strip-ansi-5.2.0.tgz#8c9a536feb6afc962bdfa5b104a5091c1ad9c0ae"
  integrity sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=
  dependencies:
    ansi-regex "^4.1.0"

strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/strip-ansi/download/strip-ansi-6.0.1.tgz#9e26c63d30f53443e9489495b2105d37b67a85d9"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-bom@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/strip-bom/download/strip-bom-2.0.0.tgz#6219a85616520491f35788bdbf1447a99c7e6b0e"
  integrity sha1-YhmoVhZSBJHzV4i9vxRHqZx+aw4=
  dependencies:
    is-utf8 "^0.2.0"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/strip-bom/download/strip-bom-3.0.0.tgz#2334c18e9c759f7bdd56fdef7e9ae3d588e68ed3"
  integrity sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/strip-eof/download/strip-eof-1.0.0.tgz#bb43ff5598a6eb05d89b59fcd129c983313606bf"
  integrity sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=

strip-indent@^1.0.1:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/strip-indent/download/strip-indent-1.0.1.tgz#0c7962a6adefa7bbd4ac366460a638552ae1a0a2"
  integrity sha1-DHlipq3vp7vUrDZkYKY4VSrhoKI=
  dependencies:
    get-stdin "^4.0.1"

strip-indent@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/strip-indent/download/strip-indent-2.0.0.tgz#5ef8db295d01e6ed6cbf7aab96998d7822527b68"
  integrity sha1-XvjbKV0B5u1sv3qrlpmNeCJSe2g=

strip-indent@^3.0.0:
  version "3.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/strip-indent/download/strip-indent-3.0.0.tgz#c32e1cee940b6b3432c771bc2c54bcce73cd3001"
  integrity sha1-wy4c7pQLazQyx3G8LFS8znPNMAE=
  dependencies:
    min-indent "^1.0.0"

strong-log-transformer@^2.0.0:
  version "2.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/strong-log-transformer/download/strong-log-transformer-2.1.0.tgz#0f5ed78d325e0421ac6f90f7f10e691d6ae3ae10"
  integrity sha1-D17XjTJeBCGsb5D38Q5pHWrjrhA=
  dependencies:
    duplexer "^0.1.1"
    minimist "^1.2.0"
    through "^2.3.4"

strtok3@^6.0.3:
  version "6.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/strtok3/download/strtok3-6.3.0.tgz#358b80ffe6d5d5620e19a073aa78ce947a90f9a0"
  integrity sha1-NYuA/+bV1WIOGaBzqnjOlHqQ+aA=
  dependencies:
    "@tokenizer/token" "^0.3.0"
    peek-readable "^4.1.0"

supports-color@^5.3.0:
  version "5.5.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/supports-color/download/supports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz#6eda4bd344a3c94aea376d4cc31bc77311039e09"
  integrity sha1-btpL00SjyUrqN21MwxvHcxEDngk=

synchronous-promise@^2.0.6:
  version "2.0.17"
  resolved "http://npm.devops.xiaohongshu.com:7001/synchronous-promise/download/synchronous-promise-2.0.17.tgz#38901319632f946c982152586f2caf8ddc25c032"
  integrity sha1-OJATGWMvlGyYIVJYbyyvjdwlwDI=

tar@^4.4.10, tar@^4.4.12, tar@^4.4.8:
  version "4.4.19"
  resolved "http://npm.devops.xiaohongshu.com:7001/tar/download/tar-4.4.19.tgz#2e4d7263df26f2b914dee10c825ab132123742f3"
  integrity sha1-Lk1yY98m8rkU3uEMglqxMhI3QvM=
  dependencies:
    chownr "^1.1.4"
    fs-minipass "^1.2.7"
    minipass "^2.9.0"
    minizlib "^1.3.3"
    mkdirp "^0.5.5"
    safe-buffer "^5.2.1"
    yallist "^3.1.1"

tdigest@^0.1.1:
  version "0.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/tdigest/download/tdigest-0.1.2.tgz#96c64bac4ff10746b910b0e23b515794e12faced"
  integrity sha1-lsZLrE/xB0a5ELDiO1FXlOEvrO0=
  dependencies:
    bintrees "1.0.2"

temp-dir@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/temp-dir/download/temp-dir-1.0.0.tgz#0a7c0ea26d3a39afa7e0ebea9c1fc0bc4daa011d"
  integrity sha1-CnwOom06Oa+n4OvqnB/AvE2qAR0=

temp-write@^3.4.0:
  version "3.4.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/temp-write/download/temp-write-3.4.0.tgz#8cff630fb7e9da05f047c74ce4ce4d685457d492"
  integrity sha1-jP9jD7fp2gXwR8dM5M5NaFRX1JI=
  dependencies:
    graceful-fs "^4.1.2"
    is-stream "^1.1.0"
    make-dir "^1.0.0"
    pify "^3.0.0"
    temp-dir "^1.0.0"
    uuid "^3.0.1"

text-extensions@^1.0.0:
  version "1.9.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/text-extensions/download/text-extensions-1.9.0.tgz#1853e45fee39c945ce6f6c36b2d659b5aabc2a26"
  integrity sha1-GFPkX+45yUXOb2w2stZZtaq8KiY=

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/thenify-all/download/thenify-all-1.6.0.tgz#1a1918d402d8fc3f98fbf234db0bcc8cc10e9726"
  integrity sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/thenify/download/thenify-3.3.1.tgz#8932e686a4066038a016dd9e2ca46add9838a95f"
  integrity sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8=
  dependencies:
    any-promise "^1.0.0"

thrift@~0.18.1:
  version "0.18.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/thrift/download/thrift-0.18.1.tgz#b17946f79fd220c7b1e839d956ef63071ec6982b"
  integrity sha1-sXlG95/SIMex6DnZVu9jBx7GmCs=
  dependencies:
    browser-or-node "^1.2.1"
    isomorphic-ws "^4.0.1"
    node-int64 "^0.4.0"
    q "^1.5.0"
    ws "^5.2.3"

through2@^2.0.0, through2@^2.0.2:
  version "2.0.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/through2/download/through2-2.0.5.tgz#01c1e39eb31d07cb7d03a96a70823260b23132cd"
  integrity sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=
  dependencies:
    readable-stream "~2.3.6"
    xtend "~4.0.1"

through2@^3.0.0:
  version "3.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/through2/download/through2-3.0.2.tgz#99f88931cfc761ec7678b41d5d7336b5b6a07bf4"
  integrity sha1-mfiJMc/HYex2eLQdXXM2tbage/Q=
  dependencies:
    inherits "^2.0.4"
    readable-stream "2 || 3"

through2@^4.0.0:
  version "4.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/through2/download/through2-4.0.2.tgz#a7ce3ac2a7a8b0b966c80e7c49f0484c3b239764"
  integrity sha1-p846wqeosLlmyA58SfBITDsjl2Q=
  dependencies:
    readable-stream "3"

through@2, "through@>=2.2.7 <3", through@^2.3.4, through@^2.3.6:
  version "2.3.8"
  resolved "http://npm.devops.xiaohongshu.com:7001/through/download/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

tiny-warning@^1.0.3:
  version "1.0.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/tiny-warning/download/tiny-warning-1.0.3.tgz#94a30db453df4c643d0fd566060d60a875d84754"
  integrity sha1-lKMNtFPfTGQ9D9VmBg1gqHXYR1Q=

tinycolor2@^1.4.1:
  version "1.6.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/tinycolor2/download/tinycolor2-1.6.0.tgz#f98007460169b0263b97072c5ae92484ce02d09e"
  integrity sha1-+YAHRgFpsCY7lwcsWukkhM4C0J4=

tmp@^0.0.33:
  version "0.0.33"
  resolved "http://npm.devops.xiaohongshu.com:7001/tmp/download/tmp-0.0.33.tgz#6d34335889768d21b2bcda0aa277ced3b1bfadf9"
  integrity sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=
  dependencies:
    os-tmpdir "~1.0.2"

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/to-fast-properties/download/to-fast-properties-2.0.0.tgz#dc5e698cbd079265bc73e0377681a4e4e83f616e"
  integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=

to-object-path@^0.3.0:
  version "0.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/to-object-path/download/to-object-path-0.3.0.tgz#297588b7b0e7e0ac08e04e672f85c1f4999e17af"
  integrity sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=
  dependencies:
    kind-of "^3.0.2"

to-regex-range@^2.1.0:
  version "2.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/to-regex-range/download/to-regex-range-2.1.1.tgz#7c80c17b9dfebe599e27367e0d4dd5590141db38"
  integrity sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=
  dependencies:
    is-number "^3.0.0"
    repeat-string "^1.6.1"

to-regex@^3.0.1, to-regex@^3.0.2:
  version "3.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/to-regex/download/to-regex-3.0.2.tgz#13cfdd9b336552f30b51f33a8ae1b42a7a7599ce"
  integrity sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=
  dependencies:
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    regex-not "^1.0.2"
    safe-regex "^1.1.0"

toggle-selection@^1.0.6:
  version "1.0.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/toggle-selection/download/toggle-selection-1.0.6.tgz#6e45b1263f2017fa0acc7d89d78b15b8bf77da32"
  integrity sha1-bkWxJj8gF/oKzH2J14sVuL932jI=

toidentifier@1.0.1:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/toidentifier/download/toidentifier-1.0.1.tgz#3be34321a88a820ed1bd80dfaa33e479fbb8dd35"
  integrity sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=

token-types@^2.0.0:
  version "2.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/token-types/download/token-types-2.1.1.tgz#bd585d64902aaf720b8979d257b4b850b4d45c45"
  integrity sha1-vVhdZJAqr3ILiXnSV7S4ULTUXEU=
  dependencies:
    "@tokenizer/token" "^0.1.1"
    ieee754 "^1.2.1"

toposort@^2.0.2:
  version "2.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/toposort/download/toposort-2.0.2.tgz#ae21768175d1559d48bef35420b2f4962f09c330"
  integrity sha1-riF2gXXRVZ1IvvNUILL0li8JwzA=

tough-cookie@~2.5.0:
  version "2.5.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/tough-cookie/download/tough-cookie-2.5.0.tgz#cd9fb2a0aa1d5a12b473bd9fb96fa3dcff65ade2"
  integrity sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=
  dependencies:
    psl "^1.1.28"
    punycode "^2.1.1"

tr46@^1.0.1:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/tr46/download/tr46-1.0.1.tgz#a8b13fd6bfd2489519674ccde55ba3693b706d09"
  integrity sha1-qLE/1r/SSJUZZ0zN5VujaTtwbQk=
  dependencies:
    punycode "^2.1.0"

tr46@~0.0.3:
  version "0.0.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/tr46/download/tr46-0.0.3.tgz#8184fd347dac9cdc185992f3a6622e14b9d9ab6a"
  integrity sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=

trim-newlines@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/trim-newlines/download/trim-newlines-1.0.0.tgz#5887966bb582a4503a41eb524f7d35011815a613"
  integrity sha1-WIeWa7WCpFA6QetST301ARgVphM=

trim-newlines@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/trim-newlines/download/trim-newlines-2.0.0.tgz#b403d0b91be50c331dfc4b82eeceb22c3de16d20"
  integrity sha1-tAPQuRvlDDMd/EuC7s6yLD3hbSA=

trim-newlines@^3.0.0:
  version "3.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/trim-newlines/download/trim-newlines-3.0.1.tgz#260a5d962d8b752425b32f3a7db0dcacd176c144"
  integrity sha1-Jgpdli2LdSQlsy86fbDcrNF2wUQ=

tslib@2.3.0:
  version "2.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/tslib/download/tslib-2.3.0.tgz#803b8cdab3e12ba581a4ca41c8839bbb0dacb09e"
  integrity sha1-gDuM2rPhK6WBpMpByIObuw2ssJ4=

tslib@^1.9.0, tslib@^1.9.3:
  version "1.14.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/tslib/download/tslib-1.14.1.tgz#cf2d38bdc34a134bcaf1091c41f6619e2f672d00"
  integrity sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=

tslib@^2.0.0, tslib@^2.1.0, tslib@^2.3.1:
  version "2.6.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/tslib/-/tslib-2.6.2.tgz#703ac29425e7b37cd6fd456e92404d46d1f3e4ae"
  integrity sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q==

tslib@^2.0.3:
  version "2.4.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/tslib/download/tslib-2.4.0.tgz#7cecaa7f073ce680a05847aa77be941098f36dc3"
  integrity sha1-fOyqfwc85oCgWEeqd76UEJjzbcM=

tslib@~2.3.1:
  version "2.3.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/tslib/download/tslib-2.3.1.tgz#e8a335add5ceae51aa261d32a490158ef042ef01"
  integrity sha1-6KM1rdXOrlGqJh0ypJAVjvBC7wE=

tsscmp@1.0.6:
  version "1.0.6"
  resolved "http://npm.devops.xiaohongshu.com:7001/tsscmp/download/tsscmp-1.0.6.tgz#85b99583ac3589ec4bfef825b5000aa911d605eb"
  integrity sha1-hbmVg6w1iexL/vgltQAKqRHWBes=

tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/tunnel-agent/download/tunnel-agent-0.6.0.tgz#27a5dea06b36b04a0a9966774b290868f0fc40fd"
  integrity sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=
  dependencies:
    safe-buffer "^5.0.1"

tweetnacl@^0.14.3, tweetnacl@~0.14.0:
  version "0.14.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/tweetnacl/download/tweetnacl-0.14.5.tgz#5ae68177f192d4456269d108afa93ff8743f4f64"
  integrity sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=

type-fest@^0.18.0:
  version "0.18.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/type-fest/download/type-fest-0.18.1.tgz#db4bc151a4a2cf4eebf9add5db75508db6cc841f"
  integrity sha1-20vBUaSiz07r+a3V23VQjbbMhB8=

type-fest@^0.3.0:
  version "0.3.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/type-fest/download/type-fest-0.3.1.tgz#63d00d204e059474fe5e1b7c011112bbd1dc29e1"
  integrity sha1-Y9ANIE4FlHT+Xht8ARESu9HcKeE=

type-fest@^0.6.0:
  version "0.6.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/type-fest/download/type-fest-0.6.0.tgz#8d2a2370d3df886eb5c90ada1c5bf6188acf838b"
  integrity sha1-jSojcNPfiG61yQraHFv2GIrPg4s=

type-fest@^0.8.1:
  version "0.8.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/type-fest/download/type-fest-0.8.1.tgz#09e249ebde851d3b1e48d27c105444667f17b83d"
  integrity sha1-CeJJ696FHTseSNJ8EFREZn8XuD0=

type-is@^1.6.16, type-is@^1.6.18:
  version "1.6.18"
  resolved "http://npm.devops.xiaohongshu.com:7001/type-is/download/type-is-1.6.18.tgz#4e552cd05df09467dcbc4ef739de89f2cf37c131"
  integrity sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

type@^1.0.1:
  version "1.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/type/download/type-1.2.0.tgz#848dd7698dafa3e54a6c479e759c4bc3f18847a0"
  integrity sha1-hI3XaY2vo+VKbEeedZxLw/GIR6A=

type@^2.7.2:
  version "2.7.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/type/download/type-2.7.2.tgz#2376a15a3a28b1efa0f5350dcf72d24df6ef98d0"
  integrity sha1-I3ahWjoose+g9TUNz3LSTfbvmNA=

typedarray-to-buffer@^3.1.5:
  version "3.1.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/typedarray-to-buffer/download/typedarray-to-buffer-3.1.5.tgz#a97ee7a9ff42691b9f783ff1bc5112fe3fca9080"
  integrity sha1-qX7nqf9CaRufeD/xvFES/j/KkIA=
  dependencies:
    is-typedarray "^1.0.0"

typedarray@^0.0.6:
  version "0.0.6"
  resolved "http://npm.devops.xiaohongshu.com:7001/typedarray/download/typedarray-0.0.6.tgz#867ac74e3864187b1d3d47d996a78ec5c8830777"
  integrity sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=

typescript@4.5.2:
  version "4.5.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/typescript/download/typescript-4.5.2.tgz#8ac1fba9f52256fdb06fb89e4122fa6a346c2998"
  integrity sha1-isH7qfUiVv2wb7ieQSL6ajRsKZg=

typescript@^5.0.4:
  version "5.0.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/typescript/download/typescript-5.0.4.tgz#b217fd20119bd61a94d4011274e0ab369058da3b"
  integrity sha1-shf9IBGb1hqU1AESdOCrNpBY2js=

uc.micro@^2.0.0, uc.micro@^2.1.0:
  version "2.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/uc.micro/-/uc.micro-2.1.0.tgz#f8d3f7d0ec4c3dea35a7e3c8efa4cb8b45c9e7ee"
  integrity sha512-ARDJmphmdvUk6Glw7y9DQ2bFkKBHwQHLi2lsaH6PPmz/Ka9sFOBsBluozhDltWmnv9u/cF6Rt87znRTPV+yp/A==

uglify-js@^3.1.4:
  version "3.16.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/uglify-js/download/uglify-js-3.16.3.tgz#94c7a63337ee31227a18d03b8a3041c210fd1f1d"
  integrity sha1-lMemMzfuMSJ6GNA7ijBBwhD9Hx0=

uid-number@0.0.6:
  version "0.0.6"
  resolved "http://npm.devops.xiaohongshu.com:7001/uid-number/download/uid-number-0.0.6.tgz#0ea10e8035e8eb5b8e4449f06da1c730663baa81"
  integrity sha1-DqEOgDXo61uOREnwbaHHMGY7qoE=

umask@^1.1.0:
  version "1.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/umask/download/umask-1.1.0.tgz#f29cebf01df517912bb58ff9c4e50fde8e33320d"
  integrity sha1-8pzr8B31F5ErtY/5xOUP3o4zMg0=

unbox-primitive@^1.0.2:
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/unbox-primitive/download/unbox-primitive-1.0.2.tgz#29032021057d5e6cdbd08c5129c226dff8ed6f9e"
  integrity sha1-KQMgIQV9Xmzb0IxRKcIm3/jtb54=
  dependencies:
    call-bind "^1.0.2"
    has-bigints "^1.0.2"
    has-symbols "^1.0.3"
    which-boxed-primitive "^1.0.2"

union-value@^1.0.0:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/union-value/download/union-value-1.0.1.tgz#0b6fe7b835aecda61c6ea4d4f02c14221e109847"
  integrity sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=
  dependencies:
    arr-union "^3.1.0"
    get-value "^2.0.6"
    is-extendable "^0.1.1"
    set-value "^2.0.1"

unique-filename@^1.1.1:
  version "1.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/unique-filename/download/unique-filename-1.1.1.tgz#1d69769369ada0583103a1e6ae87681b56573230"
  integrity sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA=
  dependencies:
    unique-slug "^2.0.0"

unique-slug@^2.0.0:
  version "2.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/unique-slug/download/unique-slug-2.0.2.tgz#baabce91083fc64e945b0f3ad613e264f7cd4e6c"
  integrity sha1-uqvOkQg/xk6UWw861hPiZPfNTmw=
  dependencies:
    imurmurhash "^0.1.4"

universal-user-agent@^4.0.0:
  version "4.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/universal-user-agent/download/universal-user-agent-4.0.1.tgz#fd8d6cb773a679a709e967ef8288a31fcc03e557"
  integrity sha1-/Y1st3OmeacJ6WfvgoijH8wD5Vc=
  dependencies:
    os-name "^3.1.0"

universal-user-agent@^6.0.0:
  version "6.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/universal-user-agent/download/universal-user-agent-6.0.0.tgz#3381f8503b251c0d9cd21bc1de939ec9df5480ee"
  integrity sha1-M4H4UDslHA2c0hvB3pOeyd9UgO4=

universalify@^0.1.0:
  version "0.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/universalify/download/universalify-0.1.2.tgz#b646f69be3942dabcecc9d6639c80dc105efaa66"
  integrity sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY=

universalify@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/universalify/download/universalify-2.0.0.tgz#75a4984efedc4b08975c5aeb73f530d02df25717"
  integrity sha1-daSYTv7cSwiXXFrrc/Uw0C3yVxc=

unpipe@1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/unpipe/download/unpipe-1.0.0.tgz#b2bf4ee8514aae6165b4817829d21b2ef49904ec"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

unset-value@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/unset-value/download/unset-value-1.0.0.tgz#8376873f7d2335179ffb1e6fc3a8ed0dfc8ab559"
  integrity sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=
  dependencies:
    has-value "^0.3.1"
    isobject "^3.0.0"

upath@^1.2.0:
  version "1.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/upath/download/upath-1.2.0.tgz#8f66dbcd55a883acdae4408af8b035a5044c1894"
  integrity sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ=

upper-case@^2.0.1:
  version "2.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/upper-case/download/upper-case-2.0.2.tgz#d89810823faab1df1549b7d97a76f8662bae6f7a"
  integrity sha1-2JgQgj+qsd8VSbfZenb4Ziuub3o=
  dependencies:
    tslib "^2.0.3"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/uri-js/download/uri-js-4.4.1.tgz#9b1a52595225859e55f669d928f88c6c57f2a77e"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

urix@^0.1.0:
  version "0.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/urix/download/urix-0.1.0.tgz#da937f7a62e21fec1fd18d49b35c2935067a6c72"
  integrity sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=

url-parse@^1.5.1:
  version "1.5.10"
  resolved "http://npm.devops.xiaohongshu.com:7001/url-parse/download/url-parse-1.5.10.tgz#9d3c2f736c1d75dd3bd2be507dcc111f1e2ea9c1"
  integrity sha1-nTwvc2wddd070r5QfcwRHx4uqcE=
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

url-search-params@^1.1.0:
  version "1.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/url-search-params/-/url-search-params-1.1.0.tgz#865669a6e4e9e5543f86fc972b27c91485375326"
  integrity sha512-XiO5GLAxmlZgdLob/RmKZRc2INHrssMbpwD6O46JkB1aEJO4fkV3x3mR6+CDX01ijfEUwvfwCiUQZrKqfm1ILw==

url@^0.11.0:
  version "0.11.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/url/download/url-0.11.0.tgz#3838e97cfc60521eb73c525a8e55bfdd9e2e28f1"
  integrity sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE=
  dependencies:
    punycode "1.3.2"
    querystring "0.2.0"

use@^3.1.0:
  version "3.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/use/download/use-3.1.1.tgz#d50c8cac79a19fbc20f2911f56eb973f4e10070f"
  integrity sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=

util-deprecate@^1.0.1, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/util-deprecate/download/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

util-promisify@^2.1.0:
  version "2.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/util-promisify/download/util-promisify-2.1.0.tgz#3c2236476c4d32c5ff3c47002add7c13b9a82a53"
  integrity sha1-PCI2R2xNMsX/PEcAKt18E7moKlM=
  dependencies:
    object.getownpropertydescriptors "^2.0.3"

uuid@^3.0.1, uuid@^3.3.2:
  version "3.4.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/uuid/download/uuid-3.4.0.tgz#b23e4358afa8a202fe7a100af1f5f883f02007ee"
  integrity sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=

uuid@^7.0.2:
  version "7.0.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/uuid/download/uuid-7.0.3.tgz#c5c9f2c8cf25dc0a372c4df1441c41f5bd0c680b"
  integrity sha1-xcnyyM8l3Ao3LE3xRBxB9b0MaAs=

uuid@^8.1.0, uuid@^8.3.1, uuid@^8.3.2:
  version "8.3.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/uuid/download/uuid-8.3.2.tgz#80d5b5ced271bb9af6c445f21a1a04c606cefbe2"
  integrity sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=

v-click-outside@^2.1.3:
  version "2.1.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/v-click-outside/download/v-click-outside-2.1.5.tgz#aa69172fb41fcc79b26b9a4bc72a30ccf03f7a3c"
  integrity sha1-qmkXL7QfzHmya5pLxyowzPA/ejw=

validate-npm-package-license@^3.0.1, validate-npm-package-license@^3.0.3:
  version "3.0.4"
  resolved "http://npm.devops.xiaohongshu.com:7001/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz#fc91f6b9c7ba15c857f4cb2c5defeec39d4f410a"
  integrity sha1-/JH2uce6FchX9MssXe/uw51PQQo=
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

validate-npm-package-name@^3.0.0:
  version "3.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/validate-npm-package-name/download/validate-npm-package-name-3.0.0.tgz#5fa912d81eb7d0c74afc140de7317f0ca7df437e"
  integrity sha1-X6kS2B630MdK/BQN5zF/DKffQ34=
  dependencies:
    builtins "^1.0.3"

vary@^1.1.2:
  version "1.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/vary/download/vary-1.1.2.tgz#2299f02c6ded30d4a5961b0b9f74524a18f634fc"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

verror@1.10.0:
  version "1.10.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/verror/download/verror-1.10.0.tgz#3a105ca17053af55d6e270c1f8288682e18da400"
  integrity sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=
  dependencies:
    assert-plus "^1.0.0"
    core-util-is "1.0.2"
    extsprintf "^1.2.0"

viewerjs@^1.9.0:
  version "1.10.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/viewerjs/download/viewerjs-1.10.2.tgz#de16fa10668e4da6325969836a3264a046e3ef9a"
  integrity sha1-3hb6EGaOTaYyWWmDajJkoEbj75o=

vue-demi@*, vue-demi@>=0.14.0:
  version "0.14.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/vue-demi/download/vue-demi-0.14.0.tgz#dcfd9a9cf9bb62ada1582ec9042372cf67ca6190"
  integrity sha1-3P2anPm7Yq2hWC7JBCNyz2fKYZA=

vue-demi@^0.13.6:
  version "0.13.11"
  resolved "http://npm.devops.xiaohongshu.com:7001/vue-demi/download/vue-demi-0.13.11.tgz#7d90369bdae8974d87b1973564ad390182410d99"
  integrity sha1-fZA2m9rol02HsZc1ZK05AYJBDZk=

vue-frag@^1.1.4:
  version "1.4.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/vue-frag/download/vue-frag-1.4.1.tgz#c0c2e633ce36b0b5eb8373c539c90d3bab25a7f8"
  integrity sha1-wMLmM842sLXrg3PFOckNO6slp/g=

vue-observe-visibility@^2.0.0-alpha.1:
  version "2.0.0-alpha.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/vue-observe-visibility/download/vue-observe-visibility-2.0.0-alpha.1.tgz#1e4eda7b12562161d58984b7e0dea676d83bdb13"
  integrity sha1-Hk7aexJWIWHViYS34N6mdtg72xM=

vue-resize@^2.0.0-alpha.1:
  version "2.0.0-alpha.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/vue-resize/download/vue-resize-2.0.0-alpha.1.tgz#43eeb79e74febe932b9b20c5c57e0ebc14e2df3a"
  integrity sha1-Q+63nnT+vpMrmyDFxX4OvBTi3zo=

vue-router@^4.0.14:
  version "4.1.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/vue-router/download/vue-router-4.1.5.tgz#256f597e3f5a281a23352a6193aa6e342c8d9f9a"
  integrity sha1-JW9Zfj9aKBojNSphk6puNCyNn5o=
  dependencies:
    "@vue/devtools-api" "^6.1.4"

vue-slicksort@^2.0.0-alpha.5:
  version "2.0.0-alpha.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/vue-slicksort/download/vue-slicksort-2.0.0-alpha.5.tgz#b1c09ed1882105001b1ea5f7fea0e81efadf3c70"
  integrity sha1-scCe0YghBQAbHqX3/qDoHvrfPHA=

vue-virtual-scroller@^2.0.0-beta.3:
  version "2.0.0-beta.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/vue-virtual-scroller/download/vue-virtual-scroller-2.0.0-beta.3.tgz#ae95ce97adf511d5e18b95d3e53e932c73461f3c"
  integrity sha1-rpXOl631EdXhi5XT5T6TLHNGHzw=
  dependencies:
    mitt "^2.1.0"
    vue-observe-visibility "^2.0.0-alpha.1"
    vue-resize "^2.0.0-alpha.1"

vue@^3.2.22:
  version "3.2.40"
  resolved "http://npm.devops.xiaohongshu.com:7001/vue/download/vue-3.2.40.tgz#23f387f6f9b3a0767938db6751e4fb5900f0ee34"
  integrity sha1-I/OH9vmzoHZ5ONtnUeT7WQDw7jQ=
  dependencies:
    "@vue/compiler-dom" "3.2.40"
    "@vue/compiler-sfc" "3.2.40"
    "@vue/runtime-dom" "3.2.40"
    "@vue/server-renderer" "3.2.40"
    "@vue/shared" "3.2.40"

vuedraggable@4.1.0, vuedraggable@^4.1.0:
  version "4.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/vuedraggable/download/vuedraggable-4.1.0.tgz#edece68adb8a4d9e06accff9dfc9040e66852270"
  integrity sha1-7ezmituKTZ4GrM/538kEDmaFInA=
  dependencies:
    sortablejs "1.14.0"

vuex@^4.0.0:
  version "4.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/vuex/download/vuex-4.0.2.tgz#f896dbd5bf2a0e963f00c67e9b610de749ccacc9"
  integrity sha1-+Jbb1b8qDpY/AMZ+m2EN50nMrMk=
  dependencies:
    "@vue/devtools-api" "^6.0.0-beta.11"

vxe-table@^4.5.10:
  version "4.5.13"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/vxe-table/-/vxe-table-4.5.13.tgz#239f6854810f878abbba60d5419ea07c7395210d"
  integrity sha512-CKsyUhDYIcO4TSXoO0I2YVkKEWjQLUq24PN6MhmFmvyFRdfj80cgLZ4iEjihLieW4aRqPcLHqkw83hCAyzvO8w==
  dependencies:
    dom-zindex "^1.0.1"
    xe-utils "^3.5.13"

wcwidth@^1.0.0:
  version "1.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/wcwidth/download/wcwidth-1.0.1.tgz#f0b0dcf915bc5ff1528afadb2c0e17b532da2fe8"
  integrity sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=
  dependencies:
    defaults "^1.0.3"

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/webidl-conversions/download/webidl-conversions-3.0.1.tgz#24534275e2a7bc6be7bc86611cc16ae0a5654871"
  integrity sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=

webidl-conversions@^4.0.2:
  version "4.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/webidl-conversions/download/webidl-conversions-4.0.2.tgz#a855980b1f0b6b359ba1d5d9fb39ae941faa63ad"
  integrity sha1-qFWYCx8LazWbodXZ+zmulB+qY60=

webpack-merge@^5.8.0:
  version "5.8.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/webpack-merge/download/webpack-merge-5.8.0.tgz#2b39dbf22af87776ad744c390223731d30a68f61"
  integrity sha1-Kznb8ir4d3atdEw5AiNzHTCmj2E=
  dependencies:
    clone-deep "^4.0.1"
    wildcard "^2.0.0"

whatwg-fetch@^3.6.2:
  version "3.6.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/whatwg-fetch/download/whatwg-fetch-3.6.2.tgz#dced24f37f2624ed0281725d51d0e2e3fe677f8c"
  integrity sha1-3O0k838mJO0CgXJdUdDi4/5nf4w=

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/whatwg-url/download/whatwg-url-5.0.0.tgz#966454e8765462e37644d3626f6742ce8b70965d"
  integrity sha1-lmRU6HZUYuN2RNNib2dCzotwll0=
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

whatwg-url@^7.0.0:
  version "7.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/whatwg-url/download/whatwg-url-7.1.0.tgz#c2c492f1eca612988efd3d2266be1b9fc6170d06"
  integrity sha1-wsSS8eymEpiO/T0iZr4bn8YXDQY=
  dependencies:
    lodash.sortby "^4.7.0"
    tr46 "^1.0.1"
    webidl-conversions "^4.0.2"

which-boxed-primitive@^1.0.2:
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/which-boxed-primitive/download/which-boxed-primitive-1.0.2.tgz#13757bc89b209b049fe5d86430e21cf40a89a8e6"
  integrity sha1-E3V7yJsgmwSf5dhkMOIc9AqJqOY=
  dependencies:
    is-bigint "^1.0.1"
    is-boolean-object "^1.1.0"
    is-number-object "^1.0.4"
    is-string "^1.0.5"
    is-symbol "^1.0.3"

which-module@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/which-module/download/which-module-2.0.0.tgz#d9ef07dce77b9902b8a3a8fa4b31c3e3f7e6e87a"
  integrity sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=

which@^1.2.9, which@^1.3.1:
  version "1.3.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/which/download/which-1.3.1.tgz#a45043d54f5805316da8d62f9f50918d3da70b0a"
  integrity sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=
  dependencies:
    isexe "^2.0.0"

wide-align@^1.1.0:
  version "1.1.5"
  resolved "http://npm.devops.xiaohongshu.com:7001/wide-align/download/wide-align-1.1.5.tgz#df1d4c206854369ecf3c9a4898f1b23fbd9d15d3"
  integrity sha1-3x1MIGhUNp7PPJpImPGyP72dFdM=
  dependencies:
    string-width "^1.0.2 || 2 || 3 || 4"

wildcard@^1.1.0:
  version "1.1.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/wildcard/download/wildcard-1.1.2.tgz#a7020453084d8cd2efe70ba9d3696263de1710a5"
  integrity sha1-pwIEUwhNjNLv5wup02liY94XEKU=

wildcard@^2.0.0:
  version "2.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/wildcard/download/wildcard-2.0.0.tgz#a77d20e5200c6faaac979e4b3aadc7b3dd7f8fec"
  integrity sha1-p30g5SAMb6qsl55LOq3Hs91/j+w=

windows-release@^3.1.0:
  version "3.3.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/windows-release/download/windows-release-3.3.3.tgz#1c10027c7225743eec6b89df160d64c2e0293999"
  integrity sha1-HBACfHIldD7sa4nfFg1kwuApOZk=
  dependencies:
    execa "^1.0.0"

wmf@~1.0.1:
  version "1.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/wmf/download/wmf-1.0.2.tgz#7d19d621071a08c2bdc6b7e688a9c435298cc2da"
  integrity sha1-fRnWIQcaCMK9xrfmiKnENSmMwto=

word@~0.3.0:
  version "0.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/word/download/word-0.3.0.tgz#8542157e4f8e849f4a363a288992d47612db9961"
  integrity sha1-hUIVfk+OhJ9KNjooiZLUdhLbmWE=

wordwrap@^1.0.0:
  version "1.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/wordwrap/download/wordwrap-1.0.0.tgz#27584810891456a4171c8d0226441ade90cbcaeb"
  integrity sha1-J1hIEIkUVqQXHI0CJkQa3pDLyus=

wrap-ansi@^5.1.0:
  version "5.1.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/wrap-ansi/download/wrap-ansi-5.1.0.tgz#1fd1f67235d5b6d0fee781056001bfb694c03b09"
  integrity sha1-H9H2cjXVttD+54EFYAG/tpTAOwk=
  dependencies:
    ansi-styles "^3.2.0"
    string-width "^3.0.0"
    strip-ansi "^5.0.0"

wrappy@1:
  version "1.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/wrappy/download/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write-file-atomic@^2.0.0, write-file-atomic@^2.3.0, write-file-atomic@^2.4.2:
  version "2.4.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/write-file-atomic/download/write-file-atomic-2.4.3.tgz#1fd2e9ae1df3e75b8d8c367443c692d4ca81f481"
  integrity sha1-H9Lprh3z51uNjDZ0Q8aS1MqB9IE=
  dependencies:
    graceful-fs "^4.1.11"
    imurmurhash "^0.1.4"
    signal-exit "^3.0.2"

write-json-file@^2.2.0:
  version "2.3.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/write-json-file/download/write-json-file-2.3.0.tgz#2b64c8a33004d54b8698c76d585a77ceb61da32f"
  integrity sha1-K2TIozAE1UuGmMdtWFp3zrYdoy8=
  dependencies:
    detect-indent "^5.0.0"
    graceful-fs "^4.1.2"
    make-dir "^1.0.0"
    pify "^3.0.0"
    sort-keys "^2.0.0"
    write-file-atomic "^2.0.0"

write-json-file@^3.2.0:
  version "3.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/write-json-file/download/write-json-file-3.2.0.tgz#65bbdc9ecd8a1458e15952770ccbadfcff5fe62a"
  integrity sha1-Zbvcns2KFFjhWVJ3DMut/P9f5io=
  dependencies:
    detect-indent "^5.0.0"
    graceful-fs "^4.1.15"
    make-dir "^2.1.0"
    pify "^4.0.1"
    sort-keys "^2.0.0"
    write-file-atomic "^2.4.2"

write-pkg@^3.1.0:
  version "3.2.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/write-pkg/download/write-pkg-3.2.0.tgz#0e178fe97820d389a8928bc79535dbe68c2cff21"
  integrity sha1-DheP6Xgg04mokovHlTXb5ows/yE=
  dependencies:
    sort-keys "^2.0.0"
    write-json-file "^2.2.0"

ws@^5.2.3:
  version "5.2.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/ws/download/ws-5.2.3.tgz#05541053414921bc29c63bee14b8b0dd50b07b3d"
  integrity sha1-BVQQU0FJIbwpxjvuFLiw3VCwez0=
  dependencies:
    async-limiter "~1.0.0"

xe-utils@^3.5.13:
  version "3.5.13"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/xe-utils/-/xe-utils-3.5.13.tgz#200b49d2e3f1ab8798bccc8e34aa32502b180164"
  integrity sha512-ORT6ghCRk0mUVavMBxetcPzUPskS6NGfntzpWazJ86e+XU0uK4HwHfDhN/jKATiyInrOH5RwQo9SO/+DB8XeBw==

xlsx@^0.18.5:
  version "0.18.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/xlsx/download/xlsx-0.18.5.tgz#16711b9113c848076b8a177022799ad356eba7d0"
  integrity sha1-FnEbkRPISAdrihdwInma01brp9A=
  dependencies:
    adler-32 "~1.3.0"
    cfb "~1.2.1"
    codepage "~1.15.0"
    crc-32 "~1.2.1"
    ssf "~0.11.2"
    wmf "~1.0.1"
    word "~0.3.0"

xtend@~4.0.1:
  version "4.0.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/xtend/download/xtend-4.0.2.tgz#bb72779f5fa465186b1f438f674fa347fdb5db54"
  integrity sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=

y18n@^4.0.0:
  version "4.0.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/y18n/download/y18n-4.0.3.tgz#b5f259c82cd6e336921efd7bfd8bf560de9eeedf"
  integrity sha1-tfJZyCzW4zaSHv17/Yv1YN6e7t8=

yallist@^3.0.0, yallist@^3.0.2, yallist@^3.1.1:
  version "3.1.1"
  resolved "http://npm.devops.xiaohongshu.com:7001/yallist/download/yallist-3.1.1.tgz#dbb7daf9bfd8bac9ab45ebf602b8cbad0d5d08fd"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yallist@^4.0.0:
  version "4.0.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/yallist/download/yallist-4.0.0.tgz#9bb92790d9c0effec63be73519e11a35019a3a72"
  integrity sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=

yargs-parser@^15.0.1:
  version "15.0.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/yargs-parser/download/yargs-parser-15.0.3.tgz#316e263d5febe8b38eef61ac092b33dfcc9b1115"
  integrity sha1-MW4mPV/r6LOO72GsCSsz38ybERU=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^20.2.3:
  version "20.2.9"
  resolved "http://npm.devops.xiaohongshu.com:7001/yargs-parser/download/yargs-parser-20.2.9.tgz#2eb7dc3b0289718fc295f362753845c41a0c94ee"
  integrity sha1-LrfcOwKJcY/ClfNidThFxBoMlO4=

yargs@^14.2.2:
  version "14.2.3"
  resolved "http://npm.devops.xiaohongshu.com:7001/yargs/download/yargs-14.2.3.tgz#1a1c3edced1afb2a2fea33604bc6d1d8d688a414"
  integrity sha1-Ghw+3O0a+yov6jNgS8bR2NaIpBQ=
  dependencies:
    cliui "^5.0.0"
    decamelize "^1.2.0"
    find-up "^3.0.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^3.0.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^15.0.1"

ylru@^1.2.0:
  version "1.3.2"
  resolved "http://npm.devops.xiaohongshu.com:7001/ylru/download/ylru-1.3.2.tgz#0de48017473275a4cbdfc83a1eaf67c01af8a785"
  integrity sha1-DeSAF0cydaTL38g6Hq9nwBr4p4U=

yup@^0.27.0:
  version "0.27.0"
  resolved "http://npm.devops.xiaohongshu.com:7001/yup/download/yup-0.27.0.tgz#f8cb198c8e7dd2124beddc2457571329096b06e7"
  integrity sha1-+MsZjI590hJL7dwkV1cTKQlrBuc=
  dependencies:
    "@babel/runtime" "^7.0.0"
    fn-name "~2.0.1"
    lodash "^4.17.11"
    property-expr "^1.5.0"
    synchronous-promise "^2.0.6"
    toposort "^2.0.2"

zrender@5.2.1:
  version "5.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/zrender/download/zrender-5.2.1.tgz#5f4bbda915ba6d412b0b19dc2431beaad05417bb"
  integrity sha1-X0u9qRW6bUErCxncJDG+qtBUF7s=
  dependencies:
    tslib "2.3.0"

zrender@5.4.4:
  version "5.4.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/zrender/-/zrender-5.4.4.tgz#8854f1d95ecc82cf8912f5a11f86657cb8c9e261"
  integrity sha512-0VxCNJ7AGOMCWeHVyTrGzUgrK4asT4ml9PEkeGirAkKNYXYzoPJCLvmyfdoOXcjTHPs10OZVMfD1Rwg16AZyYw==
  dependencies:
    tslib "2.3.0"
